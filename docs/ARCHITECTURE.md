# FixMix Platform Architecture

This document provides a comprehensive overview of the FixMix platform architecture, including system design, data flow, technology stack, and component interactions.

## 🏗️ System Overview

FixMix is a multi-platform service marketplace built using a microservices-inspired architecture with the following key components:

- **Mobile Applications**: Flutter apps for users and vendors
- **Web Applications**: React admin panel and Next.js landing page
- **Backend API**: Node.js/Express server with MongoDB
- **Real-time Communication**: Socket.IO for chat functionality
- **File Storage**: AWS S3 for media and documents
- **Push Notifications**: Firebase Cloud Messaging

## 🎯 High-Level Architecture

```mermaid
graph TB
    subgraph "Client Applications"
        UA[User Mobile App<br/>Flutter]
        VA[Vendor Mobile App<br/>Flutter]
        AP[Admin Panel<br/>React]
        LP[Landing Page<br/>Next.js]
    end
    
    subgraph "API Gateway & Load Balancer"
        LB[Load Balancer]
        API[API Gateway]
    end
    
    subgraph "Backend Services"
        BE[Backend API<br/>Node.js/Express]
        WS[WebSocket Server<br/>Socket.IO]
    end
    
    subgraph "Data Layer"
        DB[(MongoDB<br/>Database)]
        CACHE[(Redis<br/>Cache)]
    end
    
    subgraph "External Services"
        S3[AWS S3<br/>File Storage]
        FCM[Firebase<br/>Push Notifications]
        MAPS[Google Maps<br/>Location Services]
        PAY[Payment<br/>Gateway]
    end
    
    UA --> LB
    VA --> LB
    AP --> LB
    LP --> LB
    
    LB --> API
    API --> BE
    API --> WS
    
    BE --> DB
    BE --> CACHE
    BE --> S3
    BE --> FCM
    BE --> MAPS
    BE --> PAY
    
    WS --> DB
```

## 📱 Application Architecture

### Mobile Applications (Flutter)

Both user and vendor mobile apps follow clean architecture principles:

```mermaid
graph TB
    subgraph "Presentation Layer"
        UI[UI Widgets]
        CTRL[GetX Controllers]
        BIND[Bindings]
    end
    
    subgraph "Domain Layer"
        UC[Use Cases]
        ENT[Entities]
        REPO[Repository Interfaces]
    end
    
    subgraph "Data Layer"
        REPOIMPL[Repository Implementation]
        DS[Data Sources]
        API[API Client]
        LOCAL[Local Storage]
    end
    
    UI --> CTRL
    CTRL --> UC
    UC --> REPO
    REPO --> REPOIMPL
    REPOIMPL --> DS
    DS --> API
    DS --> LOCAL
    
    BIND --> CTRL
    BIND --> UC
    BIND --> REPOIMPL
```

### Web Applications

#### Admin Panel (React)
```mermaid
graph TB
    subgraph "React Admin Panel"
        COMP[React Components]
        HOOKS[Custom Hooks]
        CTX[Context Providers]
        SERV[API Services]
    end
    
    COMP --> HOOKS
    HOOKS --> CTX
    CTX --> SERV
    SERV --> API[Backend API]
```

#### Landing Page (Next.js)
```mermaid
graph TB
    subgraph "Next.js Landing Page"
        PAGES[Pages]
        COMP[Components]
        STYLES[Styles]
        UTILS[Utils]
    end
    
    PAGES --> COMP
    COMP --> STYLES
    COMP --> UTILS
```

## 🔄 Data Flow Architecture

### User Service Booking Flow

```mermaid
sequenceDiagram
    participant U as User App
    participant API as Backend API
    participant DB as MongoDB
    participant V as Vendor App
    participant FCM as Firebase
    
    U->>API: Browse Services
    API->>DB: Query Categories/Services
    DB-->>API: Return Services
    API-->>U: Service List
    
    U->>API: Create Service Request
    API->>DB: Save Request
    API->>FCM: Notify Nearby Vendors
    FCM-->>V: Push Notification
    
    V->>API: Accept Request
    API->>DB: Update Request Status
    API->>FCM: Notify User
    FCM-->>U: Booking Confirmed
    
    U->>API: Start Chat
    API->>WS: Initialize Chat Room
    WS-->>U: Chat Connected
    WS-->>V: Chat Connected
```

### Real-time Chat Flow

```mermaid
sequenceDiagram
    participant U as User
    participant WS as WebSocket Server
    participant DB as MongoDB
    participant V as Vendor
    
    U->>WS: Connect to Chat Room
    V->>WS: Connect to Chat Room
    
    U->>WS: Send Message
    WS->>DB: Save Message
    WS-->>V: Broadcast Message
    
    V->>WS: Send Reply
    WS->>DB: Save Reply
    WS-->>U: Broadcast Reply
    
    U->>WS: Mark as Read
    WS->>DB: Update Message Status
```

## 🗄️ Database Architecture

### MongoDB Collections Structure

```mermaid
erDiagram
    User ||--o{ Order : places
    User ||--o{ Query : creates
    User ||--o{ Location : has
    User ||--o{ Notification : receives
    
    Vendor ||--o{ Order : fulfills
    Vendor ||--o{ Offer : creates
    Vendor ||--o{ Service : provides
    Vendor ||--o{ Location : operates_in
    
    Order ||--|| Payment : has
    Order ||--o{ Chat : generates
    
    Category ||--o{ SubCategory : contains
    SubCategory ||--o{ Service : includes
    
    Query ||--o{ Offer : receives
    Offer ||--o{ Chat : enables
    
    Admin ||--o{ Notification : sends
```

### Key Collections

#### Users Collection
```javascript
{
  _id: ObjectId,
  name: String,
  email: String,
  phone: String,
  profilePhoto: String,
  location: [ObjectId],
  wallet: ObjectId,
  isActive: Boolean,
  isVerified: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

#### Vendors Collection
```javascript
{
  _id: ObjectId,
  name: String,
  email: String,
  phone: String,
  profilePhoto: String,
  services: [{
    name: String,
    price: Number,
    category: ObjectId
  }],
  location: [ObjectId],
  rating: Number,
  totalEarnings: Number,
  isVerified: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

#### Orders Collection
```javascript
{
  _id: ObjectId,
  user: ObjectId,
  vendor: ObjectId,
  service: String,
  amount: Number,
  status: String,
  location: ObjectId,
  scheduledDate: Date,
  completedDate: Date,
  rating: Number,
  review: String,
  createdAt: Date,
  updatedAt: Date
}
```

## 🔐 Security Architecture

### Authentication & Authorization Flow

```mermaid
graph TB
    subgraph "Client"
        APP[Mobile/Web App]
    end
    
    subgraph "Authentication"
        AUTH[Auth Middleware]
        JWT[JWT Verification]
        ROLE[Role Check]
    end
    
    subgraph "API"
        ROUTES[Protected Routes]
        CTRL[Controllers]
    end
    
    APP --> AUTH
    AUTH --> JWT
    JWT --> ROLE
    ROLE --> ROUTES
    ROUTES --> CTRL
```

### Security Layers

1. **Transport Security**: HTTPS/TLS encryption
2. **Authentication**: JWT token-based authentication
3. **Authorization**: Role-based access control (RBAC)
4. **Input Validation**: Request data sanitization
5. **Rate Limiting**: API request throttling
6. **CORS**: Cross-origin resource sharing control

## 🚀 Deployment Architecture

### Production Environment

```mermaid
graph TB
    subgraph "CDN & Load Balancer"
        CDN[CloudFlare CDN]
        LB[Load Balancer]
    end
    
    subgraph "Application Servers"
        API1[API Server 1]
        API2[API Server 2]
        WS1[WebSocket Server 1]
        WS2[WebSocket Server 2]
    end
    
    subgraph "Database Cluster"
        MONGO_PRIMARY[(MongoDB Primary)]
        MONGO_SECONDARY[(MongoDB Secondary)]
        REDIS[(Redis Cluster)]
    end
    
    subgraph "File Storage"
        S3[AWS S3 Buckets]
    end
    
    subgraph "Monitoring"
        LOGS[Centralized Logging]
        METRICS[Metrics Collection]
        ALERTS[Alert System]
    end
    
    CDN --> LB
    LB --> API1
    LB --> API2
    LB --> WS1
    LB --> WS2
    
    API1 --> MONGO_PRIMARY
    API2 --> MONGO_PRIMARY
    MONGO_PRIMARY --> MONGO_SECONDARY
    
    API1 --> REDIS
    API2 --> REDIS
    
    API1 --> S3
    API2 --> S3
    
    API1 --> LOGS
    API2 --> LOGS
    WS1 --> LOGS
    WS2 --> LOGS
    
    LOGS --> METRICS
    METRICS --> ALERTS
```

### Container Architecture

```mermaid
graph TB
    subgraph "Docker Containers"
        subgraph "API Container"
            NODE[Node.js App]
            PM2[PM2 Process Manager]
        end
        
        subgraph "Database Container"
            MONGO[MongoDB]
            MONGO_DATA[Data Volume]
        end
        
        subgraph "Cache Container"
            REDIS_CONT[Redis]
            REDIS_DATA[Data Volume]
        end
        
        subgraph "Reverse Proxy"
            NGINX[Nginx]
        end
    end
    
    NGINX --> NODE
    NODE --> MONGO
    NODE --> REDIS_CONT
    MONGO --> MONGO_DATA
    REDIS_CONT --> REDIS_DATA
```

## 📊 Performance Architecture

### Caching Strategy

```mermaid
graph TB
    subgraph "Client Side"
        BROWSER[Browser Cache]
        APP_CACHE[App Cache]
    end
    
    subgraph "CDN Layer"
        CDN_CACHE[CDN Cache]
    end
    
    subgraph "Application Layer"
        REDIS_CACHE[Redis Cache]
        MEMORY[In-Memory Cache]
    end
    
    subgraph "Database Layer"
        DB_CACHE[Database Query Cache]
        DB[(MongoDB)]
    end
    
    BROWSER --> CDN_CACHE
    APP_CACHE --> CDN_CACHE
    CDN_CACHE --> REDIS_CACHE
    REDIS_CACHE --> MEMORY
    MEMORY --> DB_CACHE
    DB_CACHE --> DB
```

### Scaling Strategy

1. **Horizontal Scaling**: Multiple API server instances
2. **Database Scaling**: MongoDB replica sets and sharding
3. **Caching**: Redis for session and data caching
4. **CDN**: Static asset distribution
5. **Load Balancing**: Request distribution across servers

## 🔄 API Architecture

### RESTful API Design

```mermaid
graph TB
    subgraph "API Endpoints"
        AUTH[/api/auth/*]
        USERS[/api/users/*]
        VENDORS[/api/vendors/*]
        ORDERS[/api/orders/*]
        CHAT[/api/chat/*]
        ADMIN[/api/admin/*]
    end
    
    subgraph "Middleware Stack"
        CORS[CORS Middleware]
        AUTH_MW[Auth Middleware]
        RATE[Rate Limiting]
        VALID[Validation]
        LOG[Logging]
    end
    
    subgraph "Controllers"
        AUTH_CTRL[Auth Controller]
        USER_CTRL[User Controller]
        VENDOR_CTRL[Vendor Controller]
        ORDER_CTRL[Order Controller]
        CHAT_CTRL[Chat Controller]
        ADMIN_CTRL[Admin Controller]
    end
    
    AUTH --> CORS
    USERS --> CORS
    VENDORS --> CORS
    ORDERS --> CORS
    CHAT --> CORS
    ADMIN --> CORS
    
    CORS --> AUTH_MW
    AUTH_MW --> RATE
    RATE --> VALID
    VALID --> LOG
    
    LOG --> AUTH_CTRL
    LOG --> USER_CTRL
    LOG --> VENDOR_CTRL
    LOG --> ORDER_CTRL
    LOG --> CHAT_CTRL
    LOG --> ADMIN_CTRL
```

## 🔗 Integration Architecture

### External Service Integrations

```mermaid
graph TB
    subgraph "FixMix Backend"
        API[Backend API]
    end
    
    subgraph "Payment Services"
        STRIPE[Stripe]
        PAYPAL[PayPal]
    end
    
    subgraph "Communication Services"
        FCM[Firebase Cloud Messaging]
        SMS[SMS Gateway]
        EMAIL[Email Service]
    end
    
    subgraph "Location Services"
        GMAPS[Google Maps API]
        GEOCODING[Geocoding Service]
    end
    
    subgraph "Storage Services"
        S3[AWS S3]
        CLOUDINARY[Cloudinary]
    end
    
    API --> STRIPE
    API --> PAYPAL
    API --> FCM
    API --> SMS
    API --> EMAIL
    API --> GMAPS
    API --> GEOCODING
    API --> S3
    API --> CLOUDINARY
```

This architecture documentation provides a comprehensive view of the FixMix platform's technical design, data flow, and system interactions. Each component is designed to be scalable, maintainable, and secure while providing optimal performance for users, vendors, and administrators.
