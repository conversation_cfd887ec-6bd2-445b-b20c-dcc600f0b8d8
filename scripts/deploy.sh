#!/bin/bash

# FixMix Landing Page Deployment Script
echo "🚀 Starting FixMix Landing Page Deployment..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Install dependencies
print_status "Installing dependencies..."
npm ci

# Run tests
print_status "Running tests..."
npm test -- --watchAll=false || {
    echo "Warning: Some tests failed. Continuing..."
}

# Build the application
print_status "Building the application..."
npm run build

print_success "🎉 Deployment preparation completed!"
print_status "Ready for production deployment."

exit 0
