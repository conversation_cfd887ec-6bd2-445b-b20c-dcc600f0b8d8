# FixMix Shared Packages

This directory contains shared packages used across the FixMix monorepo. These packages provide consistent tooling, components, and configurations for all applications in the workspace.

## 📦 Package Overview

### `@repo/ui`
Shared React component library used by web applications.

### `@repo/eslint-config`
ESLint configurations for consistent code quality across all projects.

### `@repo/typescript-config`
TypeScript configurations for type safety and consistency.

## 🎨 UI Component Library (`@repo/ui`)

### Overview
The UI package provides reusable React components that maintain design consistency across the FixMix web applications (Admin Panel and Landing Page).

### Installation & Usage

#### In Web Applications
```json
{
  "dependencies": {
    "@repo/ui": "*"
  }
}
```

#### Import Components
```javascript
import { Button, Card, Code } from "@repo/ui";

function MyComponent() {
  return (
    <Card>
      <Button variant="primary">Click me</Button>
      <Code>console.log('Hello FixMix');</Code>
    </Card>
  );
}
```

### Available Components

#### Button Component
```typescript
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

// Usage
<Button variant="primary" size="lg" onClick={handleClick}>
  Submit
</Button>
```

#### Card Component
```typescript
interface CardProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
}

// Usage
<Card title="User Profile">
  <p>User information content</p>
</Card>
```

#### Code Component
```typescript
interface CodeProps {
  children: string;
  language?: string;
  className?: string;
}

// Usage
<Code language="javascript">
  const message = "Hello World";
</Code>
```

### Component Development

#### Adding New Components
```bash
cd packages/ui
npm run generate:component
```

#### Component Structure
```typescript
// src/new-component.tsx
import React from 'react';

interface NewComponentProps {
  // Define props interface
}

export const NewComponent: React.FC<NewComponentProps> = ({ 
  // destructure props 
}) => {
  return (
    <div className="new-component">
      {/* Component JSX */}
    </div>
  );
};

export default NewComponent;
```

#### Exporting Components
```typescript
// src/index.ts
export { Button } from './button';
export { Card } from './card';
export { Code } from './code';
export { NewComponent } from './new-component';
```

### Styling Guidelines

#### CSS Classes
- Use semantic class names
- Follow BEM methodology when applicable
- Ensure responsive design
- Maintain accessibility standards

#### Example Component Styles
```css
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.button--primary {
  background-color: #3b82f6;
  color: white;
}

.button--primary:hover {
  background-color: #2563eb;
}

.button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
```

## 🔧 ESLint Configuration (`@repo/eslint-config`)

### Overview
Provides consistent ESLint configurations for different types of applications in the monorepo.

### Available Configurations

#### Base Configuration (`base.js`)
```javascript
// For general JavaScript/TypeScript projects
module.exports = {
  extends: ["@repo/eslint-config/base"]
};
```

#### Next.js Configuration (`next.js`)
```javascript
// For Next.js applications
module.exports = {
  extends: ["@repo/eslint-config/next-js"]
};
```

#### React Internal Configuration (`react-internal.js`)
```javascript
// For React component libraries
module.exports = {
  extends: ["@repo/eslint-config/react-internal"]
};
```

### Usage in Applications

#### Admin Panel (React)
```javascript
// apps/Fixmix-Admin/.eslintrc.js
module.exports = {
  extends: ["@repo/eslint-config/react-internal"]
};
```

#### Landing Page (Next.js)
```javascript
// apps/fixmix-landing-page/.eslintrc.js
module.exports = {
  extends: ["@repo/eslint-config/next-js"]
};
```

#### UI Package
```javascript
// packages/ui/.eslintrc.js
module.exports = {
  extends: ["@repo/eslint-config/react-internal"]
};
```

### Configuration Details

#### Base Rules
- TypeScript support
- Import/export rules
- Code formatting with Prettier
- Security best practices
- Performance optimizations

#### React-Specific Rules
- React hooks rules
- JSX accessibility
- Component naming conventions
- Props validation

#### Next.js-Specific Rules
- Next.js best practices
- Image optimization
- Link usage
- Performance optimizations

### Custom Rules

#### Adding Project-Specific Rules
```javascript
// .eslintrc.js
module.exports = {
  extends: ["@repo/eslint-config/base"],
  rules: {
    // Override or add custom rules
    "no-console": "warn",
    "prefer-const": "error"
  }
};
```

## 📝 TypeScript Configuration (`@repo/typescript-config`)

### Overview
Provides TypeScript configurations optimized for different types of applications.

### Available Configurations

#### Base Configuration (`base.json`)
```json
{
  "extends": "@repo/typescript-config/base.json"
}
```

#### Next.js Configuration (`nextjs.json`)
```json
{
  "extends": "@repo/typescript-config/nextjs.json"
}
```

#### React Library Configuration (`react-library.json`)
```json
{
  "extends": "@repo/typescript-config/react-library.json"
}
```

### Usage in Applications

#### Admin Panel
```json
// apps/Fixmix-Admin/tsconfig.json
{
  "extends": "@repo/typescript-config/base.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "build"]
}
```

#### Landing Page
```json
// apps/fixmix-landing-page/tsconfig.json
{
  "extends": "@repo/typescript-config/nextjs.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"],
  "exclude": ["node_modules"]
}
```

#### UI Package
```json
// packages/ui/tsconfig.json
{
  "extends": "@repo/typescript-config/react-library.json",
  "compilerOptions": {
    "outDir": "dist",
    "rootDir": "src"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

### Configuration Features

#### Strict Type Checking
- `strict: true`
- `noImplicitAny: true`
- `strictNullChecks: true`
- `strictFunctionTypes: true`

#### Module Resolution
- `moduleResolution: "node"`
- `esModuleInterop: true`
- `allowSyntheticDefaultImports: true`

#### Build Optimizations
- `skipLibCheck: true`
- `forceConsistentCasingInFileNames: true`
- `resolveJsonModule: true`

## 🚀 Development Workflow

### Adding New Shared Packages

#### 1. Create Package Directory
```bash
mkdir packages/new-package
cd packages/new-package
```

#### 2. Initialize Package
```json
{
  "name": "@repo/new-package",
  "version": "0.0.0",
  "private": true,
  "main": "./src/index.ts",
  "types": "./src/index.ts"
}
```

#### 3. Add to Workspace
```json
// Root package.json
{
  "workspaces": [
    "apps/*",
    "packages/*"
  ]
}
```

### Package Development Commands

#### UI Package
```bash
# Lint components
npm run lint

# Type check
npm run check-types

# Generate new component
npm run generate:component
```

#### ESLint Config
```bash
# Test configuration
npx eslint . --config base.js

# Check for conflicts
npx eslint-config-prettier base.js
```

### Testing Shared Packages

#### Unit Testing
```javascript
// packages/ui/__tests__/button.test.tsx
import { render, screen } from '@testing-library/react';
import { Button } from '../src/button';

describe('Button Component', () => {
  test('renders button with text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });
  
  test('applies variant classes', () => {
    render(<Button variant="primary">Primary</Button>);
    expect(screen.getByRole('button')).toHaveClass('button--primary');
  });
});
```

#### Integration Testing
```javascript
// Test package usage in applications
import { Button } from '@repo/ui';

// Verify component works in application context
```

## 🔄 Versioning & Publishing

### Internal Package Versioning
Since packages are private and used within the monorepo:

```json
{
  "name": "@repo/ui",
  "version": "0.0.0",
  "private": true
}
```

### Dependency Management
```json
// Application package.json
{
  "dependencies": {
    "@repo/ui": "*",
    "@repo/eslint-config": "*",
    "@repo/typescript-config": "*"
  }
}
```

## 🤝 Contributing to Shared Packages

### Guidelines
1. **Consistency**: Maintain consistent APIs across components
2. **Documentation**: Document all public interfaces
3. **Testing**: Add tests for new components and configurations
4. **Backward Compatibility**: Avoid breaking changes when possible
5. **Performance**: Optimize for bundle size and runtime performance

### Code Review Process
1. Create feature branch for package changes
2. Update relevant documentation
3. Add or update tests
4. Test in consuming applications
5. Submit pull request with detailed description

## 📞 Support

For issues with shared packages:
- **UI Components**: Check component props and styling
- **ESLint Config**: Verify rule conflicts and overrides
- **TypeScript Config**: Check compilation errors and type issues
- **General**: Review package.json dependencies and exports

## 🔗 Related Documentation

- [Main Project README](../README.md)
- [Turborepo Documentation](https://turborepo.com/docs)
- [React Component Best Practices](https://react.dev/learn)
- [ESLint Configuration Guide](https://eslint.org/docs/user-guide/configuring)
- [TypeScript Configuration Reference](https://www.typescriptlang.org/tsconfig)
