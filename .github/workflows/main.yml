name: Deploy to <PERSON>inger VPS

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Execute remote SSH commands
        uses: appleboy/ssh-action@main
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USER }}
          key: ${{ secrets.VPS_SSH_KEY }}
          script: |
            cd /var/www/fixmix_backend
            git pull origin main
            npm install
            pm2 restart fixmix_backend || pm2 start index.js --name fixmix_backend
