name: Deploy FixMix to Hostinger VPS

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Execute remote SSH commands
        uses: appleboy/ssh-action@v0.1.0
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USER }}
          key: ${{ secrets.VPS_SSH_KEY }}
          port: 22
          script: |
            cd /var/www/backend-fixmix
            git pull origin main
            npm install
            pm2 restart backend-fixmix || pm2 start index.js --name backend-fixmix
          debug: true
