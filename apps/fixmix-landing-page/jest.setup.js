import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      locale: 'en',
      locales: ['en', 'ar'],
      defaultLocale: 'en',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn().mockResolvedValue(undefined),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
    }
  },
}))

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} />
  },
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {
    return null
  }
  disconnect() {
    return null
  }
  unobserve() {
    return null
  }
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {
    return null
  }
  disconnect() {
    return null
  }
  unobserve() {
    return null
  }
}

// Mock analytics
jest.mock('@/utils/analytics', () => ({
  trackCTAClick: jest.fn(),
  initGA: jest.fn(),
  reportWebVitals: jest.fn(),
}))

// Mock performance utilities
jest.mock('@/utils/performance', () => ({
  initPerformanceMonitoring: jest.fn(),
}))

// Mock performance optimizations
jest.mock('@/utils/performanceOptimizations', () => ({
  initPerformanceOptimizations: jest.fn(),
}))

// Mock SCSS modules
jest.mock('@/styles/Home.module.scss', () => ({}))
jest.mock('@/styles/Hero.module.scss', () => ({}))

// Mock content
jest.mock('@/content/index.js', () => ({
  content: {
    hero: {
      badge: '🎉 متوفر الآن على iOS و Android',
      title: 'Your One-Stop Solution for Getting Work Done',
      subtitle: 'FixMix is an on-demand service marketplace that lets users book expert vendors for various tasks like plumbing, electrical work, painting, carpentry, and more.',
    },
    faq: {
      title: "FAQ's",
      subtitle: 'Find answers to commonly asked questions about FixMix',
      items: {
        booking: {
          question: 'How do I book a service on FixMix?',
          answer: 'Simply browse categories, chat with a vendor, and confirm your booking.'
        }
      }
    }
  }
}))

// Mock components that might cause issues
jest.mock('@/components/OptimizedImage', () => {
  const MockedOptimizedImage = ({ src, alt, ...props }) => (
    // eslint-disable-next-line @next/next/no-img-element
    <img src={src} alt={alt} {...props} />
  )

  return {
    __esModule: true,
    default: MockedOptimizedImage,
    HeroImage: MockedOptimizedImage,
    PhoneScreenshot: MockedOptimizedImage,
  }
})

jest.mock('@/components/AnimatedSection', () => {
  return function MockedAnimatedSection({ children }) {
    return <div data-testid="animated-section">{children}</div>
  }
})

jest.mock('@/components/AppLinks', () => {
  return function MockedAppLinks() {
    return <div data-testid="app-links">App Links</div>
  }
})
