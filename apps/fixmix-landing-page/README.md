# FixMix Landing Page

The FixMix Landing Page is a modern, responsive Next.js website that serves as the marketing front for the FixMix platform. It showcases the platform's features, benefits, and value proposition to potential users and vendors.

## 🚀 Overview

The landing page serves multiple purposes:
- **Marketing Hub**: Showcase FixMix platform features and benefits
- **User Acquisition**: Convert visitors into platform users
- **SEO Optimization**: Improve search engine visibility
- **Brand Presence**: Establish FixMix brand identity
- **Information Portal**: Provide platform information and support

## 🛠️ Technology Stack

- **Framework**: Next.js 14
- **Styling**: Bootstrap 5 + Sass + Tailwind CSS
- **JavaScript**: ES6+ with React 18
- **Icons**: React Icons
- **Analytics**: React GA4 (Google Analytics)
- **Build Tool**: Next.js built-in bundler
- **Deployment**: Vercel/Netlify ready

## 📁 Project Structure

```
apps/fixmix-landing-page/
├── public/
│   ├── images/
│   │   ├── icons/          # Service and feature icons
│   │   ├── hero/           # Hero section images
│   │   ├── screenshots/    # App screenshots
│   │   └── app-store/      # App store badges
│   ├── favicon.ico
│   ├── manifest.json       # PWA manifest
│   └── robots.txt          # SEO robots file
├── src/
│   ├── components/         # Reusable UI components
│   │   ├── Hero.js         # Hero section component
│   │   ├── UseCase.js      # Feature showcase component
│   │   ├── Reviews.js      # Customer reviews section
│   │   ├── FAQ.js          # FAQ section component
│   │   ├── FaqItem.js      # Individual FAQ item
│   │   ├── WhatMakesFixMixStandOut.js # Features & stats
│   │   ├── DownloadSection.js # App download section
│   │   ├── AnimatedSection.js # Animation wrapper
│   │   ├── LanguageSwitcher.js # Language toggle
│   │   ├── Navigation.js   # Main navigation
│   │   ├── StickyNavigation.js # Sticky nav bar
│   │   └── ...
│   ├── pages/             # Next.js pages
│   │   ├── index.js       # English home page
│   │   ├── ar/            # Arabic pages
│   │   │   ├── index.js   # Arabic home page
│   │   │   ├── terms.js   # Arabic terms page
│   │   │   └── privacy.js # Arabic privacy page
│   │   ├── privacy/       # Privacy pages
│   │   ├── terms/         # Terms pages
│   │   ├── _app.js        # App wrapper
│   │   └── _document.js   # Document wrapper
│   ├── styles/            # Styling files
│   │   ├── globals.scss   # Global styles
│   │   ├── i18n.scss      # RTL/LTR styles
│   │   ├── faq-enhancements.scss # FAQ specific styles
│   │   ├── accessibility.scss # Accessibility styles
│   │   └── ...
│   ├── locales/           # Internationalization
│   │   ├── en.json        # English translations
│   │   └── ar.json        # Arabic translations
│   ├── hooks/             # Custom React hooks
│   │   ├── useTranslation.js # Translation hook
│   │   └── ...
│   ├── utils/             # Utility functions
│   │   ├── analytics.js   # Google Analytics
│   │   ├── seoConfig.js   # SEO configuration
│   │   └── ...
│   └── content/           # Static content
├── __tests__/             # Test files
├── next.config.js         # Next.js configuration
├── tailwind.config.js     # Tailwind configuration
├── jest.config.js         # Jest test configuration
├── tsconfig.json          # TypeScript configuration
└── package.json           # Dependencies
```

## 🔧 Installation & Setup

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn

### 1. Install Dependencies
```bash
cd apps/fixmix-landing-page
npm install
```

### 2. Environment Configuration
Create a `.env.local` file for environment variables:

```env
NEXT_PUBLIC_GA_TRACKING_ID=your_google_analytics_id
NEXT_PUBLIC_API_BASE_URL=https://api.fixmix.co
NEXT_PUBLIC_ENVIRONMENT=development
```

### 3. Start Development Server
```bash
npm run dev
```

The application will start on `http://localhost:3000`.

### 4. Build for Production
```bash
npm run build
```

### 5. Start Production Server
```bash
npm start
```

## 🎨 Design & Layout

### Hero Section
The main hero section showcases the platform's value proposition:

```javascript
const Hero = ({ title, subtitle }) => {
  return (
    <section className="hero-section">
      <div className="container">
        <div className="row align-items-center">
          <div className="col-lg-6">
            <h1 className="hero-title">{title}</h1>
            <p className="hero-subtitle">{subtitle}</p>
            <div className="hero-actions">
              <button className="btn btn-primary">Get Started</button>
              <button className="btn btn-outline">Learn More</button>
            </div>
          </div>
          <div className="col-lg-6">
            <div className="hero-image">
              <img src="/images/hero/app-mockup.png" alt="FixMix App" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
```

### Feature Showcase
Highlight key platform features and benefits:

```javascript
const UseCase = ({ imagePath, title, description }) => {
  return (
    <div className="col-md-4 mb-4">
      <div className="use-case-card">
        <div className="use-case-icon">
          <img src={imagePath} alt={title} />
        </div>
        <h3 className="use-case-title">{title}</h3>
        <p className="use-case-description">{description}</p>
      </div>
    </div>
  );
};

// Usage in main page
const features = [
  {
    imagePath: "/images/icons/services.svg",
    title: "Wide Range of Services",
    description: "From plumbing to electrical work, find experts for any task"
  },
  {
    imagePath: "/images/icons/verified.svg",
    title: "Verified Professionals",
    description: "All vendors are background-checked and verified"
  },
  {
    imagePath: "/images/icons/support.svg",
    title: "24/7 Support",
    description: "Round-the-clock customer support for peace of mind"
  }
];
```

### Responsive Design
The landing page is fully responsive across all devices:

```scss
// Responsive breakpoints
$breakpoints: (
  'mobile': 576px,
  'tablet': 768px,
  'desktop': 992px,
  'large': 1200px
);

// Hero section responsive styles
.hero-section {
  padding: 100px 0;

  @media (max-width: 768px) {
    padding: 60px 0;
    text-align: center;
  }

  .hero-title {
    font-size: 3.5rem;
    font-weight: 700;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }

  .hero-subtitle {
    font-size: 1.25rem;
    margin: 20px 0 30px;

    @media (max-width: 768px) {
      font-size: 1.1rem;
    }
  }
}
```

## 🌍 Internationalization & RTL Support

The landing page supports multiple languages with full RTL (Right-to-Left) layout support for Arabic:

### Language Support
- **English (en)**: Default language with LTR layout
- **Arabic (ar)**: Full RTL support with Arabic translations

### Translation System
```javascript
// Using the translation hook
import { useTranslation } from '@/hooks/useTranslation';

const MyComponent = () => {
  const { t, isRTL, changeLanguage } = useTranslation();

  return (
    <div dir={isRTL ? 'rtl' : 'ltr'}>
      <h1>{t('hero.title', 'Default English Title')}</h1>
      <button onClick={() => changeLanguage('ar')}>
        Switch to Arabic
      </button>
    </div>
  );
};
```

### RTL Layout Features
- Automatic text direction switching
- Mirrored layouts for RTL languages
- RTL-specific CSS adjustments
- Proper Arabic font rendering
- Accessibility support for RTL

### Adding New Languages
1. Create a new translation file in `src/locales/[locale].json`
2. Add the locale to the translation system
3. Update the language switcher component
4. Test RTL layout if applicable

## 🌟 Key Features

### SEO Optimization
Built-in SEO features for better search engine visibility:

```javascript
// pages/_app.js
import Head from 'next/head';

export default function App({ Component, pageProps }) {
  return (
    <>
      <Head>
        <title>FixMix - Your One-Stop Solution for Getting Work Done</title>
        <meta name="description" content="FixMix is an on-demand service marketplace connecting users with skilled vendors for home and professional services." />
        <meta name="keywords" content="home services, plumbing, electrical, carpentry, on-demand services" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta property="og:title" content="FixMix - On-Demand Service Marketplace" />
        <meta property="og:description" content="Connect with skilled professionals for all your service needs" />
        <meta property="og:image" content="/images/og-image.png" />
        <meta property="og:url" content="https://fixmix.co" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://fixmix.co" />
      </Head>
      <Component {...pageProps} />
    </>
  );
}
```

### Google Analytics Integration
Track visitor behavior and conversions:

```javascript
// utils/analytics.js
import { gtag } from 'ga-gtag';

export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_TRACKING_ID;

// Initialize Google Analytics
export const initGA = () => {
  if (typeof window !== 'undefined' && GA_TRACKING_ID) {
    gtag('config', GA_TRACKING_ID, {
      page_title: document.title,
      page_location: window.location.href,
    });
  }
};

// Track page views
export const trackPageView = (url) => {
  if (GA_TRACKING_ID) {
    gtag('config', GA_TRACKING_ID, {
      page_path: url,
    });
  }
};

// Track events
export const trackEvent = (action, category, label, value) => {
  if (GA_TRACKING_ID) {
    gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};
```

### Performance Optimization
Optimized for fast loading and great user experience:

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['api.fixmix.co'],
    formats: ['image/webp', 'image/avif'],
  },
  compress: true,
  poweredByHeader: false,
  generateEtags: false,

  // Performance optimizations
  experimental: {
    optimizeCss: true,
    optimizeImages: true,
  },

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

## 📱 Mobile Responsiveness

### Mobile-First Design
The landing page follows mobile-first design principles:

```scss
// Mobile-first responsive design
.container {
  padding: 0 15px;

  @media (min-width: 576px) {
    max-width: 540px;
    margin: 0 auto;
  }

  @media (min-width: 768px) {
    max-width: 720px;
  }

  @media (min-width: 992px) {
    max-width: 960px;
  }

  @media (min-width: 1200px) {
    max-width: 1140px;
  }
}

// Navigation responsive behavior
.navbar {
  @media (max-width: 991px) {
    .navbar-nav {
      text-align: center;
      padding: 20px 0;
    }

    .navbar-toggler {
      border: none;
      padding: 4px 8px;
    }
  }
}
```

### Touch-Friendly Interface
Optimized for mobile touch interactions:

```scss
// Touch-friendly buttons and links
.btn {
  min-height: 44px;
  padding: 12px 24px;
  font-size: 16px;

  @media (max-width: 768px) {
    width: 100%;
    margin-bottom: 10px;
  }
}

// Touch targets
.nav-link, .btn, .card {
  @media (max-width: 768px) {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
```

## 🎯 Content Management

### Dynamic Content Structure
Easily manageable content structure:

```javascript
// content/siteContent.js
export const siteContent = {
  hero: {
    title: "Your One-Stop Solution for Getting Work Done",
    subtitle: "FixMix is an on-demand service marketplace that lets users book expert vendors for various tasks like plumbing, electrical work, painting, carpentry, and more.",
    ctaText: "Get Started Today",
    ctaSecondary: "Learn More"
  },

  features: [
    {
      icon: "/images/icons/services.svg",
      title: "Wide Range of Services",
      description: "From plumbing to electrical work, find experts for any task you need completed."
    },
    {
      icon: "/images/icons/verified.svg",
      title: "Verified Professionals",
      description: "All our vendors are thoroughly vetted, background-checked, and verified for your safety."
    },
    {
      icon: "/images/icons/support.svg",
      title: "24/7 Customer Support",
      description: "Round-the-clock support to help you with any questions or concerns."
    }
  ],

  testimonials: [
    {
      name: "Sarah Johnson",
      role: "Homeowner",
      content: "FixMix made it so easy to find a reliable plumber. The service was excellent!",
      rating: 5
    }
  ],

  footer: {
    description: "FixMix connects you with skilled professionals for all your service needs.",
    links: {
      company: ["About Us", "Careers", "Press"],
      support: ["Help Center", "Contact Us", "FAQ"],
      legal: ["Privacy Policy", "Terms of Service", "Cookie Policy"]
    }
  }
};
```

### Content Components
Reusable content components for easy updates:

```javascript
// components/ContentSection.js
const ContentSection = ({
  title,
  subtitle,
  children,
  className = "",
  background = "white"
}) => {
  return (
    <section className={`content-section bg-${background} ${className}`}>
      <div className="container">
        {title && (
          <div className="section-header text-center mb-5">
            <h2 className="section-title">{title}</h2>
            {subtitle && (
              <p className="section-subtitle">{subtitle}</p>
            )}
          </div>
        )}
        <div className="section-content">
          {children}
        </div>
      </div>
    </section>
  );
};

// Usage
<ContentSection
  title="Why Choose FixMix?"
  subtitle="Discover the benefits of our platform"
  background="light"
>
  <FeatureGrid features={siteContent.features} />
</ContentSection>
```

## 🚀 Deployment

### Vercel Deployment (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to Vercel
vercel

# Production deployment
vercel --prod
```

### Netlify Deployment
```bash
# Build the project
npm run build

# Deploy to Netlify
netlify deploy --prod --dir=out
```

### Docker Deployment
```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app

COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
```

### Static Export
For static hosting services:

```javascript
// next.config.js
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
};
```

```bash
# Generate static export
npm run build
```

## 📊 Analytics & Tracking

### Conversion Tracking
Track important user actions:

```javascript
// components/CTAButton.js
import { trackEvent } from '../utils/analytics';

const CTAButton = ({ text, href, variant = "primary" }) => {
  const handleClick = () => {
    trackEvent('click', 'CTA', text);
  };

  return (
    <a
      href={href}
      className={`btn btn-${variant}`}
      onClick={handleClick}
    >
      {text}
    </a>
  );
};
```

### Performance Monitoring
Monitor site performance:

```javascript
// utils/performance.js
export const reportWebVitals = (metric) => {
  if (process.env.NODE_ENV === 'production') {
    // Send to analytics service
    gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.value),
      non_interaction: true,
    });
  }
};

// pages/_app.js
export { reportWebVitals };
```

## 🔧 Customization

### Theme Customization
Easy theme and brand customization:

```scss
// styles/variables.scss
:root {
  // Brand colors
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;

  // Typography
  --font-family-primary: 'Poppins', sans-serif;
  --font-family-secondary: 'Inter', sans-serif;

  // Spacing
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;

  // Border radius
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 1rem;
}
```

### Component Customization
Flexible component system:

```javascript
// components/Button.js
const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  disabled = false,
  onClick,
  ...props
}) => {
  const baseClasses = 'btn';
  const variantClasses = `btn-${variant}`;
  const sizeClasses = `btn-${size}`;
  const widthClasses = fullWidth ? 'w-100' : '';

  const className = [
    baseClasses,
    variantClasses,
    sizeClasses,
    widthClasses
  ].filter(Boolean).join(' ');

  return (
    <button
      className={className}
      disabled={disabled}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
};
```

## 🧪 Testing

The project includes comprehensive testing setup with Jest and React Testing Library.

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Type checking
npm run type-check
```

### Test Structure
```
__tests__/
├── components/        # Component tests
│   ├── Hero.test.js
│   ├── FAQ.test.js
│   └── ...
├── hooks/            # Hook tests
│   ├── useTranslation.test.js
│   └── ...
├── pages/            # Page tests
│   ├── index.test.js
│   └── ...
└── utils/            # Utility tests
    ├── analytics.test.js
    └── ...
```

### Component Testing Example
```javascript
// __tests__/components/Hero.test.js
import { render, screen } from '@testing-library/react';
import Hero from '../../src/components/Hero';

describe('Hero Component', () => {
  test('renders hero title and subtitle', () => {
    const title = "Test Title";
    const subtitle = "Test Subtitle";

    render(<Hero title={title} subtitle={subtitle} />);

    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText(subtitle)).toBeInTheDocument();
  });

  test('renders CTA buttons', () => {
    render(<Hero title="Test" subtitle="Test" />);

    expect(screen.getByText('Get Started')).toBeInTheDocument();
    expect(screen.getByText('Learn More')).toBeInTheDocument();
  });
});
```

### Translation Testing
```javascript
// __tests__/hooks/useTranslation.test.js
import { renderHook } from '@testing-library/react';
import { useTranslation } from '../../src/hooks/useTranslation';

describe('useTranslation Hook', () => {
  test('returns correct translation for English', () => {
    const { result } = renderHook(() => useTranslation());

    expect(result.current.t('hero.title')).toBe('Your One-Stop Solution for Getting Work Done');
    expect(result.current.isRTL).toBe(false);
  });

  test('handles RTL for Arabic', () => {
    // Mock router to return Arabic locale
    const { result } = renderHook(() => useTranslation());

    // Test RTL functionality
    expect(result.current.isRTL).toBeDefined();
  });
});
```

### E2E Testing
```javascript
// cypress/integration/landing-page.spec.js
describe('Landing Page', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('should display hero section', () => {
    cy.get('.hero-section').should('be.visible');
    cy.get('.hero-title').should('contain', 'Your One-Stop Solution');
  });

  it('should navigate to features section', () => {
    cy.get('[href="#features"]').click();
    cy.get('#features').should('be.visible');
  });

  it('should track CTA clicks', () => {
    cy.window().then((win) => {
      cy.stub(win, 'gtag').as('gtag');
    });

    cy.get('.btn-primary').first().click();
    cy.get('@gtag').should('have.been.called');
  });
});
```

## 🔒 Security

### Security Headers
Implemented security best practices:

```javascript
// next.config.js security headers
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'SAMEORIGIN'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
];
```

### Content Security Policy
```javascript
const ContentSecurityPolicy = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline' *.googletagmanager.com;
  child-src *.youtube.com *.google.com *.twitter.com;
  style-src 'self' 'unsafe-inline' *.googleapis.com;
  img-src * blob: data:;
  media-src 'none';
  connect-src *;
  font-src 'self' *.googleapis.com *.gstatic.com;
`;
```

## 📈 Performance Optimization

### Image Optimization
```javascript
// components/OptimizedImage.js
import Image from 'next/image';

const OptimizedImage = ({ src, alt, width, height, priority = false }) => {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      priority={priority}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
      quality={85}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    />
  );
};
```

### Code Splitting
```javascript
// Dynamic imports for better performance
import dynamic from 'next/dynamic';

const DynamicTestimonials = dynamic(() => import('../components/Testimonials'), {
  loading: () => <div>Loading testimonials...</div>,
  ssr: false
});

const DynamicContactForm = dynamic(() => import('../components/ContactForm'), {
  loading: () => <div>Loading contact form...</div>
});
```

## 🛠️ Development Workflow

### Code Quality Tools
```bash
# Linting
npm run lint

# Code formatting
npm run format        # Check formatting
npm run format:fix    # Fix formatting issues

# Type checking
npm run type-check
```

### Pre-commit Hooks
The project uses ESLint and Prettier to maintain code quality:

```json
// .eslintrc.json
{
  "extends": ["next/core-web-vitals", "prettier"],
  "rules": {
    "no-unused-vars": "error",
    "no-console": "warn"
  }
}
```

### Component Development
1. Create component in `src/components/`
2. Add corresponding test file in `__tests__/components/`
3. Update translations if needed
4. Add RTL support for layout components
5. Test in both English and Arabic

### Adding New Pages
1. Create page in appropriate directory (`src/pages/` or `src/pages/ar/`)
2. Add translations to both `en.json` and `ar.json`
3. Update navigation if needed
4. Add SEO meta tags
5. Test responsive design

## 🤝 Contributing

### Development Guidelines
1. Follow Next.js best practices and conventions
2. Use semantic HTML and accessible design patterns
3. Implement responsive design for all components
4. Optimize images and assets for web performance
5. Write comprehensive tests for new features
6. Update documentation for significant changes
7. Ensure RTL support for new components
8. Test in multiple languages

### Code Style
- Use functional components with hooks
- Implement proper TypeScript types (if migrating)
- Follow ESLint and Prettier configurations
- Use meaningful component and variable names
- Comment complex logic and business rules
- Follow accessibility best practices
- Use semantic HTML elements

## 📞 Support

### Landing Page Support
- **Build Issues**: Check Next.js configuration and dependencies
- **Styling Issues**: Verify Bootstrap and Tailwind CSS setup
- **Performance Issues**: Use Next.js built-in performance tools
- **SEO Issues**: Check meta tags and structured data

## 🔗 Related Documentation

- [Main Project README](../../README.md)
- [Backend API Documentation](../fixmix_backend/README.md)
- [User Mobile App Documentation](../fixmix/README.md)
- [Vendor Mobile App Documentation](../fixmix_vendor/README.md)
- [Admin Panel Documentation](../Fixmix-Admin/README.md)
```
