# FixMix Landing Page

A modern, responsive landing page for FixMix - the on-demand service marketplace connecting users with verified professionals for home services.

## 🌟 Features

- 🌐 **Bilingual Support**: English and Arabic with proper RTL layout
- 📱 **Responsive Design**: Optimized for all device sizes (mobile-first approach)
- ⚡ **Performance Optimized**: Fast loading with image optimization, lazy loading, and service worker caching
- ♿ **Accessible**: WCAG 2.1 AA compliant with keyboard navigation and screen reader support
- 🎨 **Modern UI**: Clean design with smooth animations and micro-interactions
- 🔍 **SEO Optimized**: Comprehensive meta tags, structured data, and sitemap
- 🚀 **PWA Ready**: Service worker for offline functionality and app-like experience
- 📊 **Analytics**: Google Analytics integration with performance monitoring
- 🔒 **Error Handling**: Comprehensive error boundaries and fallback components

## 🛠 Tech Stack

- **Framework**: Next.js 14 with React 18
- **Styling**: SCSS with Bootstrap 5.3
- **Fonts**: Google Fonts (Montserrat, Noto Sans Arabic)
- **Icons**: Custom SVG icons with optimization
- **Analytics**: Google Analytics 4 with Web Vitals
- **Performance**: Service Worker, Image optimization, Code splitting
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **SEO**: Meta tags, Open Graph, Twitter Cards, JSON-LD structured data

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Git

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd fixmix-landing-page
```

2. **Install dependencies**
```bash
npm install
```

3. **Run the development server**
```bash
npm run dev
```

4. **Open in browser**
   - English: [http://localhost:3000](http://localhost:3000)
   - Arabic: [http://localhost:3000/ar](http://localhost:3000/ar)

### Available Scripts

- `npm run dev` - Start development server (port 3000)
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run export` - Export static files

## 🌍 Internationalization

The application supports English (default) and Arabic with comprehensive RTL support:

### Language Access
- **English**: `https://www.fixmix.co` or `http://localhost:3000`
- **Arabic**: `https://www.fixmix.co/ar` or `http://localhost:3000/ar`

### RTL Features
- Proper text direction and alignment
- Mirrored layouts for navigation and components
- Arabic typography with appropriate fonts
- Flipped icons and arrows for RTL context
- Keyboard navigation respecting text direction

## 🎨 Design System

### Colors
- **Primary**: #0096c7 (FixMix Blue)
- **Secondary**: #3B82F6 (Accent Blue)
- **Success**: #10B981 (Green)
- **Warning**: #F59E0B (Orange)
- **Error**: #EF4444 (Red)

### Typography
- **English**: Montserrat (Google Fonts)
- **Arabic**: Noto Sans Arabic, Cairo, Amiri, Tajawal

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🚀 Performance Optimizations

### Implemented Optimizations
- **Image Optimization**: Next.js Image component with lazy loading
- **Code Splitting**: Dynamic imports for heavy components
- **Service Worker**: Caching strategy for offline functionality
- **Font Optimization**: Preloaded critical fonts
- **Bundle Analysis**: Optimized bundle size
- **Critical CSS**: Inlined above-the-fold styles
- **Resource Hints**: DNS prefetch and preconnect

### Performance Metrics
- **Lighthouse Score**: 95+ (Performance, Accessibility, Best Practices, SEO)
- **Core Web Vitals**: Optimized LCP, FID, and CLS
- **Bundle Size**: < 500KB gzipped

## ♿ Accessibility Features

### WCAG 2.1 AA Compliance
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: ARIA labels and semantic HTML
- **Color Contrast**: 4.5:1 minimum contrast ratio
- **Focus Management**: Visible focus indicators
- **Alternative Text**: Descriptive alt text for images
- **Language Declaration**: Proper lang attributes
- **Skip Links**: Navigation shortcuts

## 🔍 SEO Optimization

### Technical SEO
- **Meta Tags**: Comprehensive title, description, and keywords
- **Open Graph**: Social media optimization
- **Twitter Cards**: Twitter-specific meta tags
- **Structured Data**: JSON-LD for rich snippets
- **Sitemap**: XML sitemap for search engines
- **Robots.txt**: Search engine directives
- **Canonical URLs**: Duplicate content prevention

## 🚀 Deployment

### Production Build
```bash
npm run build
npm run start
```

### Static Export (for CDN)
```bash
npm run export
```

### Environment Variables
Create `.env.local` for local development:
```env
NEXT_PUBLIC_GA_ID=your-google-analytics-id
NEXT_PUBLIC_SITE_URL=https://www.fixmix.co
```

### Hosting Recommendations
- **Vercel**: Optimal for Next.js (recommended)
- **Netlify**: Good static hosting option
- **AWS S3 + CloudFront**: Enterprise solution
- **Hostinger**: Current hosting provider

## 📊 Analytics & Monitoring

### Google Analytics 4
- Page views and user interactions
- Conversion tracking for app downloads
- Performance metrics (Core Web Vitals)
- User demographics and behavior

### Performance Monitoring
- Real-time performance tracking
- Error reporting and logging
- User experience metrics
- Bundle size monitoring

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Test thoroughly (accessibility, performance, RTL)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Code Standards
- Follow ESLint configuration
- Write accessible, semantic HTML
- Test in both English and Arabic
- Ensure mobile responsiveness
- Maintain performance standards

## 📝 License

This project is proprietary to FixMix. All rights reserved.

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Documentation**: Internal wiki
- **Issues**: GitHub Issues (for development team)

---

**Built with ❤️ by the FixMix Development Team**
