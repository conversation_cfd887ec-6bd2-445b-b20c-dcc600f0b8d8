import React from 'react';
import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/router';
import { useTranslation } from '../../src/hooks/useTranslation';

// Mock the useRouter hook
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Test component to use the hook
function TestComponent() {
  const { t, locale, isRTL, availableLocales, changeLanguage } = useTranslation();

  return (
    <div>
      <div data-testid="locale">{locale}</div>
      <div data-testid="isRTL">{isRTL.toString()}</div>
      <div data-testid="translation">{t('test.key', 'Default Text')}</div>
      <div data-testid="availableLocales">{availableLocales.join(',')}</div>
      <div data-testid="changeLanguage">{typeof changeLanguage}</div>
    </div>
  );
}

// Mock the translation files
jest.mock('../../src/locales/en.json', () => ({
  'hero': {
    'title': 'Your One-Stop Solution for Getting Work Done'
  },
  'faq': {
    'title': "FAQ's"
  }
}), { virtual: true });

jest.mock('../../src/locales/ar.json', () => ({
  'hero': {
    'title': 'حلك الشامل لإنجاز العمل'
  },
  'faq': {
    'title': 'الأسئلة الشائعة'
  }
}), { virtual: true });

describe('useTranslation Hook', () => {
  beforeEach(() => {
    // Reset DOM direction
    if (typeof document !== 'undefined') {
      document.documentElement.dir = 'ltr';
      document.documentElement.lang = 'en';
    }
  });

  test('returns correct translation for English', () => {
    useRouter.mockReturnValue({
      locale: 'en',
      pathname: '/',
      asPath: '/',
      push: jest.fn(),
    });

    render(<TestComponent />);

    expect(screen.getByTestId('locale')).toHaveTextContent('en');
    expect(screen.getByTestId('isRTL')).toHaveTextContent('false');
    expect(screen.getByTestId('translation')).toHaveTextContent('Default Text');
  });

  test('returns correct translation for Arabic', () => {
    useRouter.mockReturnValue({
      locale: 'ar',
      pathname: '/ar',
      asPath: '/ar',
      push: jest.fn(),
    });

    render(<TestComponent />);

    expect(screen.getByTestId('locale')).toHaveTextContent('ar');
    expect(screen.getByTestId('isRTL')).toHaveTextContent('true');
    expect(screen.getByTestId('translation')).toHaveTextContent('Default Text');
  });

  test('provides available locales', () => {
    useRouter.mockReturnValue({
      locale: 'en',
      pathname: '/',
      asPath: '/',
      push: jest.fn(),
    });

    render(<TestComponent />);

    expect(screen.getByTestId('availableLocales')).toHaveTextContent('en,ar');
  });

  test('provides changeLanguage function', () => {
    const mockPush = jest.fn().mockResolvedValue(true);
    useRouter.mockReturnValue({
      locale: 'en',
      pathname: '/',
      asPath: '/',
      push: mockPush,
    });

    render(<TestComponent />);

    expect(screen.getByTestId('changeLanguage')).toHaveTextContent('function');
  });
});
