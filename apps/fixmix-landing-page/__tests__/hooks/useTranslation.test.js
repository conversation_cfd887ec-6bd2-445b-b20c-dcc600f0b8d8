import { renderHook } from '@testing-library/react';
import { useRouter } from 'next/router';
import { useTranslation } from '../../src/hooks/useTranslation';

// Mock the useRouter hook
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock the translation files
jest.mock('../../src/locales/en.json', () => ({
  'hero': {
    'title': 'Your One-Stop Solution for Getting Work Done'
  },
  'faq': {
    'title': "FAQ's"
  }
}), { virtual: true });

jest.mock('../../src/locales/ar.json', () => ({
  'hero': {
    'title': 'حلك الشامل لإنجاز العمل'
  },
  'faq': {
    'title': 'الأسئلة الشائعة'
  }
}), { virtual: true });

describe('useTranslation Hook', () => {
  beforeEach(() => {
    // Reset DOM direction
    if (typeof document !== 'undefined') {
      document.documentElement.dir = 'ltr';
      document.documentElement.lang = 'en';
    }
  });

  test('returns correct translation for English', () => {
    useRouter.mockReturnValue({
      locale: 'en',
      pathname: '/',
      asPath: '/',
      push: jest.fn(),
    });

    const { result } = renderHook(() => useTranslation());
    
    expect(result.current.locale).toBe('en');
    expect(result.current.isRTL).toBe(false);
    expect(typeof result.current.t).toBe('function');
  });

  test('returns correct translation for Arabic', () => {
    useRouter.mockReturnValue({
      locale: 'ar',
      pathname: '/ar',
      asPath: '/ar',
      push: jest.fn(),
    });

    const { result } = renderHook(() => useTranslation());
    
    expect(result.current.locale).toBe('ar');
    expect(result.current.isRTL).toBe(true);
    expect(typeof result.current.t).toBe('function');
  });

  test('t function returns fallback when key not found', () => {
    useRouter.mockReturnValue({
      locale: 'en',
      pathname: '/',
      asPath: '/',
      push: jest.fn(),
    });

    const { result } = renderHook(() => useTranslation());
    
    const translation = result.current.t('nonexistent.key', 'Default Text');
    expect(translation).toBe('Default Text');
  });

  test('t function returns key when no fallback provided', () => {
    useRouter.mockReturnValue({
      locale: 'en',
      pathname: '/',
      asPath: '/',
      push: jest.fn(),
    });

    const { result } = renderHook(() => useTranslation());
    
    const translation = result.current.t('nonexistent.key');
    expect(translation).toBe('nonexistent.key');
  });

  test('provides available locales', () => {
    useRouter.mockReturnValue({
      locale: 'en',
      pathname: '/',
      asPath: '/',
      push: jest.fn(),
    });

    const { result } = renderHook(() => useTranslation());
    
    expect(result.current.availableLocales).toEqual(['en', 'ar']);
  });

  test('provides changeLanguage function', () => {
    const mockPush = jest.fn().mockResolvedValue(true);
    useRouter.mockReturnValue({
      locale: 'en',
      pathname: '/',
      asPath: '/',
      push: mockPush,
    });

    const { result } = renderHook(() => useTranslation());
    
    expect(typeof result.current.changeLanguage).toBe('function');
  });
});
