import React from 'react';
import { render, screen } from '@testing-library/react';
import <PERSON> from '../../src/components/Hero';

// Mock components that might cause issues
jest.mock('../../src/components/AppLinks', () => {
  return function MockAppLinks() {
    return <div data-testid="app-links">Download App Links</div>;
  };
});

jest.mock('../../src/components/OptimizedImage', () => {
  const MockImage = ({ src, alt, ...props }) => (
    <img src={src} alt={alt} {...props} data-testid="optimized-image" />
  );

  return {
    __esModule: true,
    default: MockImage,
    PhoneScreenshot: MockImage,
    HeroImage: MockImage,
  };
});

describe('Hero Component', () => {
  test('renders without crashing', () => {
    render(<Hero />);
    expect(document.body).toBeInTheDocument();
  });

  test('renders hero badge with Arabic text', () => {
    render(<Hero />);

    // Check if the hero badge is rendered with Arabic text
    const badge = screen.getByText(/متوفر الآن على iOS و Android/);
    expect(badge).toBeInTheDocument();
  });

  test('renders app links component', () => {
    render(<Hero />);

    // Check if AppLinks component is rendered
    const appLinks = screen.getByTestId('app-links');
    expect(appLinks).toBeInTheDocument();
    expect(appLinks).toHaveTextContent('Download App Links');
  });

  test('renders phone images', () => {
    render(<Hero />);

    // Check if phone images are rendered
    const images = screen.getAllByTestId('optimized-image');
    expect(images.length).toBeGreaterThan(0);
  });
});
