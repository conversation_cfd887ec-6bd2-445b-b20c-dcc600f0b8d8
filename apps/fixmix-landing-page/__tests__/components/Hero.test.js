import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/router';
import Hero from '../../src/components/Hero';

// Mock the useRouter hook
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock the useTranslation hook
jest.mock('../../src/hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key, fallback) => fallback || key,
    isRTL: false,
    locale: 'en'
  })
}));

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }) {
    return <img src={src} alt={alt} {...props} />;
  };
});

describe('Hero Component', () => {
  beforeEach(() => {
    useRouter.mockReturnValue({
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      locale: 'en'
    });
  });

  test('renders without crashing', () => {
    render(<Hero />);
    expect(document.body).toBeInTheDocument();
  });

  test('renders hero content', () => {
    render(<Hero />);
    
    // Check if the hero section is rendered
    const heroSection = document.querySelector('.hero-section, [class*="hero"]');
    expect(heroSection).toBeInTheDocument();
  });

  test('renders with proper structure', () => {
    render(<Hero />);
    
    // The component should render without throwing errors
    expect(document.body).toBeInTheDocument();
  });
});
