import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/router';
import <PERSON> from '../../src/components/Hero';

// Mock the useRouter hook
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock the useTranslation hook
jest.mock('../../src/hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key, fallback) => fallback || key,
    isRTL: false,
    locale: 'en'
  })
}));

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }) {
    return <img src={src} alt={alt} {...props} />;
  };
});

describe('Hero Component', () => {
  beforeEach(() => {
    useRouter.mockReturnValue({
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      locale: 'en'
    });
  });

  test('renders without crashing', () => {
    render(<Hero />);
    expect(document.body).toBeInTheDocument();
  });

  test('renders hero badge', () => {
    render(<Hero />);

    // Check if the hero badge is rendered
    const badge = screen.getByText(/متوفر الآن على iOS و Android/);
    expect(badge).toBeInTheDocument();
  });

  test('renders app links component', () => {
    render(<Hero />);

    // Check if AppLinks component is rendered
    const appLinks = screen.getByTestId('app-links');
    expect(appLinks).toBeInTheDocument();
  });
});
