import { render, screen } from '@testing-library/react';
import FAQ from '../../src/components/FAQ';

// Mock the useTranslation hook
jest.mock('../../src/hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key, fallback) => {
      const translations = {
        'faq.title': "FAQ's",
        'faq.subtitle': 'Find answers to commonly asked questions about FixMix',
        'faq.items.booking.question': 'How do I book a service on FixMix?',
        'faq.items.booking.answer': 'Simply browse categories, chat with a vendor, and confirm your booking.',
        'faq.items.payments.question': 'How do vendors receive payments?',
        'faq.items.payments.answer': 'Payments are credited to the FixMix wallet, and vendors can withdraw earnings anytime.',
        'faq.items.verification.question': 'Are vendors background-checked?',
        'faq.items.verification.answer': 'Yes, all vendors go through a verification process before listing their services.',
        'faq.items.tracking.question': 'Can I track my service request?',
        'faq.items.tracking.answer': 'Absolutely! You get real-time updates on your service request and can communicate directly with the vendor.',
        'faq.items.parts.question': 'What if I need additional spare parts?',
        'faq.items.parts.answer': 'Vendors can bring additional parts upon request. You can discuss this during the chat before starting the service.'
      };
      return translations[key] || fallback || key;
    },
    isRTL: false,
    locale: 'en'
  })
}));

// Mock AnimatedSection component
jest.mock('../../src/components/AnimatedSection', () => {
  return function MockAnimatedSection({ children, ...props }) {
    return <div data-testid="animated-section" {...props}>{children}</div>;
  };
});

describe('FAQ Component', () => {
  test('renders FAQ title', () => {
    render(<FAQ />);
    expect(screen.getByText("FAQ's")).toBeInTheDocument();
  });

  test('renders FAQ subtitle', () => {
    render(<FAQ />);
    expect(screen.getByText('Find answers to commonly asked questions about FixMix')).toBeInTheDocument();
  });

  test('renders FAQ questions', () => {
    render(<FAQ />);
    
    expect(screen.getByText('How do I book a service on FixMix?')).toBeInTheDocument();
    expect(screen.getByText('How do vendors receive payments?')).toBeInTheDocument();
    expect(screen.getByText('Are vendors background-checked?')).toBeInTheDocument();
    expect(screen.getByText('Can I track my service request?')).toBeInTheDocument();
    expect(screen.getByText('What if I need additional spare parts?')).toBeInTheDocument();
  });

  test('renders FAQ answers', () => {
    render(<FAQ />);
    
    expect(screen.getByText('Simply browse categories, chat with a vendor, and confirm your booking.')).toBeInTheDocument();
    expect(screen.getByText('Payments are credited to the FixMix wallet, and vendors can withdraw earnings anytime.')).toBeInTheDocument();
    expect(screen.getByText('Yes, all vendors go through a verification process before listing their services.')).toBeInTheDocument();
  });

  test('has proper accordion structure', () => {
    render(<FAQ />);
    
    const accordion = document.querySelector('#faq_accordion');
    expect(accordion).toBeInTheDocument();
    expect(accordion).toHaveClass('accordion');
  });

  test('renders with proper direction for LTR', () => {
    render(<FAQ />);
    
    const faqSection = document.querySelector('[dir="ltr"]');
    expect(faqSection).toBeInTheDocument();
  });
});
