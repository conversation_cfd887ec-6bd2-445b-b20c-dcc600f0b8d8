import React from 'react';
import { render, screen } from '@testing-library/react';
import FAQ from '../../src/components/FAQ';

// Mock AnimatedSection component
jest.mock('../../src/components/AnimatedSection', () => {
  return function MockAnimatedSection({ children, ...props }) {
    return <div data-testid="animated-section" {...props}>{children}</div>;
  };
});

// Mock FaqItem component
jest.mock('../../src/components/FaqItem', () => {
  return function MockFaqItem({ question, answer, itemNum }) {
    return (
      <div data-testid={`faq-item-${itemNum}`}>
        <div data-testid="faq-question">{question}</div>
        <div data-testid="faq-answer">{answer}</div>
      </div>
    );
  };
});

describe('FAQ Component', () => {
  test('renders without crashing', () => {
    render(<FAQ />);
    expect(document.body).toBeInTheDocument();
  });

  test('renders FAQ title', () => {
    render(<FAQ />);
    expect(screen.getByText("FAQ's")).toBeInTheDocument();
  });

  test('renders FAQ subtitle', () => {
    render(<FAQ />);
    expect(screen.getByText('Find answers to commonly asked questions about FixMix')).toBeInTheDocument();
  });

  test('renders animated sections', () => {
    render(<FAQ />);
    
    const animatedSections = screen.getAllByTestId('animated-section');
    expect(animatedSections.length).toBeGreaterThan(0);
  });

  test('renders FAQ items', () => {
    render(<FAQ />);
    
    // Check if FAQ items are rendered
    const faqItems = screen.getAllByTestId(/faq-item-/);
    expect(faqItems.length).toBeGreaterThan(0);
  });

  test('has proper section structure', () => {
    render(<FAQ />);
    
    // Check if the main section exists
    const section = document.querySelector('section');
    expect(section).toBeInTheDocument();
  });
});
