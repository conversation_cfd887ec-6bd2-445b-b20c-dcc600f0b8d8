# FixMix Landing Page - Deployment Guide

This guide covers deployment options for the FixMix landing page to various hosting platforms.

## 🚀 Quick Deployment

### Prerequisites
- Node.js 18+ installed
- Git repository access
- Hosting platform account

### Build for Production
```bash
# Install dependencies
npm install

# Run production build
npm run build

# Test production build locally
npm run start
```

## 🌐 Hosting Platforms

### 1. <PERSON><PERSON> (Current)
**Recommended for current setup**

#### Steps:
1. **Build the application:**
   ```bash
   npm run build
   npm run export
   ```

2. **Upload files:**
   - Upload the `out/` directory contents to your Hostinger public_html folder
   - Ensure all files maintain their directory structure

3. **Configure domain:**
   - Point your domain (www.fixmix.co) to the hosting directory
   - Ensure SSL certificate is active

#### Hostinger-specific notes:
- Use static export (`npm run export`) for better compatibility
- Upload to `public_html` or domain-specific folder
- Configure redirects in `.htaccess` if needed

### 2. Vercel (Recommended for Next.js)
**Best performance and features**

#### Automatic Deployment:
1. Connect your GitHub repository to Vercel
2. Vercel automatically detects Next.js and configures build settings
3. Every push to main branch triggers automatic deployment

#### Manual Deployment:
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Production deployment
vercel --prod
```

#### Environment Variables:
```env
NEXT_PUBLIC_GA_ID=your-google-analytics-id
NEXT_PUBLIC_SITE_URL=https://www.fixmix.co
```

### 3. Netlify
**Good alternative with easy setup**

#### Steps:
1. **Build command:** `npm run build`
2. **Publish directory:** `out`
3. **Environment variables:** Set in Netlify dashboard

#### netlify.toml configuration:
```toml
[build]
  command = "npm run build && npm run export"
  publish = "out"

[[redirects]]
  from = "/ar/*"
  to = "/ar/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 4. AWS S3 + CloudFront
**Enterprise solution with CDN**

#### Steps:
1. Create S3 bucket with static website hosting
2. Upload `out/` directory contents
3. Configure CloudFront distribution
4. Set up custom domain with Route 53

## 🔧 Environment Configuration

### Required Environment Variables
```env
# Google Analytics (optional)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Site URL for SEO
NEXT_PUBLIC_SITE_URL=https://www.fixmix.co

# Build environment
NODE_ENV=production
```

### Development vs Production
- **Development:** `npm run dev`
- **Production:** `npm run build && npm run start`
- **Static Export:** `npm run export` (for static hosting)

## 📊 Performance Optimization

### Pre-deployment Checklist
- [ ] Run `npm run build` successfully
- [ ] Test both English and Arabic versions
- [ ] Verify all images load correctly
- [ ] Check mobile responsiveness
- [ ] Test FAQ accordion functionality
- [ ] Validate SEO meta tags
- [ ] Confirm analytics tracking

### Build Optimization
```bash
# Check bundle size
npm run build

# Analyze bundle (if analyzer is installed)
npm run analyze
```

## 🔍 SEO Configuration

### Sitemap
- Automatically generated at `/sitemap.xml`
- Includes both English and Arabic pages
- Submit to Google Search Console

### Robots.txt
- Located at `/robots.txt`
- Allows all crawlers
- Points to sitemap

### Meta Tags
- Configured for both languages
- Open Graph and Twitter Cards included
- Structured data (JSON-LD) implemented

## 🌍 Internationalization

### URL Structure
- English: `https://www.fixmix.co/`
- Arabic: `https://www.fixmix.co/ar/`

### Language Switching
- Maintains current page context
- Preserves scroll position
- Updates meta tags and lang attributes

## 🚨 Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clear Next.js cache
rm -rf .next

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Rebuild
npm run build
```

#### Image Loading Issues
- Ensure all images are in `public/images/` directory
- Check image paths are correct
- Verify image formats are supported (jpg, png, webp)

#### RTL Layout Issues
- Test Arabic pages thoroughly
- Verify arrow directions in FAQ
- Check text alignment

#### Performance Issues
- Optimize images before deployment
- Enable compression on hosting platform
- Use CDN for static assets

### Deployment Verification
1. **Functionality Test:**
   - [ ] Homepage loads correctly
   - [ ] Language switching works
   - [ ] FAQ accordion functions
   - [ ] All links work
   - [ ] Forms submit properly

2. **Performance Test:**
   - [ ] Page load time < 3 seconds
   - [ ] Lighthouse score > 90
   - [ ] Mobile performance acceptable

3. **SEO Test:**
   - [ ] Meta tags present
   - [ ] Sitemap accessible
   - [ ] Structured data valid

## 📞 Support

### Deployment Issues
- Check hosting platform documentation
- Verify build logs for errors
- Test locally before deployment

### Performance Issues
- Use Lighthouse for analysis
- Check Core Web Vitals
- Monitor real user metrics

---

**Last Updated:** December 2024
**Version:** 1.0.0
