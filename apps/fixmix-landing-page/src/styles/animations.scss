// Interactive Animations for FixMix Landing Page

// ===== ANIMATION KEYFRAMES =====

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-180deg) scale(0.8);
  }
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

// ===== ANIMATION CLASSES =====

.animate-fadeIn {
  animation: fadeIn 0.8s ease-out both;
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out both;
}

.animate-fadeInDown {
  animation: fadeInDown 0.8s ease-out both;
}

.animate-fadeInLeft {
  animation: fadeInLeft 0.8s ease-out both;
}

.animate-fadeInRight {
  animation: fadeInRight 0.8s ease-out both;
}

.animate-scaleIn {
  animation: scaleIn 0.6s ease-out both;
}

.animate-slideInUp {
  animation: slideInUp 0.8s ease-out both;
}

.animate-slideInDown {
  animation: slideInDown 0.8s ease-out both;
}

.animate-rotateIn {
  animation: rotateIn 0.8s ease-out both;
}

.animate-bounceIn {
  animation: bounceIn 0.8s ease-out both;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

// ===== HOVER EFFECTS =====

.hover-lift {
  transition: all var(--transition-base);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
  }
}

.hover-scale {
  transition: all var(--transition-base);
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-rotate {
  transition: all var(--transition-base);
  
  &:hover {
    transform: rotate(5deg);
  }
}

.hover-glow {
  transition: all var(--transition-base);
  
  &:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
}

.hover-slide-right {
  transition: all var(--transition-base);
  
  &:hover {
    transform: translateX(5px);
  }
}

.hover-slide-left {
  transition: all var(--transition-base);
  
  &:hover {
    transform: translateX(-5px);
  }
}

.hover-bounce {
  transition: all var(--transition-fast);
  
  &:hover {
    animation: pulse 0.6s ease-in-out;
  }
}

// ===== MICRO-INTERACTIONS =====

.btn-interactive {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-base);
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }
  
  &:hover::before {
    width: 300px;
    height: 300px;
  }
  
  &:active {
    transform: scale(0.98);
  }
}

.card-interactive {
  transition: all var(--transition-base);
  cursor: pointer;
  
  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
  }
  
  &:active {
    transform: translateY(-4px) scale(1.01);
  }
}

.link-interactive {
  position: relative;
  text-decoration: none;
  transition: all var(--transition-base);
  
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: currentColor;
    transition: width var(--transition-base);
  }
  
  &:hover::after {
    width: 100%;
  }
}

// ===== LOADING ANIMATIONS =====

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: currentColor;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-dots {
  display: inline-flex;
  gap: 4px;
  
  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: loadingDots 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

// ===== TYPING ANIMATION =====

.typing-cursor {
  animation: blink 1s infinite;
  font-weight: bold;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

// ===== SCROLL ANIMATIONS =====

.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
  
  &.revealed {
    opacity: 1;
    transform: translateY(0);
  }
}

.parallax-element {
  will-change: transform;
}

// ===== RESPONSIVE ANIMATIONS =====

@media (max-width: 768px) {
  // Reduce animation intensity on mobile
  .hover-lift:hover {
    transform: translateY(-2px);
  }
  
  .hover-scale:hover {
    transform: scale(1.02);
  }
  
  .card-interactive:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

// ===== ACCESSIBILITY =====

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .animate-float,
  .animate-pulse,
  .animate-glow {
    animation: none !important;
  }
  
  .hover-lift:hover,
  .hover-scale:hover,
  .hover-rotate:hover,
  .card-interactive:hover {
    transform: none !important;
  }
}
