// Accessibility Enhancements for FixMix Landing Page

// ===== FOCUS MANAGEMENT =====

// Enhanced focus styles
*:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

// Skip to content link
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-600);
  color: white;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: 10000;
  font-weight: var(--font-medium);
  transition: top var(--transition-fast);
  
  &:focus {
    top: 6px;
    outline: 2px solid white;
  }
}

// Focus visible for keyboard navigation
.focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

// Remove focus outline for mouse users
.no-focus-outline:focus:not(.focus-visible) {
  outline: none;
}

// ===== SCREEN READER SUPPORT =====

// Screen reader only content
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Screen reader only content that becomes visible on focus
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

// ===== KEYBOARD NAVIGATION =====

// Enhanced button keyboard interaction
.btn {
  &:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
  }
  
  &:focus:not(:focus-visible) {
    outline: none;
    box-shadow: none;
  }
  
  &:focus-visible {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
  }
}

// Enhanced link keyboard interaction
a {
  &:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
  
  &:focus:not(:focus-visible) {
    outline: none;
  }
  
  &:focus-visible {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
}

// ===== HIGH CONTRAST MODE =====

@media (prefers-contrast: high) {
  :root {
    --primary-500: #0000ff;
    --gray-900: #000000;
    --gray-100: #ffffff;
  }
  
  .btn-primary {
    background-color: #0000ff;
    border-color: #0000ff;
    color: #ffffff;
    
    &:hover {
      background-color: #0000cc;
      border-color: #0000cc;
    }
  }
  
  .btn-outline-primary {
    color: #0000ff;
    border-color: #0000ff;
    
    &:hover {
      background-color: #0000ff;
      color: #ffffff;
    }
  }
  
  .card-modern {
    border: 2px solid #000000;
  }
  
  .text-muted {
    color: #000000 !important;
  }
}

// ===== REDUCED MOTION =====

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  // Disable parallax and complex animations
  .parallax-element {
    transform: none !important;
  }
  
  // Keep essential transitions for usability
  .btn,
  .nav-link,
  .accordion-button {
    transition: background-color 0.15s ease-in-out,
                border-color 0.15s ease-in-out,
                color 0.15s ease-in-out !important;
  }
}

// ===== COLOR CONTRAST =====

// Ensure sufficient color contrast
.text-muted {
  color: var(--gray-600) !important; // Improved contrast
}

.text-light {
  color: rgba(255, 255, 255, 0.95) !important; // Improved contrast
}

// ===== TOUCH TARGETS =====

// Minimum touch target size (44px x 44px)
.btn,
.nav-link,
.accordion-button,
.mobile-nav-link {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  @media (max-width: 768px) {
    min-height: 48px; // Larger on mobile
  }
}

// Touch-friendly spacing
@media (max-width: 768px) {
  .btn + .btn {
    margin-top: 8px;
  }
  
  .nav-link + .nav-link {
    margin-top: 4px;
  }
}

// ===== ARIA LABELS AND DESCRIPTIONS =====

// Loading states with proper ARIA
.loading-spinner {
  &[aria-label] {
    // Ensure loading spinners have proper labels
  }
}

// Progress indicators
.scroll-progress {
  &[role="progressbar"] {
    // Ensure progress bars have proper ARIA attributes
  }
}

// ===== FORM ACCESSIBILITY =====

// Enhanced form controls
.form-control,
.form-select {
  &:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
  }
  
  &:invalid {
    border-color: var(--error);
    
    &:focus {
      outline-color: var(--error);
      box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
    }
  }
}

// Error messages
.error-message {
  color: var(--error);
  font-size: var(--text-sm);
  margin-top: var(--spacing-xs);
  
  &::before {
    content: "⚠ ";
    font-weight: bold;
  }
}

// ===== RESPONSIVE TEXT =====

// Ensure text remains readable at all zoom levels
@media (max-width: 576px) {
  body {
    font-size: 16px; // Prevent zoom on iOS
  }
  
  .btn {
    font-size: 16px; // Prevent zoom on iOS
  }
  
  input,
  select,
  textarea {
    font-size: 16px; // Prevent zoom on iOS
  }
}

// ===== PRINT ACCESSIBILITY =====

@media print {
  // Ensure content is accessible when printed
  .sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }
  
  // Show URLs for links
  a[href^="http"]:after {
    content: " (" attr(href) ")";
    font-size: 0.8em;
    color: #666;
  }
  
  // Hide decorative elements
  .hero-bg-decoration,
  .scroll-progress,
  .sticky-nav {
    display: none !important;
  }
}

// ===== LANGUAGE SUPPORT =====

// RTL support preparation for Arabic
[dir="rtl"] {
  .btn-group > .btn:not(:last-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }
  
  .btn-group > .btn:not(:first-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }
  
  .text-start {
    text-align: right !important;
  }
  
  .text-end {
    text-align: left !important;
  }
  
  .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
  }
  
  .me-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
  }
}
