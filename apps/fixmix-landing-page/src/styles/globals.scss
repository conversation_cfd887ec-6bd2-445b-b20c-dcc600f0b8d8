@tailwind base;
@tailwind components;
@tailwind utilities;

// Import design system
@import './design-system.scss';
@import './cards.scss';
// CSS Custom Properties for Navigation
:root {
  --z-sticky: 1000;
  --transition-base: 0.3s ease;
  --transition-fast: 0.15s ease;
  --gray-700: #374151;
  --gray-300: #d1d5db;
  --gray-200: #e5e7eb;
  --font-medium: 500;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --radius-md: 0.375rem;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

@import './hero-enhancements.scss';
@import './animations.scss';
@import './navigation.scss';
@import './faq-enhancements.scss';
@import './accessibility.scss';
@import './i18n.scss';

// Image optimization styles
.image-container {
  position: relative;
  overflow: hidden;

  &.loading {
    .image-loading-overlay {
      opacity: 1;
    }
  }

  &.loaded {
    .image-loading-overlay {
      opacity: 0;
      pointer-events: none;
    }
  }
}

.image-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(243, 244, 246, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.image-placeholder {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.image-error-fallback {
  border: 1px dashed #d1d5db;
  border-radius: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

#main-container {
  width: 100%;
  overflow-x: clip;
}

.my-lg {
  margin-top: 1em;
  margin-bottom: 0.75em;
}

.text-sm {
  font-size: 0.80em;
}

.h2-lg {
  font-size: 30px !important;
}

a {
  text-decoration: none;
}

// Accordion Style overrides
.accordion-button:focus {
  outline: none;
  box-shadow: none;
}

.accordion-button {
  font-weight: 600;
}

.bg-primary {
  background-color: #0096c7 !important;
}

.text-primary {
  color: #0096c7 !important;
}

.navbar-brand {
 margin-left: 20px;
}

button.accordion-button:not(.collapsed) {
  background-color: transparent;
  color: #212529;
  border-color: #212529;
}

.youtube-video {
  aspect-ratio: 16 / 9;
  width: 90%;
  position: relative;
  z-index: 3;
  border-radius: 20px;
}

.bg-graident {
  background: rgb(22, 130, 94);
  background: radial-gradient(circle, rgba(22, 130, 94, 1) 0%, rgba(18, 122, 87, 1) 30%, rgba(33, 95, 74, 1) 100%);
}
.bg-blue {
  background: #0096c7;
}

.social-icon svg:hover {
  color: #cee1d8;
}

@media (min-width: 768px) { 
  .h2-lg {
    font-size: 48px !important;
  }
  .my-lg {
    margin-top: 1.75em;
    margin-bottom: 1em;
  }
  .youtube-video {
    aspect-ratio: 16 / 9;
    width: 60%;
  }
}

.phone_shadow {
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.5))
}
.phone_shadow_2 {
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.25))
}

#section_footer {
  width: 100%;
  background: #0096c7;

  a, span {
    color: #fff;
    font-size: .75em;
    text-decoration: none;
  }
  
  a:hover {
    text-decoration: underline;
  }

  p {
    color: #fff;
    font-size: .5em;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slideUp 1s ease-out;
}

// RTL-specific styles for FAQ accordion
.rtl-accordion-button {
  &::after {
    margin-left: 0 !important;
    margin-right: auto !important;
    transform: rotate(-135deg) !important; // Arrow for collapsed state (moved 5 degrees clockwise)
    left: 1.25rem !important;
    right: auto !important;
  }

  &:not(.collapsed)::after {
    transform: rotate(45deg) !important; // Arrow for expanded state (moved 5 degrees clockwise)
  }
}

// RTL accordion fixes
[dir="rtl"] {
  .accordion-button {
    text-align: right !important;
    padding-right: 3rem !important;
    padding-left: 1.25rem !important;

    &::after {
      margin-left: 0 !important;
      margin-right: auto !important;
      transform: rotate(-135deg) !important; // Arrow for collapsed state (moved 5 degrees clockwise)
      left: 1.25rem !important;
      right: auto !important;
      position: absolute;
    }

    &:not(.collapsed)::after {
      transform: rotate(45deg) !important; // Arrow for expanded state (moved 5 degrees clockwise)
    }
  }

  .accordion-body {
    text-align: right !important;
    padding-right: 1.25rem !important;
    padding-left: 1.25rem !important;
  }

  .accordion-item {
    text-align: right !important;
  }

  .accordion-header {
    text-align: right !important;
  }

  // Ensure proper text alignment for all FAQ content
  .accordion-collapse {
    text-align: right !important;
  }
}