// FixMix Design System
// Modern, consistent design tokens for the landing page

// ===== SPACING SYSTEM =====
:root {
  // Spacing scale (8px base)
  --spacing-xs: 0.25rem;    // 4px
  --spacing-sm: 0.5rem;     // 8px
  --spacing-md: 1rem;       // 16px
  --spacing-lg: 1.5rem;     // 24px
  --spacing-xl: 2rem;       // 32px
  --spacing-2xl: 3rem;      // 48px
  --spacing-3xl: 4rem;      // 64px
  --spacing-4xl: 6rem;      // 96px
  --spacing-5xl: 8rem;      // 128px

  // Layout spacing
  --container-padding: var(--spacing-lg);
  --section-padding: var(--spacing-4xl);
  --component-gap: var(--spacing-xl);

  // ===== TYPOGRAPHY =====
  // Font sizes (modular scale)
  --text-xs: 0.75rem;       // 12px
  --text-sm: 0.875rem;      // 14px
  --text-base: 1rem;        // 16px
  --text-lg: 1.125rem;      // 18px
  --text-xl: 1.25rem;       // 20px
  --text-2xl: 1.5rem;       // 24px
  --text-3xl: 1.875rem;     // 30px
  --text-4xl: 2.25rem;      // 36px
  --text-5xl: 3rem;         // 48px
  --text-6xl: 3.75rem;      // 60px

  // Line heights
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;

  // Font weights
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  // ===== COLORS =====
  // Keep existing FixMix colors but enhance with shades
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;    // Main primary
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  // Grays
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  // Semantic colors
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: var(--primary-500);

  // ===== SHADOWS =====
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  // ===== BORDER RADIUS =====
  --radius-sm: 0.25rem;      // 4px
  --radius-base: 0.375rem;   // 6px
  --radius-md: 0.5rem;       // 8px
  --radius-lg: 0.75rem;      // 12px
  --radius-xl: 1rem;         // 16px
  --radius-2xl: 1.5rem;      // 24px
  --radius-full: 9999px;

  // ===== TRANSITIONS =====
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  // ===== BREAKPOINTS =====
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;

  // ===== Z-INDEX SCALE =====
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

// ===== UTILITY CLASSES =====

// Spacing utilities
.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }
.p-2xl { padding: var(--spacing-2xl) !important; }
.p-3xl { padding: var(--spacing-3xl) !important; }

.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }
.m-2xl { margin: var(--spacing-2xl) !important; }
.m-3xl { margin: var(--spacing-3xl) !important; }

// Gap utilities
.gap-xs { gap: var(--spacing-xs) !important; }
.gap-sm { gap: var(--spacing-sm) !important; }
.gap-md { gap: var(--spacing-md) !important; }
.gap-lg { gap: var(--spacing-lg) !important; }
.gap-xl { gap: var(--spacing-xl) !important; }
.gap-2xl { gap: var(--spacing-2xl) !important; }

// Shadow utilities
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-base { box-shadow: var(--shadow-base) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--shadow-2xl) !important; }

// Border radius utilities
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-base { border-radius: var(--radius-base) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

// Transition utilities
.transition-fast { transition: all var(--transition-fast) !important; }
.transition-base { transition: all var(--transition-base) !important; }
.transition-slow { transition: all var(--transition-slow) !important; }

// ===== LAYOUT GRID =====
.layout-grid {
  display: grid;
  gap: var(--component-gap);
  
  &.grid-1 { grid-template-columns: 1fr; }
  &.grid-2 { grid-template-columns: repeat(2, 1fr); }
  &.grid-3 { grid-template-columns: repeat(3, 1fr); }
  &.grid-4 { grid-template-columns: repeat(4, 1fr); }
  
  // Responsive grid
  @media (max-width: 768px) {
    &.grid-2, &.grid-3, &.grid-4 {
      grid-template-columns: 1fr;
    }
  }
  
  @media (min-width: 769px) and (max-width: 1024px) {
    &.grid-3, &.grid-4 {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

// ===== SECTION SPACING =====
.section {
  padding: var(--section-padding) 0;
  
  &.section-sm {
    padding: var(--spacing-3xl) 0;
  }
  
  &.section-lg {
    padding: var(--spacing-5xl) 0;
  }
}

// ===== CONTAINER IMPROVEMENTS =====
.container {
  padding-left: var(--container-padding);
  padding-right: var(--container-padding);
}

// ===== RESPONSIVE UTILITIES =====
@media (max-width: 576px) {
  :root {
    --section-padding: var(--spacing-3xl);
    --container-padding: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  :root {
    --component-gap: var(--spacing-lg);
  }
}
