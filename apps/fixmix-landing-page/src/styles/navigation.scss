// Sticky Navigation Styles for FixMix Landing Page

// ===== STICKY NAVIGATION =====
.sticky-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: var(--z-sticky);
  transform: translateY(-100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  opacity: 0;

  &.sticky-nav-visible {
    transform: translateY(0);
    opacity: 1;
    animation: slideInFromTop 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .container {
    padding-top: 0;
    padding-bottom: 0;
  }
}

.sticky-nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  min-height: 70px;
}

.sticky-nav-logo {
  cursor: pointer;
  transition: all var(--transition-base);
  
  &:hover {
    transform: scale(1.05);
  }
}

.sticky-nav-links {
  display: flex;
  align-items: center;
  gap: 2rem;
  
  .nav-link {
    background: none;
    border: none;
    color: var(--gray-700);
    font-weight: var(--font-medium);
    font-size: var(--text-base);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-base);
    cursor: pointer;
    position: relative;
    
    &:hover {
      color: var(--primary-600);
      background: var(--primary-50);
    }
    
    &.active {
      color: var(--primary-600);
      background: var(--primary-100);
      
      &::after {
        content: '';
        position: absolute;
        bottom: -1rem;
        left: 50%;
        transform: translateX(-50%);
        width: 4px;
        height: 4px;
        background: var(--primary-600);
        border-radius: 50%;
      }
    }
    
    &:focus {
      outline: 2px solid var(--primary-500);
      outline-offset: 2px;
    }
  }
}

.sticky-nav-cta {
  .btn {
    padding: 0.5rem 1.5rem;
    font-weight: var(--font-medium);
    transition: all var(--transition-base);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }
  }
}

// ===== MOBILE MENU =====
.mobile-menu-toggle {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  
  &:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
  }
}

.hamburger {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  position: relative;
  
  span {
    display: block;
    height: 2px;
    width: 100%;
    background: var(--gray-700);
    border-radius: 1px;
    transition: all var(--transition-base);
    
    &:nth-child(1) {
      transform-origin: top left;
    }
    
    &:nth-child(2) {
      margin: 6px 0;
    }
    
    &:nth-child(3) {
      transform-origin: bottom left;
    }
  }
  
  &.active {
    span {
      &:nth-child(1) {
        transform: rotate(45deg) translate(2px, -2px);
      }
      
      &:nth-child(2) {
        opacity: 0;
        transform: translateX(-10px);
      }
      
      &:nth-child(3) {
        transform: rotate(-45deg) translate(2px, 2px);
      }
    }
  }
}

.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  
  &.mobile-menu-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
}

.mobile-menu-content {
  padding: 1.5rem 0;
  
  .mobile-nav-link {
    display: block;
    width: 100%;
    background: none;
    border: none;
    color: var(--gray-700);
    font-weight: var(--font-medium);
    font-size: var(--text-lg);
    padding: 1rem 1.5rem;
    text-align: left;
    transition: all var(--transition-base);
    cursor: pointer;
    
    &:hover {
      color: var(--primary-600);
      background: var(--primary-50);
    }
    
    &.active {
      color: var(--primary-600);
      background: var(--primary-100);
      border-left: 4px solid var(--primary-600);
    }
    
    &:focus {
      outline: 2px solid var(--primary-500);
      outline-offset: -2px;
    }
  }
  
  .mobile-menu-cta {
    padding: 1rem 1.5rem 0;
    border-top: 1px solid var(--gray-200);
    margin-top: 1rem;
  }
}

// ===== SMOOTH SCROLL BEHAVIOR =====
html {
  scroll-behavior: smooth;
}

// ===== NAVBAR ANIMATIONS =====
@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

// Override for users who prefer reduced motion
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

// ===== TOUCH OPTIMIZATIONS =====
@media (max-width: 768px) {
  .sticky-nav-content {
    padding: 0.75rem 0;
    min-height: 60px;
  }
  
  .mobile-nav-link {
    min-height: 48px; // Touch-friendly target size
    display: flex;
    align-items: center;
  }
  
  .mobile-menu-toggle {
    min-width: 48px;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// ===== ACCESSIBILITY =====
@media (prefers-reduced-motion: reduce) {
  .sticky-nav,
  .mobile-menu,
  .hamburger span,
  .nav-link,
  .mobile-nav-link {
    transition: none !important;
  }
  
  .sticky-nav-logo:hover,
  .sticky-nav-cta .btn:hover {
    transform: none !important;
  }
}

// ===== FOCUS MANAGEMENT =====
.sticky-nav {
  // Ensure focus is visible
  *:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
  }
  
  // Skip to content link
  .skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-600);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-md);
    z-index: 10000;
    transition: top var(--transition-fast);
    
    &:focus {
      top: 6px;
    }
  }
}

// ===== BACKDROP BLUR FALLBACK =====
@supports not (backdrop-filter: blur(10px)) {
  .sticky-nav {
    background: rgba(255, 255, 255, 0.98);
  }
}

// ===== DARK MODE SUPPORT (Future) =====
@media (prefers-color-scheme: dark) {
  .sticky-nav {
    background: rgba(17, 24, 39, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
    
    .nav-link {
      color: var(--gray-300);
      
      &:hover {
        color: var(--primary-400);
        background: rgba(59, 130, 246, 0.1);
      }
      
      &.active {
        color: var(--primary-400);
        background: rgba(59, 130, 246, 0.2);
      }
    }
    
    .mobile-nav-link {
      color: var(--gray-300);
      
      &:hover {
        color: var(--primary-400);
        background: rgba(59, 130, 246, 0.1);
      }
      
      &.active {
        color: var(--primary-400);
        background: rgba(59, 130, 246, 0.2);
      }
    }
    
    .hamburger span {
      background: var(--gray-300);
    }
    
    .mobile-menu {
      background: rgb(17, 24, 39);
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }
  }
}
