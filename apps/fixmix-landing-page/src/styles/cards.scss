// Modern Card Styles for FixMix Landing Page

// ===== BASE CARD STYLES =====
.card-modern {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-base);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: var(--transition-base);
  position: relative;
  
  // Hover effects
  &.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-200);
  }
  
  // Clickable cards
  &.card-clickable {
    cursor: pointer;
    
    &:focus {
      outline: 2px solid var(--primary-500);
      outline-offset: 2px;
    }
    
    &:active {
      transform: translateY(-2px);
    }
  }
}

// ===== CARD SIZES =====
.card-sm {
  padding: var(--spacing-md);
}

.card-md {
  padding: var(--spacing-lg);
}

.card-lg {
  padding: var(--spacing-xl);
}

// ===== CARD VARIANTS =====

// Default card
.card-default {
  // Uses base styles
}

// Feature card
.card-feature {
  padding: var(--spacing-xl);
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  
  .card-icon {
    margin-bottom: var(--spacing-lg);
    display: flex;
    justify-content: center;
    align-items: center;
    
    .icon-image {
      width: 64px;
      height: 64px;
      object-fit: contain;
    }
    
    // Icon background circle
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
      border-radius: var(--radius-full);
      z-index: -1;
      transition: var(--transition-base);
    }
  }
  
  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .card-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
    line-height: var(--leading-tight);
  }
  
  .card-description {
    color: var(--gray-600);
    line-height: var(--leading-relaxed);
    flex: 1;
    margin: 0;
  }
  
  // Hover effect for icon
  &:hover .card-icon::before {
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    transform: scale(1.1);
  }
}

// Testimonial card
.card-testimonial {
  padding: var(--spacing-xl);
  position: relative;
  
  .testimonial-content {
    position: relative;
    z-index: 1;
  }
  
  .testimonial-rating {
    margin-bottom: var(--spacing-md);
    
    .star {
      font-size: var(--text-lg);
      margin-right: var(--spacing-xs);
      
      &.star-filled {
        color: #fbbf24; // Yellow
      }
      
      &.star-empty {
        color: var(--gray-300);
      }
    }
  }
  
  .testimonial-quote {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--gray-700);
    margin-bottom: var(--spacing-lg);
    font-style: italic;
    position: relative;
    
    &::before {
      content: '"';
      font-size: 4rem;
      color: var(--primary-200);
      position: absolute;
      top: -1rem;
      left: -1rem;
      font-family: serif;
      line-height: 1;
    }
  }
  
  .testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    
    .author-avatar {
      width: 48px;
      height: 48px;
      border-radius: var(--radius-full);
      object-fit: cover;
      border: 2px solid var(--gray-200);
    }
    
    .author-info {
      flex: 1;
      
      .author-name {
        display: block;
        font-weight: var(--font-semibold);
        color: var(--gray-900);
        font-style: normal;
      }
      
      .author-role {
        color: var(--gray-500);
        font-size: var(--text-sm);
      }
    }
  }
  
  // Background decoration
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-50), transparent);
    border-radius: 0 var(--radius-lg) 0 100%;
    z-index: 0;
  }
}

// Service card
.card-service {
  padding: 0;
  overflow: hidden;
  
  .service-image {
    height: 200px;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: var(--transition-base);
    }
  }
  
  .service-content {
    padding: var(--spacing-xl);
  }
  
  .service-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
  }
  
  .service-description {
    color: var(--gray-600);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--spacing-lg);
  }
  
  .service-features {
    list-style: none;
    padding: 0;
    margin-bottom: var(--spacing-lg);
    
    .feature-item {
      display: flex;
      align-items: center;
      margin-bottom: var(--spacing-sm);
      
      .feature-check {
        color: var(--success);
        font-weight: var(--font-bold);
        margin-right: var(--spacing-sm);
      }
    }
  }
  
  .service-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .service-price {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--primary-600);
    }
  }
  
  // Hover effect for image
  &:hover .service-image img {
    transform: scale(1.05);
  }
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .card-feature {
    padding: var(--spacing-lg);
    
    .card-icon .icon-image {
      width: 48px;
      height: 48px;
    }
    
    .card-icon::before {
      width: 64px;
      height: 64px;
    }
    
    .card-title {
      font-size: var(--text-lg);
    }
  }
  
  .card-testimonial {
    padding: var(--spacing-lg);
    
    .testimonial-quote {
      font-size: var(--text-base);
      
      &::before {
        font-size: 3rem;
        top: -0.5rem;
        left: -0.5rem;
      }
    }
  }
  
  .card-service {
    .service-image {
      height: 160px;
    }
    
    .service-content {
      padding: var(--spacing-lg);
    }
    
    .service-footer {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: stretch;
    }
  }
}

// ===== ACCESSIBILITY =====
@media (prefers-reduced-motion: reduce) {
  .card-modern {
    transition: none;
    
    &.card-hover:hover {
      transform: none;
    }
    
    &.card-clickable:active {
      transform: none;
    }
  }
  
  .card-feature .card-icon::before {
    transition: none;
  }
  
  .card-service .service-image img {
    transition: none;
  }
}
