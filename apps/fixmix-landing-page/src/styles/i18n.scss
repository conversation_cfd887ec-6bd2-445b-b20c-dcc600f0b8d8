// Internationalization Styles for FixMix Landing Page
// Preparing for Arabic (RTL) support

// ===== RTL LAYOUT SUPPORT =====

[dir="rtl"] {
  // Text alignment
  .text-start {
    text-align: right !important;
  }
  
  .text-end {
    text-align: left !important;
  }
  
  // Margin utilities
  .ms-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
  .ms-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
  .ms-3 { margin-right: 1rem !important; margin-left: 0 !important; }
  .ms-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
  .ms-5 { margin-right: 3rem !important; margin-left: 0 !important; }
  .ms-auto { margin-right: auto !important; margin-left: 0 !important; }
  
  .me-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
  .me-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
  .me-3 { margin-left: 1rem !important; margin-right: 0 !important; }
  .me-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
  .me-5 { margin-left: 3rem !important; margin-right: 0 !important; }
  .me-auto { margin-left: auto !important; margin-right: 0 !important; }
  
  // Padding utilities
  .ps-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
  .ps-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
  .ps-3 { padding-right: 1rem !important; padding-left: 0 !important; }
  .ps-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }
  .ps-5 { padding-right: 3rem !important; padding-left: 0 !important; }
  
  .pe-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
  .pe-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
  .pe-3 { padding-left: 1rem !important; padding-right: 0 !important; }
  .pe-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }
  .pe-5 { padding-left: 3rem !important; padding-right: 0 !important; }
  
  // Float utilities
  .float-start { float: right !important; }
  .float-end { float: left !important; }
  
  // Border radius
  .rounded-start {
    border-top-right-radius: var(--bs-border-radius) !important;
    border-bottom-right-radius: var(--bs-border-radius) !important;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
  
  .rounded-end {
    border-top-left-radius: var(--bs-border-radius) !important;
    border-bottom-left-radius: var(--bs-border-radius) !important;
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
}

// ===== COMPONENT RTL ADJUSTMENTS =====

[dir="rtl"] {
  // Navigation
  .sticky-nav-links {
    .nav-link {
      &.active::after {
        left: auto;
        right: 50%;
        transform: translateX(50%);
      }
    }
  }
  
  // Hamburger menu
  .hamburger {
    span {
      &:nth-child(1) {
        transform-origin: top right;
      }
      
      &:nth-child(3) {
        transform-origin: bottom right;
      }
    }
    
    &.active {
      span {
        &:nth-child(1) {
          transform: rotate(-45deg) translate(-2px, -2px);
        }
        
        &:nth-child(3) {
          transform: rotate(45deg) translate(-2px, 2px);
        }
      }
    }
  }
  
  // Mobile menu
  .mobile-nav-link {
    &.active {
      border-left: none;
      border-right: 4px solid var(--primary-600);
    }
  }
  
  // Cards
  .card-modern {
    .card-icon {
      &::before {
        left: auto;
        right: 50%;
        transform: translate(50%, -50%);
      }
    }
  }
  
  // Buttons with icons
  .btn {
    .icon-start {
      order: 2;
      margin-left: 0.5rem;
      margin-right: 0;
    }
    
    .icon-end {
      order: 0;
      margin-right: 0.5rem;
      margin-left: 0;
    }
  }
  
  // Accordion
  .accordion-button {
    &::after {
      margin-left: 0;
      margin-right: auto;
    }
  }
  
  // Dropdown
  .dropdown-menu {
    left: auto;
    right: 0;
  }
  
  // Phone mockups (flip horizontally for RTL)
  .phone_shadow {
    transform: scaleX(-1);
    
    &:hover {
      transform: scaleX(-1) translateY(-8px) scale(1.02);
    }
  }
}

// ===== ARABIC FONT SUPPORT =====

[lang="ar"] {
  font-family: 'Noto Sans Arabic', 'Cairo', 'Amiri', 'Tajawal', sans-serif;
  
  // Adjust line height for Arabic text
  line-height: 1.8;
  
  // Headings
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600; // Arabic fonts often look better with medium weight
    line-height: 1.6;
  }
  
  // Body text
  p, .lead {
    line-height: 1.8;
  }
  
  // Buttons
  .btn {
    font-weight: 500;
    letter-spacing: 0; // Remove letter spacing for Arabic
  }
  
  // Navigation
  .nav-link {
    font-weight: 500;
  }
}

// ===== LANGUAGE SWITCHER =====

.language-switcher {
  position: relative;
  
  .dropdown-menu {
    min-width: 150px;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    
    .dropdown-item {
      padding: 0.5rem 1rem;
      display: flex;
      align-items: center;
      transition: all var(--transition-base);
      
      &:hover {
        background-color: var(--primary-50);
        color: var(--primary-700);
      }
      
      &.active {
        background-color: var(--primary-100);
        color: var(--primary-700);
        font-weight: var(--font-medium);
      }
      
      .flag-icon {
        font-size: 1.2em;
      }
      
      .language-name {
        flex: 1;
      }
    }
  }
  
  .btn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    
    .flag-icon {
      font-size: 1.1em;
    }
    
    .language-code {
      font-weight: var(--font-medium);
      font-size: 0.875rem;
    }
  }
}

// ===== RESPONSIVE RTL ADJUSTMENTS =====

@media (max-width: 768px) {
  [dir="rtl"] {
    // Mobile navigation adjustments
    .mobile-menu-content {
      text-align: right;
    }
    
    // Hero section
    .hero-additional-ctas {
      .btn {
        text-align: center;
      }
    }
    
    // Cards
    .card-feature {
      text-align: center; // Keep centered on mobile for both LTR and RTL
    }
  }
}

// ===== FONT LOADING =====

// Preload Arabic fonts when needed
@font-face {
  font-family: 'Noto Sans Arabic';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap');
}

// ===== ACCESSIBILITY FOR RTL =====

[dir="rtl"] {
  // Ensure focus indicators work correctly in RTL
  *:focus {
    outline-offset: 2px;
  }
  
  // Skip to content link
  .skip-to-content {
    left: auto;
    right: 6px;
    
    &:focus {
      right: 6px;
    }
  }
  
  // Scroll progress (flip for RTL)
  .scroll-progress {
    left: auto;
    right: 0;
    transform-origin: right;
  }
}

// ===== PRINT STYLES FOR RTL =====

@media print {
  [dir="rtl"] {
    // Ensure proper printing for RTL content
    body {
      direction: rtl;
    }
    
    .text-start {
      text-align: right !important;
    }
    
    .text-end {
      text-align: left !important;
    }
  }
}
