// Enhanced FAQ Styles for FixMix Landing Page

// ===== FAQ ACCORDION ENHANCEMENTS =====
#faq_accordion {
  .accordion-item {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    transition: all var(--transition-base);
    background: white;
    box-shadow: var(--shadow-sm);
    
    &:hover {
      box-shadow: var(--shadow-md);
      border-color: var(--primary-200);
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .accordion-header {
    margin-bottom: 0;
    
    .accordion-button {
      background: white;
      border: none;
      padding: var(--spacing-lg) var(--spacing-xl);
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--gray-900);
      text-align: left;
      transition: all var(--transition-base);
      position: relative;
      
      &:not(.collapsed) {
        background: var(--primary-50);
        color: var(--primary-700);
        box-shadow: none;
      }
      
      &:focus {
        box-shadow: 0 0 0 2px var(--primary-500);
        border-color: transparent;
      }
      
      &:hover {
        background: var(--gray-50);
        
        &:not(.collapsed) {
          background: var(--primary-100);
        }
      }
      
      // Custom arrow
      &::after {
        content: '';
        width: 12px;
        height: 12px;
        border: 2px solid currentColor;
        border-left: 0;
        border-top: 0;
        transform: rotate(45deg);
        transition: transform var(--transition-base);
        background: none;
        margin-left: auto;
        flex-shrink: 0;
      }

      &.collapsed::after {
        transform: rotate(-135deg);
      }

      // RTL support for Arabic
      [dir="rtl"] & {
        &::after {
          margin-left: 0;
          margin-right: auto;
          order: -1;
        }
      }
    }
  }
  
  .accordion-collapse {
    border-top: 1px solid var(--gray-200);
    
    .accordion-body {
      padding: var(--spacing-lg) var(--spacing-xl);
      color: var(--gray-600);
      line-height: var(--leading-relaxed);
      font-size: var(--text-base);
      
      // Add some visual enhancements
      p {
        margin-bottom: var(--spacing-md);
        
        &:last-child {
          margin-bottom: 0;
        }
      }
      
      // Style links in FAQ answers
      a {
        color: var(--primary-600);
        text-decoration: none;
        font-weight: var(--font-medium);
        
        &:hover {
          text-decoration: underline;
          color: var(--primary-700);
        }
      }
      
      // Style lists in FAQ answers
      ul, ol {
        padding-left: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
        
        li {
          margin-bottom: var(--spacing-sm);
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      
      // Style code snippets if any
      code {
        background: var(--gray-100);
        padding: 0.2em 0.4em;
        border-radius: var(--radius-sm);
        font-size: 0.9em;
        color: var(--gray-800);
      }
    }
  }
}

// ===== FAQ SECTION LAYOUT =====
.faq-section {
  .faq-intro {
    text-align: center;
    margin-bottom: var(--spacing-4xl);
    
    .faq-title {
      font-size: clamp(2rem, 4vw, 2.5rem);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--spacing-lg);
    }
    
    .faq-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      max-width: 600px;
      margin: 0 auto;
      line-height: var(--leading-relaxed);
    }
  }
  
  .faq-grid {
    display: grid;
    gap: var(--spacing-xl);
    
    // Two-column layout for larger screens
    @media (min-width: 992px) {
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-2xl);
    }
  }
  
  .faq-column {
    .accordion-item {
      margin-bottom: var(--spacing-lg);
    }
  }
}

// ===== SEARCH FAQ FUNCTIONALITY =====
.faq-search {
  margin-bottom: var(--spacing-3xl);
  
  .search-input {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    transition: all var(--transition-base);
    
    &:focus {
      outline: none;
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    &::placeholder {
      color: var(--gray-400);
    }
  }
}

// ===== ANIMATIONS =====
.accordion-item {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease-out forwards;
  
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
  &:nth-child(5) { animation-delay: 0.5s; }
  &:nth-child(6) { animation-delay: 0.6s; }
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  #faq_accordion {
    .accordion-header {
      .accordion-button {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--text-base);
      }
    }
    
    .accordion-collapse {
      .accordion-body {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--text-sm);
      }
    }
  }
  
  .faq-section {
    .faq-intro {
      margin-bottom: var(--spacing-3xl);
      
      .faq-title {
        font-size: 1.75rem;
      }
      
      .faq-subtitle {
        font-size: var(--text-base);
      }
    }
  }
}

// ===== ACCESSIBILITY =====
@media (prefers-reduced-motion: reduce) {
  .accordion-item {
    animation: none;
    opacity: 1;
    transform: none;
  }
  
  .accordion-button::after {
    transition: none;
  }
}

// ===== HIGH CONTRAST MODE =====
@media (prefers-contrast: high) {
  #faq_accordion {
    .accordion-item {
      border-color: var(--gray-900);
    }
    
    .accordion-button {
      border: 2px solid var(--gray-900);
      
      &:not(.collapsed) {
        background: var(--primary-600);
        color: white;
      }
    }
  }
}

// ===== PRINT STYLES =====
@media print {
  #faq_accordion {
    .accordion-item {
      break-inside: avoid;
      box-shadow: none;
      border: 1px solid #000;
    }
    
    .accordion-collapse {
      display: block !important;
    }
    
    .accordion-button::after {
      display: none;
    }
  }
}
