// Enhanced Hero Section Styles for FixMix Landing Page

// ===== HERO ENHANCEMENTS =====

.hero-badge {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3.5rem) !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin-bottom: 1.5rem !important;
  animation: fadeInUp 0.8s ease-out 0.4s both;
  
  // Add text shadow for better readability
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: clamp(1rem, 2.5vw, 1.25rem) !important;
  line-height: 1.6 !important;
  opacity: 0.9;
  animation: fadeInUp 0.8s ease-out 0.6s both;
  
  // Add text shadow for better readability
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.hero-additional-ctas {
  animation: fadeInUp 0.8s ease-out 0.8s both;
  
  .btn {
    transition: all var(--transition-base);
    
    &:hover {
      transform: translateY(-2px);
    }
    
    &.btn-outline-light {
      border-width: 2px;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: white;
        box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
      }
    }
    
    &.btn-link {
      &:hover {
        text-decoration: underline !important;
      }
    }
  }
}



// ===== PHONE MOCKUP ENHANCEMENTS =====

.phone_shadow {
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3)) !important;
  transition: all var(--transition-slow);

  // Restore original bottom-to-top animation
  animation: slideInFromBottom 1s ease-out both;

  &:hover {
    transform: translateY(-8px) scale(1.02);
    filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.4)) !important;
  }
}

// Staggered animation for phone images (original timing)
.phone_shadow:nth-child(1) {
  animation-delay: 0.5s;
}

.phone_shadow:nth-child(2) {
  animation-delay: 0.7s;
}

.phone_shadow:nth-child(3) {
  animation-delay: 0.9s;
}

// ===== ANIMATIONS =====

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(100px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// Remove floating animation - keep original slide-in only

// ===== RESPONSIVE IMPROVEMENTS =====

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem !important;
    margin-bottom: 1rem !important;
  }
  
  .hero-subtitle {
    font-size: 1.1rem !important;
    margin-bottom: 2rem !important;
  }
  
  .hero-additional-ctas {
    .btn {
      width: 100%;
      margin-bottom: 0.5rem;
      
      &.btn-link {
        width: auto;
        margin-bottom: 0;
      }
    }
  }
  

  
  // Keep slide-in animation on mobile
  .phone_shadow {
    // Keep original slide-in animation
  }
}

@media (max-width: 576px) {
  .hero-badge {
    .badge {
      font-size: 0.75rem;
      padding: 0.5rem 1rem;
    }
  }
  
  .hero-title {
    font-size: 2rem !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  
  .hero-subtitle {
    font-size: 1rem !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  

}

// ===== ACCESSIBILITY =====

@media (prefers-reduced-motion: reduce) {
  .hero-badge,
  .hero-title,
  .hero-subtitle,
  .hero-additional-ctas,
  .hero-stats,
  .phone_shadow {
    animation: none !important;
  }
  
  .hero-additional-ctas .btn:hover,
  .phone_shadow:hover {
    transform: none !important;
  }
}

// ===== FOCUS STATES =====

.hero-additional-ctas .btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

// ===== BACKGROUND ENHANCEMENTS =====

// Add subtle gradient overlay to improve text readability
#section_headline_container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.05) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
  pointer-events: none;
  z-index: 1;
}

#section_headline {
  position: relative;
  z-index: 2;
}

// ===== BADGE ENHANCEMENTS =====

.hero-badge .badge {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-base);
  font-weight: 600;
  font-size: 0.9rem;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
  }

  // Enhanced red notification styling
  &.bg-danger {
    background-color: rgba(220, 53, 69, 0.15) !important;
    color: #dc3545 !important;
    border-color: rgba(220, 53, 69, 0.3) !important;

    &:hover {
      background-color: rgba(220, 53, 69, 0.25) !important;
      border-color: rgba(220, 53, 69, 0.5) !important;
    }
  }
}
