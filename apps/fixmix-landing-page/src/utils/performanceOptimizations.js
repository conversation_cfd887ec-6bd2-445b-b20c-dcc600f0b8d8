// Performance optimization utilities for FixMix Landing Page

// Preload critical resources
export const preloadCriticalResources = () => {
  if (typeof window === 'undefined') return;

  const criticalResources = [
    // Critical fonts
    { href: '/fonts/Montserrat-Regular.woff2', as: 'font', type: 'font/woff2', crossorigin: 'anonymous' },
    { href: '/fonts/Montserrat-Bold.woff2', as: 'font', type: 'font/woff2', crossorigin: 'anonymous' },
    
    // Critical images
    { href: '/images/FixMix.png', as: 'image' },
    { href: '/images/phones/Home.png', as: 'image' },
    { href: '/images/phones/Splash Screen.png', as: 'image' },
    
    // Critical CSS
    { href: '/styles/critical.css', as: 'style' }
  ];

  criticalResources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource.href;
    link.as = resource.as;
    if (resource.type) link.type = resource.type;
    if (resource.crossorigin) link.crossOrigin = resource.crossorigin;
    document.head.appendChild(link);
  });
};

// Optimize third-party scripts
export const optimizeThirdPartyScripts = () => {
  // Defer non-critical scripts
  const deferScripts = [
    'https://www.googletagmanager.com/gtag/js',
    'https://connect.facebook.net/en_US/fbevents.js'
  ];

  deferScripts.forEach(src => {
    const script = document.createElement('script');
    script.src = src;
    script.defer = true;
    script.async = true;
    document.head.appendChild(script);
  });
};

// Implement service worker for caching
export const registerServiceWorker = async () => {
  if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered successfully:', registration);
      
      // Update service worker when new version is available
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New content is available, prompt user to refresh
            if (confirm('New version available! Refresh to update?')) {
              window.location.reload();
            }
          }
        });
      });
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  }
};

// Optimize CSS delivery
export const optimizeCSSDelivery = () => {
  // Load non-critical CSS asynchronously
  const nonCriticalCSS = [
    '/styles/animations.css',
    '/styles/components.css'
  ];

  nonCriticalCSS.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.media = 'print';
    link.onload = function() { this.media = 'all'; };
    document.head.appendChild(link);
  });
};

// Implement resource hints
export const addResourceHints = () => {
  const hints = [
    // DNS prefetch for external domains
    { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
    { rel: 'dns-prefetch', href: '//www.google-analytics.com' },
    { rel: 'dns-prefetch', href: '//connect.facebook.net' },
    
    // Preconnect to critical origins
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true },
    
    // Prefetch likely next pages
    { rel: 'prefetch', href: '/ar' },
    { rel: 'prefetch', href: '/privacy' },
    { rel: 'prefetch', href: '/terms' }
  ];

  hints.forEach(hint => {
    const link = document.createElement('link');
    link.rel = hint.rel;
    link.href = hint.href;
    if (hint.crossorigin) link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });
};

// Optimize images with intersection observer
export const optimizeImageLoading = () => {
  if (typeof window === 'undefined') return;

  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        
        // Load high-res image
        if (img.dataset.src) {
          img.src = img.dataset.src;
          img.classList.remove('lazy');
          img.classList.add('loaded');
        }
        
        // Load background images
        if (img.dataset.bg) {
          img.style.backgroundImage = `url(${img.dataset.bg})`;
        }
        
        imageObserver.unobserve(img);
      }
    });
  }, {
    rootMargin: '50px 0px',
    threshold: 0.01
  });

  // Observe all lazy images
  document.querySelectorAll('img[data-src], [data-bg]').forEach(img => {
    imageObserver.observe(img);
  });
};

// Bundle splitting optimization
export const optimizeBundleLoading = () => {
  // Dynamically import heavy components
  const loadHeavyComponents = async () => {
    const { default: AnimatedSection } = await import('@/components/AnimatedSection');
    const { default: Reviews } = await import('@/components/Reviews');
    return { AnimatedSection, Reviews };
  };

  // Preload components that will be needed soon
  const preloadComponents = () => {
    import('@/components/FAQ');
    import('@/components/DownloadSection');
  };

  return { loadHeavyComponents, preloadComponents };
};

// Memory optimization
export const optimizeMemoryUsage = () => {
  // Clean up event listeners
  const cleanupListeners = [];
  
  const addCleanupListener = (element, event, handler) => {
    element.addEventListener(event, handler);
    cleanupListeners.push(() => element.removeEventListener(event, handler));
  };

  // Cleanup function
  const cleanup = () => {
    cleanupListeners.forEach(cleanup => cleanup());
    cleanupListeners.length = 0;
  };

  // Auto cleanup on page unload
  window.addEventListener('beforeunload', cleanup);

  return { addCleanupListener, cleanup };
};

// Critical rendering path optimization
export const optimizeCriticalRenderingPath = () => {
  // Inline critical CSS
  const inlineCriticalCSS = () => {
    const criticalCSS = `
      /* Critical above-the-fold styles */
      body { font-family: 'Montserrat', sans-serif; margin: 0; }
      .hero-section { min-height: 100vh; }
      .navbar { background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
      .btn-primary { background: #3B82F6; border: none; }
    `;
    
    const style = document.createElement('style');
    style.textContent = criticalCSS;
    document.head.appendChild(style);
  };

  // Defer non-critical CSS
  const deferNonCriticalCSS = () => {
    const nonCriticalLinks = document.querySelectorAll('link[rel="stylesheet"]:not([data-critical])');
    nonCriticalLinks.forEach(link => {
      link.media = 'print';
      link.onload = function() { this.media = 'all'; };
    });
  };

  return { inlineCriticalCSS, deferNonCriticalCSS };
};

// Initialize all performance optimizations
export const initPerformanceOptimizations = () => {
  if (typeof window === 'undefined') return;

  // Run immediately
  preloadCriticalResources();
  addResourceHints();
  optimizeCSSDelivery();

  // Run after DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    optimizeImageLoading();
    optimizeThirdPartyScripts();
    optimizeMemoryUsage();
  });

  // Run after page load
  window.addEventListener('load', () => {
    registerServiceWorker();
    const { preloadComponents } = optimizeBundleLoading();
    
    // Preload components after a short delay
    setTimeout(preloadComponents, 2000);
  });
};

const performanceOptimizations = {
  preloadCriticalResources,
  optimizeThirdPartyScripts,
  registerServiceWorker,
  optimizeCSSDelivery,
  addResourceHints,
  optimizeImageLoading,
  optimizeBundleLoading,
  optimizeMemoryUsage,
  optimizeCriticalRenderingPath,
  initPerformanceOptimizations
};

export default performanceOptimizations;
