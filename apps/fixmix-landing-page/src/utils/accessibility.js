// Accessibility utilities for FixMix Landing Page

// Focus management utilities
export const focusManagement = {
  // Trap focus within a container
  trapFocus: (container) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    return () => container.removeEventListener('keydown', handleTabKey);
  },

  // Focus first element in container
  focusFirst: (container) => {
    const firstFocusable = container.querySelector(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    if (firstFocusable) {
      firstFocusable.focus();
    }
  },

  // Save and restore focus
  saveFocus: () => {
    const activeElement = document.activeElement;
    return () => {
      if (activeElement && typeof activeElement.focus === 'function') {
        activeElement.focus();
      }
    };
  }
};

// Keyboard navigation utilities
export const keyboardNavigation = {
  // Handle arrow key navigation for lists
  handleArrowNavigation: (e, items, currentIndex) => {
    let newIndex = currentIndex;
    
    switch (e.key) {
      case 'ArrowDown':
        newIndex = (currentIndex + 1) % items.length;
        break;
      case 'ArrowUp':
        newIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
        break;
      case 'Home':
        newIndex = 0;
        break;
      case 'End':
        newIndex = items.length - 1;
        break;
      default:
        return currentIndex;
    }
    
    e.preventDefault();
    items[newIndex]?.focus();
    return newIndex;
  },

  // Handle escape key
  handleEscape: (callback) => (e) => {
    if (e.key === 'Escape') {
      callback();
    }
  }
};

// Screen reader utilities
export const screenReader = {
  // Announce message to screen readers
  announce: (message, priority = 'polite') => {
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', priority);
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'visually-hidden';
    announcer.textContent = message;
    
    document.body.appendChild(announcer);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcer);
    }, 1000);
  },

  // Create visually hidden text for screen readers
  createVisuallyHidden: (text) => {
    const span = document.createElement('span');
    span.className = 'visually-hidden';
    span.textContent = text;
    return span;
  }
};

// Color contrast utilities
export const colorContrast = {
  // Calculate contrast ratio between two colors
  getContrastRatio: (color1, color2) => {
    const getLuminance = (color) => {
      const rgb = color.match(/\d+/g);
      const [r, g, b] = rgb.map(c => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };

    const lum1 = getLuminance(color1);
    const lum2 = getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    
    return (brightest + 0.05) / (darkest + 0.05);
  },

  // Check if contrast meets WCAG standards
  meetsWCAG: (color1, color2, level = 'AA') => {
    const ratio = colorContrast.getContrastRatio(color1, color2);
    const thresholds = {
      'AA': 4.5,
      'AAA': 7,
      'AA-large': 3,
      'AAA-large': 4.5
    };
    return ratio >= thresholds[level];
  }
};

// Motion and animation utilities
export const motionUtilities = {
  // Check if user prefers reduced motion
  prefersReducedMotion: () => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  // Respect motion preferences
  respectMotionPreference: (animationConfig) => {
    if (motionUtilities.prefersReducedMotion()) {
      return {
        ...animationConfig,
        duration: 0,
        delay: 0,
        transition: 'none'
      };
    }
    return animationConfig;
  }
};

// Form accessibility utilities
export const formAccessibility = {
  // Associate label with input
  associateLabel: (input, label) => {
    const id = input.id || `input-${Date.now()}`;
    input.id = id;
    label.setAttribute('for', id);
  },

  // Add error message to input
  addErrorMessage: (input, message) => {
    const errorId = `${input.id}-error`;
    let errorElement = document.getElementById(errorId);
    
    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.id = errorId;
      errorElement.className = 'error-message text-danger';
      errorElement.setAttribute('role', 'alert');
      input.parentNode.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
    input.setAttribute('aria-describedby', errorId);
    input.setAttribute('aria-invalid', 'true');
  },

  // Remove error message
  removeErrorMessage: (input) => {
    const errorId = `${input.id}-error`;
    const errorElement = document.getElementById(errorId);
    
    if (errorElement) {
      errorElement.remove();
    }
    
    input.removeAttribute('aria-describedby');
    input.removeAttribute('aria-invalid');
  }
};

// Language and RTL utilities
export const languageUtilities = {
  // Set document language and direction
  setDocumentLanguage: (locale) => {
    document.documentElement.lang = locale;
    document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr';
  },

  // Get appropriate font for language
  getLanguageFont: (locale) => {
    const fonts = {
      'ar': "'Noto Sans Arabic', 'Cairo', 'Amiri', 'Tajawal', sans-serif",
      'en': "'Montserrat', 'Helvetica Neue', Arial, sans-serif"
    };
    return fonts[locale] || fonts['en'];
  }
};

// Initialize accessibility features
export const initAccessibility = () => {
  // Add skip link if not present
  if (!document.querySelector('.skip-link')) {
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.className = 'skip-link visually-hidden-focusable';
    skipLink.textContent = 'Skip to main content';
    document.body.insertBefore(skipLink, document.body.firstChild);
  }

  // Add focus indicators for keyboard navigation
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Tab') {
      document.body.classList.add('keyboard-navigation');
    }
  });

  document.addEventListener('mousedown', () => {
    document.body.classList.remove('keyboard-navigation');
  });

  // Announce page changes to screen readers
  const announcePageChange = () => {
    const title = document.title;
    screenReader.announce(`Page loaded: ${title}`);
  };

  // Listen for route changes (for SPA)
  window.addEventListener('popstate', announcePageChange);
  
  // Initial announcement
  if (document.readyState === 'complete') {
    announcePageChange();
  } else {
    window.addEventListener('load', announcePageChange);
  }
};

export default {
  focusManagement,
  keyboardNavigation,
  screenReader,
  colorContrast,
  motionUtilities,
  formAccessibility,
  languageUtilities,
  initAccessibility
};
