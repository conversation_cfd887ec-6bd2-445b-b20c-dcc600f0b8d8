// SEO Configuration for FixMix Landing Page
export const seoConfig = {
  defaultTitle: 'FixMix - On-Demand Home Services & Professional Vendors',
  titleTemplate: '%s | FixMix',
  defaultDescription: 'Book trusted professionals for plumbing, electrical, carpentry, and more. FixMix connects you with verified vendors for all your home service needs. Download the app today!',
  siteUrl: 'https://fixmix.co',
  defaultImage: 'https://fixmix.co/images/FixMix-og-image.png',
  twitterHandle: '@fixmix',
  
  // Structured data schemas
  organizationSchema: {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "FixMix",
    "description": "On-demand service marketplace connecting users with skilled vendors for home and professional services",
    "url": "https://fixmix.co",
    "logo": "https://fixmix.co/images/FixMix.png",
    "foundingDate": "2023",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-FIXMIX",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["English", "Arabic"]
    },
    "sameAs": [
      "https://facebook.com/fixmix",
      "https://twitter.com/fixmix", 
      "https://instagram.com/fixmix",
      "https://linkedin.com/company/fixmix"
    ],
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    }
  },

  mobileAppSchema: {
    "@context": "https://schema.org",
    "@type": "MobileApplication",
    "name": "FixMix",
    "description": "Book home services and connect with professional vendors",
    "applicationCategory": "LifestyleApplication",
    "operatingSystem": ["iOS", "Android"],
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "1250"
    }
  },

  serviceSchema: {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Home Service Marketplace",
    "description": "Professional home services including plumbing, electrical, carpentry, and more",
    "provider": {
      "@type": "Organization",
      "name": "FixMix"
    },
    "serviceType": [
      "Plumbing Services",
      "Electrical Services", 
      "Carpentry Services",
      "Painting Services",
      "Home Maintenance"
    ],
    "areaServed": {
      "@type": "Country",
      "name": "United States"
    }
  },

  // Breadcrumb schema generator
  generateBreadcrumbSchema: (breadcrumbs) => ({
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": `https://fixmix.co${crumb.url}`
    }))
  }),

  // FAQ schema generator
  generateFAQSchema: (faqs) => ({
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  })
};

// Meta tag generators
export const generateMetaTags = (pageConfig = {}) => {
  const {
    title = seoConfig.defaultTitle,
    description = seoConfig.defaultDescription,
    image = seoConfig.defaultImage,
    url = seoConfig.siteUrl,
    type = 'website',
    locale = 'en_US',
    alternateLocales = ['ar_SA']
  } = pageConfig;

  return {
    title,
    description,
    canonical: url,
    openGraph: {
      type,
      locale,
      url,
      title,
      description,
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
      site_name: 'FixMix',
    },
    twitter: {
      handle: seoConfig.twitterHandle,
      site: seoConfig.twitterHandle,
      cardType: 'summary_large_image',
      title,
      description,
      image,
    },
    additionalMetaTags: [
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1, user-scalable=no'
      },
      {
        name: 'theme-color',
        content: '#0096c7'
      },
      {
        name: 'mobile-web-app-capable',
        content: 'yes'
      },
      {
        name: 'apple-mobile-web-app-status-bar-style',
        content: 'default'
      },
      {
        name: 'msapplication-TileColor',
        content: '#0096c7'
      }
    ],
    additionalLinkTags: [
      {
        rel: 'icon',
        href: '/favicon.ico',
      },
      {
        rel: 'apple-touch-icon',
        href: '/images/icons/icon-192x192.png',
        sizes: '192x192'
      },
      {
        rel: 'manifest',
        href: '/manifest.json'
      },
      ...alternateLocales.map(locale => ({
        rel: 'alternate',
        hrefLang: locale.replace('_', '-').toLowerCase(),
        href: `${url}/${locale.split('_')[0]}`
      }))
    ]
  };
};

export default seoConfig;
