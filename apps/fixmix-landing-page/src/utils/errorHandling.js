// Error handling utilities for FixMix Landing Page

// Error types
export const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  VALIDATION: 'VALIDATION_ERROR',
  COMPONENT: 'COMPONENT_ERROR',
  TRANSLATION: 'TRANSLATION_ERROR',
  IMAGE: 'IMAGE_ERROR',
  ANALYTICS: 'ANALYTICS_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
};

// Error severity levels
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// Custom error class
export class FixMixError extends Error {
  constructor(message, type = ERROR_TYPES.UNKNOWN, severity = ERROR_SEVERITY.MEDIUM, context = {}) {
    super(message);
    this.name = 'FixMixError';
    this.type = type;
    this.severity = severity;
    this.context = context;
    this.timestamp = new Date().toISOString();
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, FixMixError);
    }
  }
}

// Error logger
export const errorLogger = {
  log: (error, context = {}) => {
    const errorInfo = {
      message: error.message,
      type: error.type || ERROR_TYPES.UNKNOWN,
      severity: error.severity || ERROR_SEVERITY.MEDIUM,
      stack: error.stack,
      context: { ...error.context, ...context },
      timestamp: error.timestamp || new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
      url: typeof window !== 'undefined' ? window.location.href : 'server'
    };

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 FixMix Error [${errorInfo.severity.toUpperCase()}]`);
      console.error('Message:', errorInfo.message);
      console.error('Type:', errorInfo.type);
      console.error('Context:', errorInfo.context);
      console.error('Stack:', errorInfo.stack);
      console.groupEnd();
    }

    // Send to error reporting service in production
    if (process.env.NODE_ENV === 'production') {
      // This would integrate with services like Sentry, LogRocket, etc.
      try {
        // Example: Sentry.captureException(error, { extra: errorInfo });
        console.error('Production Error:', errorInfo);
      } catch (reportingError) {
        console.error('Failed to report error:', reportingError);
      }
    }

    return errorInfo;
  }
};

// Error boundary helper
export const withErrorBoundary = (Component, fallbackComponent = null) => {
  return class extends React.Component {
    constructor(props) {
      super(props);
      this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error) {
      return { hasError: true, error };
    }

    componentDidCatch(error, errorInfo) {
      const fixMixError = new FixMixError(
        error.message,
        ERROR_TYPES.COMPONENT,
        ERROR_SEVERITY.HIGH,
        { componentStack: errorInfo.componentStack }
      );
      
      errorLogger.log(fixMixError);
    }

    render() {
      if (this.state.hasError) {
        return fallbackComponent || (
          <div className="error-fallback">
            <h3>Something went wrong</h3>
            <p>We're sorry, but this component failed to load.</p>
          </div>
        );
      }

      return <Component {...this.props} />;
    }
  };
};

// Async error handler
export const handleAsync = (asyncFn) => {
  return async (...args) => {
    try {
      return await asyncFn(...args);
    } catch (error) {
      const fixMixError = new FixMixError(
        error.message,
        ERROR_TYPES.NETWORK,
        ERROR_SEVERITY.MEDIUM,
        { function: asyncFn.name, args }
      );
      
      errorLogger.log(fixMixError);
      throw fixMixError;
    }
  };
};

// Validation helpers
export const validate = {
  required: (value, fieldName) => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      throw new FixMixError(
        `${fieldName} is required`,
        ERROR_TYPES.VALIDATION,
        ERROR_SEVERITY.LOW,
        { field: fieldName, value }
      );
    }
    return true;
  },

  email: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new FixMixError(
        'Invalid email format',
        ERROR_TYPES.VALIDATION,
        ERROR_SEVERITY.LOW,
        { email }
      );
    }
    return true;
  },

  url: (url) => {
    try {
      new URL(url);
      return true;
    } catch {
      throw new FixMixError(
        'Invalid URL format',
        ERROR_TYPES.VALIDATION,
        ERROR_SEVERITY.LOW,
        { url }
      );
    }
  }
};

// Safe component wrapper
export const safeComponent = (Component, fallback = null) => {
  return (props) => {
    try {
      return <Component {...props} />;
    } catch (error) {
      const fixMixError = new FixMixError(
        error.message,
        ERROR_TYPES.COMPONENT,
        ERROR_SEVERITY.MEDIUM,
        { component: Component.name, props }
      );
      
      errorLogger.log(fixMixError);
      
      return fallback || (
        <div className="component-error">
          <span>Component failed to render</span>
        </div>
      );
    }
  };
};

// Translation error handler
export const safeTranslation = (t, key, fallback = key) => {
  try {
    const translation = t(key, fallback);
    return translation || fallback;
  } catch (error) {
    const fixMixError = new FixMixError(
      `Translation failed for key: ${key}`,
      ERROR_TYPES.TRANSLATION,
      ERROR_SEVERITY.LOW,
      { key, fallback }
    );
    
    errorLogger.log(fixMixError);
    return fallback;
  }
};

// Image loading error handler
export const handleImageError = (src, onError) => {
  return (event) => {
    const fixMixError = new FixMixError(
      `Failed to load image: ${src}`,
      ERROR_TYPES.IMAGE,
      ERROR_SEVERITY.LOW,
      { src, event }
    );
    
    errorLogger.log(fixMixError);
    
    if (onError) {
      onError(event);
    }
  };
};

// Analytics error handler
export const safeAnalytics = (analyticsFunction, ...args) => {
  try {
    return analyticsFunction(...args);
  } catch (error) {
    const fixMixError = new FixMixError(
      `Analytics tracking failed: ${error.message}`,
      ERROR_TYPES.ANALYTICS,
      ERROR_SEVERITY.LOW,
      { function: analyticsFunction.name, args }
    );
    
    errorLogger.log(fixMixError);
  }
};

// Network request error handler
export const handleNetworkError = (error, context = {}) => {
  let errorType = ERROR_TYPES.NETWORK;
  let severity = ERROR_SEVERITY.MEDIUM;
  
  if (error.code === 'NETWORK_ERROR') {
    severity = ERROR_SEVERITY.HIGH;
  } else if (error.response?.status >= 500) {
    severity = ERROR_SEVERITY.HIGH;
  } else if (error.response?.status >= 400) {
    severity = ERROR_SEVERITY.MEDIUM;
  }
  
  const fixMixError = new FixMixError(
    error.message || 'Network request failed',
    errorType,
    severity,
    { 
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      ...context 
    }
  );
  
  errorLogger.log(fixMixError);
  return fixMixError;
};

// Error recovery strategies
export const errorRecovery = {
  retry: async (fn, maxAttempts = 3, delay = 1000) => {
    let lastError;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxAttempts) {
          throw new FixMixError(
            `Failed after ${maxAttempts} attempts: ${error.message}`,
            ERROR_TYPES.NETWORK,
            ERROR_SEVERITY.HIGH,
            { attempts: maxAttempts, originalError: error }
          );
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
  },

  fallback: (primaryFn, fallbackFn) => {
    return async (...args) => {
      try {
        return await primaryFn(...args);
      } catch (error) {
        errorLogger.log(new FixMixError(
          `Primary function failed, using fallback: ${error.message}`,
          ERROR_TYPES.UNKNOWN,
          ERROR_SEVERITY.MEDIUM,
          { primaryFunction: primaryFn.name, fallbackFunction: fallbackFn.name }
        ));
        
        return await fallbackFn(...args);
      }
    };
  }
};

export default {
  ERROR_TYPES,
  ERROR_SEVERITY,
  FixMixError,
  errorLogger,
  withErrorBoundary,
  handleAsync,
  validate,
  safeComponent,
  safeTranslation,
  handleImageError,
  safeAnalytics,
  handleNetworkError,
  errorRecovery
};
