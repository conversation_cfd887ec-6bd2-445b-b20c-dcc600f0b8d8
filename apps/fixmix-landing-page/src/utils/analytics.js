import ReactGA from 'react-ga4';

// Initialize Google Analytics
export const initGA = () => {
  const gaId = process.env.NEXT_PUBLIC_GA_TRACKING_ID;
  if (gaId && typeof window !== 'undefined') {
    ReactGA.initialize(gaId, {
      debug: process.env.NODE_ENV === 'development',
    });
  }
};

// Track page views
export const trackPageView = (url) => {
  if (typeof window !== 'undefined') {
    ReactGA.send({ 
      hitType: 'pageview', 
      page: url 
    });
  }
};

// Track events
export const trackEvent = (action, category = 'General', label = '', value = 0) => {
  if (typeof window !== 'undefined') {
    ReactGA.event({
      action,
      category,
      label,
      value,
    });
  }
};

// Track app download clicks
export const trackAppDownload = (store) => {
  trackEvent('app_download_click', 'App Downloads', store);
};

// Track CTA clicks
export const trackCTAClick = (ctaName, location = '') => {
  trackEvent('cta_click', 'CTA', `${ctaName}${location ? ` - ${location}` : ''}`);
};

// Track form submissions
export const trackFormSubmission = (formName) => {
  trackEvent('form_submit', 'Forms', formName);
};

// Track scroll depth
export const trackScrollDepth = (depth) => {
  trackEvent('scroll_depth', 'Engagement', `${depth}%`, depth);
};

// Performance monitoring
export const reportWebVitals = (metric) => {
  if (process.env.NODE_ENV === 'production') {
    // Track Core Web Vitals
    ReactGA.event({
      action: metric.name,
      category: 'Web Vitals',
      label: metric.id,
      value: Math.round(metric.value),
      non_interaction: true,
    });
  }
};

// Track user engagement
export const trackEngagement = (action, details = '') => {
  trackEvent(action, 'User Engagement', details);
};

// Track errors
export const trackError = (error, errorInfo = '') => {
  if (process.env.NODE_ENV === 'production') {
    ReactGA.event({
      action: 'javascript_error',
      category: 'Errors',
      label: `${error.message} - ${errorInfo}`,
      non_interaction: true,
    });
  }
};
