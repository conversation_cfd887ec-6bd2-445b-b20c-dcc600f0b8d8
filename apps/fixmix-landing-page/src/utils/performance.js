// Performance Monitoring and Optimization Utilities
import { trackEvent } from './analytics';

// Core Web Vitals monitoring
export const measureWebVitals = () => {
  if (typeof window === 'undefined') return;

  // Largest Contentful Paint (LCP)
  const observeLCP = () => {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        trackEvent('web_vitals', 'Performance', 'LCP', Math.round(lastEntry.startTime));
        
        // Log poor LCP performance
        if (lastEntry.startTime > 2500) {
          console.warn('Poor LCP performance:', lastEntry.startTime);
        }
      });
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (error) {
      console.warn('LCP observation not supported');
    }
  };

  // First Input Delay (FID)
  const observeFID = () => {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          trackEvent('web_vitals', 'Performance', 'FID', Math.round(entry.processingStart - entry.startTime));
          
          // Log poor FID performance
          if (entry.processingStart - entry.startTime > 100) {
            console.warn('Poor FID performance:', entry.processingStart - entry.startTime);
          }
        });
      });
      
      observer.observe({ entryTypes: ['first-input'] });
    } catch (error) {
      console.warn('FID observation not supported');
    }
  };

  // Cumulative Layout Shift (CLS)
  const observeCLS = () => {
    try {
      let clsValue = 0;
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        
        trackEvent('web_vitals', 'Performance', 'CLS', Math.round(clsValue * 1000));
        
        // Log poor CLS performance
        if (clsValue > 0.1) {
          console.warn('Poor CLS performance:', clsValue);
        }
      });
      
      observer.observe({ entryTypes: ['layout-shift'] });
    } catch (error) {
      console.warn('CLS observation not supported');
    }
  };

  observeLCP();
  observeFID();
  observeCLS();
};

// Resource loading performance
export const measureResourceLoading = () => {
  if (typeof window === 'undefined') return;

  window.addEventListener('load', () => {
    // Measure page load time
    const navigation = performance.getEntriesByType('navigation')[0];
    if (navigation) {
      const loadTime = navigation.loadEventEnd - navigation.fetchStart;
      trackEvent('performance', 'Page Load', 'Total Time', Math.round(loadTime));
      
      // Track specific metrics
      trackEvent('performance', 'Page Load', 'DOM Content Loaded', Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart));
      trackEvent('performance', 'Page Load', 'First Paint', Math.round(navigation.responseEnd - navigation.fetchStart));
    }

    // Measure resource loading
    const resources = performance.getEntriesByType('resource');
    const slowResources = resources.filter(resource => resource.duration > 1000);
    
    if (slowResources.length > 0) {
      console.warn('Slow loading resources:', slowResources);
      slowResources.forEach(resource => {
        trackEvent('performance', 'Slow Resource', resource.name, Math.round(resource.duration));
      });
    }
  });
};

// Image loading optimization
export const optimizeImageLoading = () => {
  if (typeof window === 'undefined') return;

  // Lazy loading for images
  const images = document.querySelectorAll('img[data-src]');
  
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });

    images.forEach(img => imageObserver.observe(img));
  } else {
    // Fallback for browsers without IntersectionObserver
    images.forEach(img => {
      img.src = img.dataset.src;
      img.classList.remove('lazy');
    });
  }
};

// Memory usage monitoring
export const monitorMemoryUsage = () => {
  if (typeof window === 'undefined' || !performance.memory) return;

  const checkMemory = () => {
    const memory = performance.memory;
    const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
    const totalMB = Math.round(memory.totalJSHeapSize / 1048576);
    const limitMB = Math.round(memory.jsHeapSizeLimit / 1048576);

    // Track memory usage
    trackEvent('performance', 'Memory Usage', 'Used MB', usedMB);
    
    // Warn if memory usage is high
    if (usedMB > limitMB * 0.8) {
      console.warn('High memory usage detected:', { usedMB, totalMB, limitMB });
    }
  };

  // Check memory usage every 30 seconds
  setInterval(checkMemory, 30000);
  
  // Initial check
  checkMemory();
};

// Bundle size analysis
export const analyzeBundleSize = () => {
  if (typeof window === 'undefined') return;

  // Track JavaScript bundle sizes
  const scripts = document.querySelectorAll('script[src]');
  scripts.forEach(script => {
    if (script.src.includes('/_next/static/')) {
      fetch(script.src, { method: 'HEAD' })
        .then(response => {
          const size = response.headers.get('content-length');
          if (size) {
            const sizeKB = Math.round(size / 1024);
            trackEvent('performance', 'Bundle Size', 'JavaScript KB', sizeKB);
            
            if (sizeKB > 500) {
              console.warn('Large JavaScript bundle detected:', script.src, sizeKB + 'KB');
            }
          }
        })
        .catch(() => {
          // Ignore errors for cross-origin requests
        });
    }
  });
};

// Network quality detection
export const detectNetworkQuality = () => {
  if (typeof window === 'undefined' || !navigator.connection) return;

  const connection = navigator.connection;
  
  const trackNetworkInfo = () => {
    trackEvent('performance', 'Network', 'Effective Type', connection.effectiveType);
    trackEvent('performance', 'Network', 'Downlink Mbps', Math.round(connection.downlink));
    trackEvent('performance', 'Network', 'RTT ms', connection.rtt);
    
    // Adjust performance based on network quality
    if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
      document.body.classList.add('slow-network');
      console.info('Slow network detected, optimizing experience');
    }
  };

  trackNetworkInfo();
  
  // Listen for network changes
  connection.addEventListener('change', trackNetworkInfo);
};

// Performance budget monitoring
export const monitorPerformanceBudget = () => {
  const budgets = {
    lcp: 2500, // 2.5 seconds
    fid: 100,  // 100 milliseconds
    cls: 0.1,  // 0.1 layout shift score
    totalPageSize: 3000000, // 3MB
    jsSize: 1000000, // 1MB
    cssSize: 500000, // 500KB
    imageSize: 2000000 // 2MB
  };

  // This would integrate with your monitoring system
  console.info('Performance budgets set:', budgets);
  
  return budgets;
};

// Initialize all performance monitoring
export const initPerformanceMonitoring = () => {
  if (typeof window === 'undefined') return;

  measureWebVitals();
  measureResourceLoading();
  optimizeImageLoading();
  monitorMemoryUsage();
  analyzeBundleSize();
  detectNetworkQuality();
  monitorPerformanceBudget();
  
  console.info('Performance monitoring initialized');
};

const performanceUtils = {
  measureWebVitals,
  measureResourceLoading,
  optimizeImageLoading,
  monitorMemoryUsage,
  analyzeBundleSize,
  detectNetworkQuality,
  monitorPerformanceBudget,
  initPerformanceMonitoring
};

export default performanceUtils;
