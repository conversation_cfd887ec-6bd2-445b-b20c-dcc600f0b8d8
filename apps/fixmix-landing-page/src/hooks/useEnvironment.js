import { useMemo } from 'react';

const useEnvironment = () => {
  const config = useMemo(() => {
    return {
      // Environment
      isDevelopment: process.env.NODE_ENV === 'development',
      isProduction: process.env.NODE_ENV === 'production',
      environment: process.env.NEXT_PUBLIC_ENVIRONMENT || 'development',
      
      // Analytics
      gaTrackingId: process.env.NEXT_PUBLIC_GA_TRACKING_ID,
      
      // API
      apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.fixmix.co',
      
      // App Store Links
      appStoreUrl: process.env.NEXT_PUBLIC_APP_STORE_URL || 'https://apps.apple.com/app/fixmix',
      googlePlayUrl: process.env.NEXT_PUBLIC_GOOGLE_PLAY_URL || 'https://play.google.com/store/apps/details?id=com.fixmix.app',
      
      // Contact Information
      contactEmail: process.env.NEXT_PUBLIC_CONTACT_EMAIL || '<EMAIL>',
      supportPhone: process.env.NEXT_PUBLIC_SUPPORT_PHONE || '******-FIXMIX',
      
      // Social Media
      socialMedia: {
        facebook: process.env.NEXT_PUBLIC_FACEBOOK_URL || 'https://facebook.com/fixmix',
        twitter: process.env.NEXT_PUBLIC_TWITTER_URL || 'https://twitter.com/fixmix',
        instagram: process.env.NEXT_PUBLIC_INSTAGRAM_URL || 'https://instagram.com/fixmix',
        linkedin: process.env.NEXT_PUBLIC_LINKEDIN_URL || 'https://linkedin.com/company/fixmix',
      },
      
      // ConvertKit
      convertkitApiKey: process.env.CONVERTKIT_API_KEY,
      convertkitFormId: process.env.CONVERTKIT_FORM_ID,
      
      // Feature Flags
      features: {
        analytics: !!process.env.NEXT_PUBLIC_GA_TRACKING_ID,
        newsletter: !!(process.env.CONVERTKIT_API_KEY && process.env.CONVERTKIT_FORM_ID),
        chatWidget: process.env.NEXT_PUBLIC_ENABLE_CHAT === 'true',
      }
    };
  }, []);

  return config;
};

export default useEnvironment;
