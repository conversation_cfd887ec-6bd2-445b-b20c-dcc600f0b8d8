import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';

// Import translation files
import enTranslations from '@/locales/en.json';
import arTranslations from '@/locales/ar.json';

const translations = {
  en: enTranslations,
  ar: arTranslations
};

export const useTranslation = () => {
  const router = useRouter();
  const { locale = 'en' } = router;
  const [currentLocale, setCurrentLocale] = useState(locale);

  useEffect(() => {
    setCurrentLocale(locale);

    // Set document direction for RTL languages
    if (typeof document !== 'undefined') {
      document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr';
      document.documentElement.lang = locale;
    }
  }, [locale]);

  const t = (key, fallback = key, interpolations = {}) => {
    const keys = key.split('.');
    let value = translations[currentLocale];

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // Fallback to English if key not found in current locale
        value = translations.en;
        for (const k of keys) {
          if (value && typeof value === 'object' && k in value) {
            value = value[k];
          } else {
            return fallback;
          }
        }
        break;
      }
    }

    if (typeof value !== 'string') {
      return fallback;
    }

    // Handle interpolations
    let result = value;
    Object.keys(interpolations).forEach(key => {
      result = result.replace(new RegExp(`{{${key}}}`, 'g'), interpolations[key]);
    });

    return result;
  };

  const changeLanguage = (newLocale) => {
    // Store current scroll position
    const scrollY = window.scrollY;

    // Navigate to new locale without scrolling to top
    router.push(router.pathname, router.asPath, { locale: newLocale, scroll: false }).then(() => {
      // Restore scroll position after navigation
      setTimeout(() => {
        window.scrollTo(0, scrollY);
      }, 100);
    });
  };

  const isRTL = currentLocale === 'ar';

  // Get nested translation object
  const getTranslations = (section) => {
    return translations[currentLocale][section] || translations.en[section] || {};
  };

  return {
    t,
    getTranslations,
    locale: currentLocale,
    changeLanguage,
    isRTL,
    availableLocales: Object.keys(translations),
    translations: translations[currentLocale]
  };
};

export default useTranslation;
