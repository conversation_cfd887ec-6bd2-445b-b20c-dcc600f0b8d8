import { useEffect } from 'react';

// Hook for enhanced keyboard navigation
const useKeyboardNavigation = () => {
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Handle Escape key to close modals/menus
      if (event.key === 'Escape') {
        // Close mobile menu if open
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        if (mobileMenu && mobileMenu.classList.contains('mobile-menu-open')) {
          mobileMenuToggle?.click();
        }
        
        // Remove focus from current element
        if (document.activeElement && document.activeElement.blur) {
          document.activeElement.blur();
        }
      }
      
      // Handle Tab key for focus management
      if (event.key === 'Tab') {
        // Add focus-visible class for keyboard users
        document.body.classList.add('keyboard-navigation');
      }
      
      // Handle Enter and Space for button-like elements
      if (event.key === 'Enter' || event.key === ' ') {
        const target = event.target;
        
        // Handle card clicks
        if (target.classList.contains('card-clickable') || 
            target.closest('.card-clickable')) {
          event.preventDefault();
          const clickableCard = target.classList.contains('card-clickable') 
            ? target 
            : target.closest('.card-clickable');
          
          if (clickableCard && clickableCard.click) {
            clickableCard.click();
          }
        }
        
        // Handle custom button elements
        if (target.getAttribute('role') === 'button' && !target.disabled) {
          event.preventDefault();
          if (target.click) {
            target.click();
          }
        }
      }
      
      // Handle arrow keys for navigation
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        const focusableElements = getFocusableElements();
        const currentIndex = focusableElements.indexOf(document.activeElement);
        
        if (currentIndex !== -1) {
          let nextIndex;
          
          switch (event.key) {
            case 'ArrowDown':
            case 'ArrowRight':
              nextIndex = (currentIndex + 1) % focusableElements.length;
              break;
            case 'ArrowUp':
            case 'ArrowLeft':
              nextIndex = currentIndex === 0 
                ? focusableElements.length - 1 
                : currentIndex - 1;
              break;
            default:
              return;
          }
          
          // Only navigate if we're in a navigation context
          if (isInNavigationContext(document.activeElement)) {
            event.preventDefault();
            focusableElements[nextIndex]?.focus();
          }
        }
      }
    };
    
    const handleMouseDown = () => {
      // Remove keyboard navigation class when mouse is used
      document.body.classList.remove('keyboard-navigation');
    };
    
    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);
    
    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, []);
};

// Helper function to get all focusable elements
const getFocusableElements = () => {
  const selector = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[role="button"]:not([disabled])',
    '.card-clickable'
  ].join(', ');
  
  return Array.from(document.querySelectorAll(selector))
    .filter(element => {
      // Filter out hidden elements
      const style = window.getComputedStyle(element);
      return style.display !== 'none' && 
             style.visibility !== 'hidden' && 
             element.offsetParent !== null;
    });
};

// Helper function to check if element is in a navigation context
const isInNavigationContext = (element) => {
  const navigationContexts = [
    '.sticky-nav',
    '.mobile-menu',
    '.accordion',
    '.nav',
    '.navbar'
  ];
  
  return navigationContexts.some(context => 
    element.closest(context) !== null
  );
};

// Hook for focus trap (useful for modals)
export const useFocusTrap = (isActive, containerRef) => {
  useEffect(() => {
    if (!isActive || !containerRef.current) return;
    
    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    const handleTabKey = (event) => {
      if (event.key !== 'Tab') return;
      
      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement?.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement?.focus();
        }
      }
    };
    
    // Focus first element when trap becomes active
    firstElement?.focus();
    
    container.addEventListener('keydown', handleTabKey);
    
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }, [isActive, containerRef]);
};

// Hook for announcing content changes to screen readers
export const useAnnouncement = () => {
  const announce = (message, priority = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  };
  
  return { announce };
};

export default useKeyboardNavigation;
