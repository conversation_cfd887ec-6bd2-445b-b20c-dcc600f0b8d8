import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import { useRouter } from 'next/router'
import { useTranslation } from '@/hooks/useTranslation'
import Head from 'next/head'

const PrivacyPage = () => {
    const { t } = useTranslation();
    const router = useRouter();

    return (
        <>
            <Head>
                <title>{t('footer.links.privacy', 'Privacy Policy')} - FixMix</title>
                <meta name="description" content="FixMix Privacy Policy" />
            </Head>
            <div className=''>
                <div className='text-center py-3 bg-primary'>
                    <nav className="navbar text-center text-md-left">
                        <Link className="navbar-brand mx-auto mx-md-4" href="/">
                            <Image
                                src="/images/FixMix.png"
                                alt={t('navigation.logo.alt', 'FixMix Logo')}
                                height={50}
                                width={100}
                                className=""
                            />
                        </Link>
                    </nav>
                </div>
                <div className='py-8 container'>
                    <h1 className='text-center py-4'>{t('privacy.title', 'Privacy Policy')}</h1>
                <div className='text-justify py-4'>{t('privacy.intro', 'Your privacy is important to us. This Privacy Policy explains how we collect, use, and protect your personal information when you use the FixMix app (the “App”). By using the App, you agree to the terms outlined in this Privacy Policy.')}</div>
                <h3 className='font-semibold text-lg lg:text-xl'>{t('privacy.sections.collection.title', '1. Information We Collect')}</h3>
                <div className='text-base'>{t('privacy.sections.collection.content', 'We collect information to provide and improve our services. This includes:')}</div>
                <h4 className='font-semibold text-base lg:text-lg'>{t('privacy.sections.collection.provided.title', 'a. Information You Provide:')}</h4>
                <ul className='list-disc text-base lg:text-lg'>
                    <li>{t('privacy.sections.collection.provided.items.0', 'Account registration details such as name, email address, and contact information.')}</li>
                    <li>{t('privacy.sections.collection.provided.items.1', 'Any additional details you provide through forms or customer support.')}</li>
                </ul>
                <h4 className='font-semibold text-base lg:text-lg'>{t('privacy.sections.collection.automatic.title', 'b. Automatically Collected Information:')}</h4>
                <ul className='list-disc text-base lg:text-lg'>
                    <li>{t('privacy.sections.collection.automatic.items.0', 'Device information, including IP address, operating system, and browser type.')}</li>
                    <li>{t('privacy.sections.collection.automatic.items.1', 'Usage data, such as app interactions, search history, and preferences.')}</li>
                    <li>{t('privacy.sections.collection.automatic.items.2', 'Cookies and similar technologies to track activity and enhance user experience.')}</li>
                </ul>
                <h3 className='font-semibold text-lg lg:text-xl'>{t('privacy.sections.usage.title', '2. How We Use Your Information')}</h3>
                <div className='text-base lg:text-lg'>{t('privacy.sections.usage.content', 'We use your information to:')}</div>
                <ul className='text-base lg:text-lg'>
                    <li>{t('privacy.sections.usage.items.0', 'Provide and improve the App’s features and services.')}</li>
                    <li>{t('privacy.sections.usage.items.1', 'Facilitate communication between users and sellers.')}</li>
                    <li>{t('privacy.sections.usage.items.2', 'Ensure a secure and personalized user experience.')}</li>
                    <li>{t('privacy.sections.usage.items.3', 'Send notifications, updates, or promotional offers.')}</li>
                    <li>{t('privacy.sections.usage.items.4', 'Conduct analytics to optimize app performance and user satisfaction.')}</li>
                </ul>
                <h3 className='font-semibold text-lg lg:text-xl'>{t('privacy.sections.sharing.title', '3. Sharing of Information')}</h3>
                <div className='text-base lg:text-lg'>{t('privacy.sections.sharing.content', 'We do not sell your personal information. However, we may share it in the following scenarios:')}</div>
                <ul className='text-base lg:text-lg'>
                    <li><strong className='font-semibold'>{t('privacy.sections.sharing.items.0', 'With Service Providers: Trusted third-party providers who help us deliver and improve our services (e.g., payment processors or analytics tools).')}</strong></li>
                    <li><strong className='font-semibold'>{t('privacy.sections.sharing.items.1', 'For Legal Reasons: To comply with legal obligations or protect the rights and safety of users.')}</strong></li>
                </ul>
                <h3 className='font-semibold text-lg lg:text-xl'>{t('privacy.sections.retention.title', '4. Data Retention')}</h3>
                <div className='text-base lg:text-lg'>{t('privacy.sections.retention.content', 'We retain your personal information as long as necessary to provide the App’s services and comply with legal requirements. If you delete your account, we may retain certain data to meet legal or operational needs.')}</div>
                <h3 className='font-semibold text-lg lg:text-xl'>{t('privacy.sections.security.title', '5. Security')}</h3>
                <div className='text-base lg:text-lg'>{t('privacy.sections.security.content', 'We implement advanced security measures to protect your information from unauthorized access, alteration, or misuse. However, no system is entirely risk-free, and we cannot guarantee absolute security.')}</div>
                <h3 className='font-semibold text-lg lg:text-xl'>{t('privacy.sections.rights.title', '6. Your Rights and Choices')}</h3>
                <ul className='text-base lg:text-lg'>
                    <li><strong className='font-semibold'>{t('privacy.sections.rights.items.0', 'Access and Correction: You can view or update your personal information in your account settings.')}</strong></li>
                    <li><strong className='font-semibold'>{t('privacy.sections.rights.items.1', 'Notifications: Manage app notifications and communication preferences.')}</strong></li>
                    <li><strong className='font-semibold'>{t('privacy.sections.rights.items.2', 'Account Deletion: Contact <NAME_EMAIL> to request the deletion of your account and data.')}</strong></li>
                </ul>
                <h3 className='font-semibold text-lg lg:text-xl'>{t('privacy.sections.cookies.title', '7. Cookies and Tracking Technologies')}</h3>
                <div className='text-base lg:text-lg'>{t('privacy.sections.cookies.content', 'The App uses cookies and similar technologies to track usage patterns, improve user experience, and deliver personalized content. You can manage cookie settings in your device or browser.')}</div>
                <h3 className='font-semibold text-lg lg:text-xl'>{t('privacy.sections.children.title', '8. Children\'s Privacy')}</h3>
                <div className='text-base lg:text-lg'>{t('privacy.sections.children.content', 'The App is not intended for children under 13 years of age, and we do not knowingly collect data from children.')}</div>
                <h3 className='font-semibold text-lg lg:text-xl'>{t('privacy.sections.changes.title', '9. Changes to This Privacy Policy')}</h3>
                <div className='text-base lg:text-lg'>{t('privacy.sections.changes.content', 'We may update this Privacy Policy periodically. Updates will be posted in the App with an updated effective date. Continued use of the App constitutes acceptance of the revised terms.')}</div>
                <h3 className='font-semibold text-lg lg:text-xl'>{t('privacy.sections.contact.title', '10. Contact Us')}</h3>
                <div className='text-base lg:text-lg'>{t('privacy.sections.contact.content', 'If you have any questions or concerns about this Privacy Policy, please contact us at:')}</div>
                <div className='text-base lg:text-lg'>{t('privacy.sections.contact.email', 'Email:')} <Link href="mailto:<EMAIL>"><EMAIL></Link></div>
                <strong className=''>{t('privacy.sections.contact.footer', 'By using FixMix, you acknowledge that you have read and understood this Privacy Policy.')}</strong>

                </div>
            </div>

            {/* Footer */}
            <div id="section_footer">
                <div className='text-center py-4'>
                    <Link href={router.pathname.startsWith('/ar') ? '/ar/privacy' : '/privacy'} className='btn btn-link'>
                        {t('footer.links.privacy', 'Privacy Policy')}
                    </Link>
                    <span>|</span>
                    <Link href={router.pathname.startsWith('/ar') ? '/ar/terms' : '/terms'} className='btn btn-link'>
                        {t('footer.links.terms', 'Terms of Service')}
                    </Link>
                    <span>|</span>
                    <Link href="mailto:<EMAIL>" className='btn btn-link'>
                        {t('contact', 'Contact Us')}
                    </Link>
                </div>
            </div>
        </>
    )
}

export default PrivacyPage
