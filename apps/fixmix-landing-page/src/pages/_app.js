import 'bootstrap/dist/css/bootstrap.css';
import '@/styles/globals.scss';
import '@/styles/blogs.scss';
import { Montserrat } from 'next/font/google';
import { useEffect } from 'react';
import Head from 'next/head'
import DynamicMeta from '@/components/DynamicMeta';
import useDropShadow from '@/hooks/useDropShadow';
import useKeyboardNavigation from '@/hooks/useKeyboardNavigation';
import Layout from '@/components/Layout';
import ErrorBoundary from '@/components/ErrorBoundary';
import { initPerformanceOptimizations } from '@/utils/performanceOptimizations';
import { initPerformanceMonitoring } from '@/utils/performance';
import { initGA, reportWebVitals } from '@/utils/analytics';
import { initPerformanceMonitoring } from '@/utils/performance';


const montserrat = Montserrat({ subsets: ['latin'] });

export default function App({ Component, pageProps }) {
  useDropShadow();
  useKeyboardNavigation();

  useEffect(() => {
    require('bootstrap/dist/js/bootstrap.bundle.min.js');

    // Initialize Google Analytics
    initGA();

    // Initialize Performance Monitoring
    initPerformanceMonitoring();

    // Initialize Performance Optimizations
    initPerformanceOptimizations();
  }, []);

  return (
    <ErrorBoundary>
      <DynamicMeta />
      <main className={montserrat.className}>
        <Layout>
          <Component {...pageProps} />
        </Layout>
      </main>
    </ErrorBoundary>
  );
}

// Export reportWebVitals for Next.js
export { reportWebVitals };
