import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import Hero from '@/components/Hero';
import UseCase from '@/components/UseCase';
import Reviews from '@/components/Reviews';
import WhatMakesFixMixStandOut from '@/components/WhatMakesFixMixStandOut';
import FAQ from '@/components/FAQ';
import DownloadSection from '@/components/DownloadSection';
import content from '@/content';
import { useTranslation } from '@/hooks/useTranslation';
import { useRouter as useNextRouter } from 'next/router';
import { ReactComponent as category } from './../../public/images/icons/category.svg'

export default function HomeArabic() {
  const { t, isRTL } = useTranslation();
  const router = useRouter();

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "FixMix",
    "url": "https://fixmix.co",
    "description": "احصل على مساعدة مهنية فورية - في أي وقت وفي أي مكان!",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://fixmix.co/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <>
      <Head>
        <title>FixMix - احصل على مساعدة مهنية فورية</title>
        <meta name="description" content="احصل على مساعدة مهنية فورية - في أي وقت وفي أي مكان! تطبيق FixMix يربطك بالخبراء المحترفين." />
        <meta name="keywords" content="مساعدة مهنية, خدمات فورية, تطبيق FixMix, خبراء محترفين" />
        <meta property="og:title" content="FixMix - احصل على مساعدة مهنية فورية" />
        <meta property="og:description" content="احصل على مساعدة مهنية فورية - في أي وقت وفي أي مكان!" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://fixmix.co/ar" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="FixMix - احصل على مساعدة مهنية فورية" />
        <meta name="twitter:description" content="احصل على مساعدة مهنية فورية - في أي وقت وفي أي مكان!" />
        <link rel="canonical" href="https://fixmix.co/ar" />
        <link rel="alternate" hrefLang="en" href="https://fixmix.co" />
        <link rel="alternate" hrefLang="ar" href="https://fixmix.co/ar" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      </Head>
      
      <main dir="rtl">
        {/* Hero Section */}
        <div id="hero">
          <Hero />
        </div>

        {/* Use Cases Section */}
        <div id="use-cases">
          <UseCase />
        </div>

        {/* Reviews Section */}
        <div id="reviews">
          <Reviews />
        </div>

        {/* What Makes FixMix Stand Out Section */}
        <div id="how-it-works">
          <WhatMakesFixMixStandOut />
        </div>

        {/* FAQ Section */}
        <div id="faq" dir="rtl">
          <FAQ />
        </div>

        {/* Download Section */}
        <div id="download">
          <DownloadSection />
        </div>
      </main>
      
      {/* Footer */}
      <div id="section_footer" dir="rtl">
        <div className='text-center py-4'>
          <Link href="/ar/privacy" className='btn btn-link'>
            سياسة الخصوصية
          </Link>
          <span>|</span>
          <Link href="/ar/terms" className='btn btn-link'>
            شروط الخدمة
          </Link>
          <span>|</span>
          <Link href="mailto:<EMAIL>" className='btn btn-link'>
            اتصل بنا
          </Link>
        </div>
      </div>
    </>
  );
}
