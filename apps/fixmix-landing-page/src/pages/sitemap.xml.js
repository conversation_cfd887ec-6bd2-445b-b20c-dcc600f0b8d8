// Static pages configuration
const staticPages = [
  { url: '/', changefreq: 'daily', priority: '1.0' },
  { url: '/privacy', changefreq: 'monthly', priority: '0.7' },
  { url: '/terms', changefreq: 'monthly', priority: '0.7' },
  { url: '/delete-account', changefreq: 'monthly', priority: '0.5' },
];

// Multi-language support
const languages = ['en', 'ar'];

function generateSiteMap() {
  const baseUrl = 'https://fixmix.co';
  const currentDate = new Date().toISOString();

  // Generate URLs for all languages
  const allUrls = [];

  languages.forEach(lang => {
    staticPages.forEach(page => {
      const url = lang === 'en' ? page.url : `/${lang}${page.url}`;
      const alternateLinks = languages
        .filter(altLang => altLang !== lang)
        .map(altLang => {
          const altUrl = altLang === 'en' ? page.url : `/${altLang}${page.url}`;
          return `<xhtml:link rel="alternate" hreflang="${altLang}" href="${baseUrl}${altUrl}" />`;
        })
        .join('\n       ');

      allUrls.push(`
     <url>
       <loc>${baseUrl}${url}</loc>
       <lastmod>${currentDate}</lastmod>
       <changefreq>${page.changefreq}</changefreq>
       <priority>${page.priority}</priority>
       ${alternateLinks}
     </url>`);
    });
  });

  return `<?xml version="1.0" encoding="UTF-8"?>
   <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
           xmlns:xhtml="http://www.w3.org/1999/xhtml">
     ${allUrls.join('')}
   </urlset>`;
}

function SiteMap() {
  // getServerSideProps will do the heavy lifting
}

export async function getServerSideProps({ res }) {
  try {
    // Generate the XML sitemap with multi-language support
    const sitemap = generateSiteMap();

    res.setHeader('Content-Type', 'text/xml');
    res.setHeader('Cache-Control', 'public, s-maxage=86400, stale-while-revalidate');
    res.write(sitemap);
    res.end();

    return {
      props: {},
    };
  } catch (error) {
    console.error('Error generating sitemap:', error);
    res.statusCode = 500;
    res.end();

    return {
      props: {},
    };
  }
}

export default SiteMap;
