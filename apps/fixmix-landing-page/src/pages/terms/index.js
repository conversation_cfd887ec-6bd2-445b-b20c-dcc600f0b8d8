import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import { useRouter } from 'next/router'
import { useTranslation } from '@/hooks/useTranslation'
import Head from 'next/head'

const TermsPage = () => {
    const { t } = useTranslation();
    const router = useRouter();

    return (
        <>
            <Head>
                <title>{t('terms.title', 'Terms of Service')} - FixMix</title>
                <meta name="description" content="FixMix Terms of Service" />
            </Head>
            <div>
                <div className='text-center py-3 bg-primary'>
                    <nav className="navbar text-center text-md-left">
                        <Link className="navbar-brand mx-auto mx-md-4" href={router.locale === 'ar' ? '/ar' : '/'}>
                            <Image
                                src="/images/FixMix.png"
                                alt={t('navigation.logo.alt', 'FixMix Logo')}
                                height={50}
                                width={100}
                                className=""
                            />
                        </Link>
                    </nav>
                </div>
                <div className='py-8 container'>
                    <h1 className='text-center py-4'>{t('terms.title', 'Terms of Service')}</h1>

                    <div className='py-2'>
                        <h3 className='font-semibold text-lg lg:text-xl mb-3'>{t('terms.sections.acceptance.title', '1. Acceptance of Terms')}</h3>
                        <p className='text-base lg:text-lg mb-4'>
                            {t('terms.sections.acceptance.content', 'Welcome to FixMix. By using our app or services, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our services.')}
                        </p>
                    </div>

                    <div className='py-2'>
                        <h3 className='font-semibold text-lg lg:text-xl mb-3'>{t('terms.sections.description.title', '2. Service Description')}</h3>
                        <p className='text-base lg:text-lg mb-4'>
                            {t('terms.sections.description.content', 'FixMix is a platform that connects users with professional service providers for various home maintenance and repair needs. We facilitate the connection between parties but are not directly responsible for the quality of services provided.')}
                        </p>
                    </div>

                    <div className='py-2'>
                        <h3 className='font-semibold text-lg lg:text-xl mb-3'>{t('terms.sections.registration.title', '3. Registration and Account')}</h3>
                        <p className='text-base lg:text-lg mb-4'>
                            {t('terms.sections.registration.content', 'You must create an account to use our services. You are responsible for maintaining the confidentiality of your account information and password. The information provided must be accurate and up-to-date.')}
                        </p>
                    </div>

                    <div className='py-2'>
                        <h3 className='font-semibold text-lg lg:text-xl mb-3'>{t('terms.sections.usage.title', '4. Use of Service')}</h3>
                        <p className='text-base lg:text-lg mb-4'>
                            {t('terms.sections.usage.content', 'You agree to use our services for lawful purposes only and in a way that does not infringe the rights of others or restrict or inhibit their use of the service. Use of the service for any fraudulent or harmful activities is prohibited.')}
                        </p>
                    </div>

                    <div className='py-2'>
                        <h3 className='font-semibold text-lg lg:text-xl mb-3'>{t('terms.sections.payments.title', '5. Payments and Fees')}</h3>
                        <p className='text-base lg:text-lg mb-4'>
                            {t('terms.sections.payments.content', 'All payments are processed through our secure platform. We charge fees for services provided. Prices are subject to change with prior notice. All payments are non-refundable unless otherwise stated.')}
                        </p>
                    </div>

                    <div className='py-2'>
                        <h3 className='font-semibold text-lg lg:text-xl mb-3'>{t('terms.sections.providers.title', '6. Service Provider Responsibilities')}</h3>
                        <p className='text-base lg:text-lg mb-4'>
                            {t('terms.sections.providers.content', 'Service providers are responsible for delivering high-quality services and adhering to scheduled appointments. They must be qualified and licensed as required to provide their services.')}
                        </p>
                    </div>

                    <div className='py-2'>
                        <h3 className='font-semibold text-lg lg:text-xl mb-3'>{t('terms.sections.disclaimer.title', '7. Disclaimer')}</h3>
                        <p className='text-base lg:text-lg mb-4'>
                            {t('terms.sections.disclaimer.content', 'FixMix acts as an intermediary between users and service providers. We are not responsible for the quality of services or any damages that may result from using services provided through our platform.')}
                        </p>
                    </div>

                    <div className='py-2'>
                        <h3 className='font-semibold text-lg lg:text-xl mb-3'>{t('terms.sections.termination.title', '8. Termination')}</h3>
                        <p className='text-base lg:text-lg mb-4'>
                            {t('terms.sections.termination.content', 'We may terminate or suspend your account at any time if you violate these terms. You may also terminate your account at any time through the app settings.')}
                        </p>
                    </div>

                    <div className='py-2'>
                        <h3 className='font-semibold text-lg lg:text-xl mb-3'>{t('terms.sections.changes.title', '9. Changes to Terms')}</h3>
                        <p className='text-base lg:text-lg mb-4'>
                            {t('terms.sections.changes.content', 'We reserve the right to modify these terms at any time. You will be notified of any changes through the app or email. Your continued use of the service constitutes acceptance of the updated terms.')}
                        </p>
                    </div>

                    <div className='py-2'>
                        <h3 className='font-semibold text-lg lg:text-xl mb-3'>{t('terms.sections.law.title', '10. Governing Law')}</h3>
                        <p className='text-base lg:text-lg mb-4'>
                            {t('terms.sections.law.content', 'These terms are governed by the laws of the United Arab Emirates. Any disputes will be resolved through the appropriate courts.')}
                        </p>
                    </div>

                    <div className='py-2'>
                        <h3 className='font-semibold text-lg lg:text-xl mb-3'>{t('terms.sections.contact.title', '11. Contact Information')}</h3>
                        <p className='text-base lg:text-lg mb-4'>
                            {t('terms.sections.contact.content', 'If you have any questions about these Terms of Service, please contact us at:')}
                        </p>
                        <p className='text-base lg:text-lg'>
                            {t('terms.sections.contact.email', 'Email:')} <Link href="mailto:<EMAIL>"><EMAIL></Link>
                        </p>
                    </div>

                    <div className='py-4 mt-4 border-top'>
                        <p className='text-center text-muted'>
                            {t('terms.sections.contact.updated', 'Last updated: December 2024')}
                        </p>
                        <p className='text-center'>
                            <strong>{t('terms.sections.contact.footer', 'By using FixMix, you agree to these Terms of Service.')}</strong>
                        </p>
                    </div>
                </div>
            </div>

            {/* Footer */}
            <div id="section_footer">
                <div className='text-center py-4'>
                    <Link href={router.pathname.startsWith('/ar') ? '/ar/privacy' : '/privacy'} className='btn btn-link'>
                        {t('footer.links.privacy', 'Privacy Policy')}
                    </Link>
                    <span>|</span>
                    <Link href={router.pathname.startsWith('/ar') ? '/ar/terms' : '/terms'} className='btn btn-link'>
                        {t('footer.links.terms', 'Terms of Service')}
                    </Link>
                    <span>|</span>
                    <Link href="mailto:<EMAIL>" className='btn btn-link'>
                        {t('contact', 'Contact Us')}
                    </Link>
                </div>
            </div>
        </>
    )
}

export default TermsPage
