import React from 'react';
import Image from 'next/image';

const Loading = ({
  size = 'md',
  text = 'Loading...',
  fullScreen = false,
  showLogo = false
}) => {
  const sizeClasses = {
    sm: 'spinner-border-sm',
    md: '',
    lg: 'spinner-border-lg'
  };

  const LoadingContent = () => (
    <div
      className={`d-flex flex-column align-items-center justify-content-center ${fullScreen ? 'min-vh-100' : 'py-5'}`}
      role="status"
      aria-live="polite"
      aria-label={text}
    >
      {showLogo && (
        <div className="mb-4">
          <Image
            src="/images/FixMix.png"
            alt="FixMix Logo"
            height={40}
            width={120}
            priority
          />
        </div>
      )}

      <div
        className={`spinner-border text-primary ${sizeClasses[size]}`}
        role="status"
        aria-hidden="true"
      >
        <span className="visually-hidden">{text}</span>
      </div>

      {text && (
        <p className="mt-3 text-muted" aria-live="polite">
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="position-fixed top-0 start-0 w-100 h-100 bg-white d-flex align-items-center justify-content-center" style={{ zIndex: 9999 }}>
        <LoadingContent />
      </div>
    );
  }

  return <LoadingContent />;
};

// Skeleton loader for content
export const SkeletonLoader = ({ lines = 3, height = '1rem' }) => (
  <div className="skeleton-loader">
    {Array.from({ length: lines }).map((_, index) => (
      <div 
        key={index}
        className="placeholder-glow mb-2"
      >
        <span 
          className="placeholder col-12" 
          style={{ height, display: 'block' }}
        ></span>
      </div>
    ))}
  </div>
);

// Button loading state
export const LoadingButton = ({ 
  loading = false, 
  children, 
  className = 'btn btn-primary',
  disabled = false,
  ...props 
}) => (
  <button 
    className={className}
    disabled={loading || disabled}
    {...props}
  >
    {loading && (
      <span className="spinner-border spinner-border-sm me-2" role="status">
        <span className="visually-hidden">Loading...</span>
      </span>
    )}
    {children}
  </button>
);

export default Loading;
