import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from '@/hooks/useTranslation';

const FaqItem = ({ itemNum, question, answer }) => {
  const { isRTL, locale } = useTranslation();

  return (
    <div
      className="accordion-item"
      dir={isRTL ? 'rtl' : 'ltr'}
      lang={locale}
    >
      <h2 className="h2-lg accordion-header" id={`heading_${itemNum}`}>
        <button
          className={`accordion-button collapsed ${isRTL ? 'rtl-accordion-button' : ''}`}
          type="button"
          data-bs-toggle="collapse"
          data-bs-target={`#collapse_${itemNum}`}
          aria-expanded="false"
          aria-controls={`collapse_${itemNum}`}
          aria-describedby={`answer_${itemNum}`}
          style={{
            textAlign: isRTL ? 'right' : 'left',
            direction: isRTL ? 'rtl' : 'ltr',
            fontFamily: isRTL ? "'Noto Sans Arabic', 'Cairo', '<PERSON><PERSON>', '<PERSON>jawal', sans-serif" : undefined,
            lineHeight: isRTL ? '1.8' : undefined,
            paddingRight: isRTL ? '3rem' : '1.25rem',
            paddingLeft: isRTL ? '1.25rem' : '3rem'
          }}
          onKeyDown={(e) => {
            // Enhanced keyboard navigation
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              e.currentTarget.click();
            }
          }}
        >
          <span className="visually-hidden">
            {isRTL ? 'سؤال:' : 'Question:'}
          </span>
          {question}
        </button>
      </h2>
      <div
        id={`collapse_${itemNum}`}
        className="accordion-collapse collapse"
        aria-labelledby={`heading_${itemNum}`}
        data-bs-parent="#faq_accordion"
      >
        <div
          id={`answer_${itemNum}`}
          className="accordion-body"
          role="region"
          aria-labelledby={`heading_${itemNum}`}
          style={{
            textAlign: isRTL ? 'right' : 'left',
            direction: isRTL ? 'rtl' : 'ltr',
            fontFamily: isRTL ? "'Noto Sans Arabic', 'Cairo', 'Amiri', 'Tajawal', sans-serif" : undefined,
            lineHeight: isRTL ? '1.8' : undefined
          }}
        >
          <span className="visually-hidden">
            {isRTL ? 'الجواب:' : 'Answer:'}
          </span>
          {typeof answer === 'string' ? (
            <span dangerouslySetInnerHTML={{ __html: answer }} />
          ) : (
            answer
          )}
        </div>
      </div>
    </div>
  );
};

FaqItem.propTypes = {
  itemNum: PropTypes.string.isRequired,
  question: PropTypes.string.isRequired,
  answer: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.node
  ]).isRequired
};

export default FaqItem;
