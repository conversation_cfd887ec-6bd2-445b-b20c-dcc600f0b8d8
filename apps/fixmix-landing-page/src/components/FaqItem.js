import React from 'react';
import { useTranslation } from '@/hooks/useTranslation';

const FaqItem = ({ itemNum, question, answer }) => {
  const { isRTL, locale } = useTranslation();

  return (
    <div
      className="accordion-item"
      dir={isRTL ? 'rtl' : 'ltr'}
      lang={locale}
    >
      <h2 className="h2-lg accordion-header" id={`heading_${itemNum}`}>
        <button
          className="accordion-button collapsed"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target={`#collapse_${itemNum}`}
          aria-expanded="false"
          aria-controls={`collapse_${itemNum}`}
          style={{
            textAlign: isRTL ? 'right' : 'left',
            direction: isRTL ? 'rtl' : 'ltr',
            fontFamily: isRTL ? "'Noto Sans Arabic', 'Cairo', 'Amiri', 'Tajawal', sans-serif" : undefined,
            lineHeight: isRTL ? '1.8' : undefined
          }}
        >
          {question}
        </button>
      </h2>
      <div
        id={`collapse_${itemNum}`}
        className="accordion-collapse collapse"
        aria-labelledby={`heading_${itemNum}`}
        data-bs-parent="#faq_accordion"
      >
        <div
          className="accordion-body"
          style={{
            textAlign: isRTL ? 'right' : 'left',
            direction: isRTL ? 'rtl' : 'ltr',
            fontFamily: isRTL ? "'Noto Sans Arabic', 'Cairo', 'Amiri', 'Tajawal', sans-serif" : undefined,
            lineHeight: isRTL ? '1.8' : undefined
          }}
        >
          {typeof answer === 'string' ? (
            <span dangerouslySetInnerHTML={{ __html: answer }} />
          ) : (
            answer
          )}
        </div>
      </div>
    </div>
  );
};

export default FaqItem;
