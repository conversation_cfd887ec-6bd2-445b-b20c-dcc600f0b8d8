import React from 'react';
import Review from './Review';
import AnimatedSection from './AnimatedSection';
import { useTranslation } from '@/hooks/useTranslation';
import styles from '@/styles/Home.module.scss';

const Reviews = () => {
  const { t, isRTL } = useTranslation();

  return (
    <div className={`${styles.section_reviews} mt-4`} dir={isRTL ? 'rtl' : 'ltr'}>
      <AnimatedSection animation="fadeInUp">
        <h2 className="h2-lg my-lg pt-4 text-center text-primary">
          {t('reviews.title', 'What People Are Saying')}
        </h2>
        <div className="text-center mb-5">
          <p className="lead text-muted">
            {t('reviews.subtitle', 'Join thousands of satisfied customers who trust FixMix for their service needs')}
          </p>
        </div>
      </AnimatedSection>
      <div className="container">
        <div className="row">
          <AnimatedSection animation="fadeInUp" delay={100}>
            <Review
              body={t('reviews.items.sarah.text', 'Finding a reliable electrician used to be difficult, but FixMix made it simple. Quick response and professional service!')}
              name={t('reviews.items.sarah.name', 'Sean A')}
              role={t('reviews.items.sarah.service', 'Electrical Services')}
            />
          </AnimatedSection>
          <AnimatedSection animation="fadeInUp" delay={200}>
            <Review
              body={t('reviews.items.ahmed.text', 'I needed urgent plumbing work, and within minutes, I got a skilled professional. Payment was smooth and secure.')}
              name={t('reviews.items.ahmed.name', 'Fatima R')}
              role={t('reviews.items.ahmed.service', 'Plumbing Services')}
            />
          </AnimatedSection>
          <AnimatedSection animation="fadeInUp" delay={300}>
            <Review
              body={t('reviews.items.maria.text', 'Booking a handyman has never been easier! I use FixMix regularly for home maintenance.')}
              name={t('reviews.items.maria.name', 'Paul M')}
              role={t('reviews.items.maria.service', 'Handyman Services')}
            />
          </AnimatedSection>
          <AnimatedSection animation="fadeInUp" delay={400}>
            <Review
              body={t('reviews.items.mohammed.text', 'As a vendor, FixMix gives me direct access to customers, allowing me to grow my business and get paid fast.')}
              name={t('reviews.items.mohammed.name', 'Mohammed H')}
              role={t('reviews.items.mohammed.service', 'Service Provider')}
            />
          </AnimatedSection>
          <AnimatedSection animation="fadeInUp" delay={500}>
            <Review
              body={t('reviews.items.sara.text', 'Excellent service! The chat feature made it easy to discuss pricing and job details before booking.')}
              name={t('reviews.items.sara.name', 'Sara B')}
              role={t('reviews.items.sara.service', 'Customer')}
              className="d-none d-md-block"
            />
          </AnimatedSection>
          <AnimatedSection animation="fadeInUp" delay={600}>
            <Review
              body="I like how I can track my vendor's location and ensure they arrive on time. A must-have App!"
              name="David K"
              role="Customer"
              className="d-none d-lg-block"
            />
          </AnimatedSection>
        </div>
      </div>
    </div>
  );
};

export default Reviews;
