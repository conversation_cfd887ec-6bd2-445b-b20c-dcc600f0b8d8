import React from 'react';
import AnimatedSection, { StaggeredAnimatedSection, CountUpAnimation } from './AnimatedSection';
import { useTranslation } from '@/hooks/useTranslation';
import styles from '@/styles/Home.module.scss';

const WhatMakesFixMixStandOut = () => {
  const { t, isRTL } = useTranslation();

  const features = [
    {
      icon: '🔧',
      title: t('standout.features.verified.title', 'Verified Professionals'),
      description: t('standout.features.verified.description', 'All vendors are background-checked and verified for your safety and peace of mind.')
    },
    {
      icon: '💬',
      title: t('standout.features.chat.title', 'Direct Communication'),
      description: t('standout.features.chat.description', 'Chat directly with vendors to discuss requirements, pricing, and timing before booking.')
    },
    {
      icon: '💳',
      title: t('standout.features.payment.title', 'Secure Payments'),
      description: t('standout.features.payment.description', 'Pay safely through our secure wallet system with multiple payment options.')
    },
    {
      icon: '📍',
      title: t('standout.features.tracking.title', 'Real-time Tracking'),
      description: t('standout.features.tracking.description', 'Track your service provider in real-time and get updates on job progress.')
    }
  ];

  const stats = [
    {
      number: 10000,
      suffix: '+',
      label: t('standout.stats.customers', 'Happy Customers')
    },
    {
      number: 5000,
      suffix: '+',
      label: t('standout.stats.vendors', 'Verified Vendors')
    },
    {
      number: 50000,
      suffix: '+',
      label: t('standout.stats.services', 'Services Completed')
    },
    {
      number: 98,
      suffix: '%',
      label: t('standout.stats.satisfaction', 'Satisfaction Rate')
    }
  ];

  return (
    <div className={`${styles.section_standout} py-5`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="container">
        <AnimatedSection animation="fadeInUp">
          <div className="text-center mb-5">
            <h2 className="h2-lg text-primary mb-4">
              {t('standout.title', 'What Makes FixMix Stand Out?')}
            </h2>
            <p className="lead text-muted">
              {t('standout.subtitle', 'Discover why thousands of users and vendors choose FixMix for their service needs')}
            </p>
          </div>
        </AnimatedSection>

        {/* Features Grid */}
        <div className="row mb-5">
          {features.map((feature, index) => (
            <div key={index} className="col-md-6 col-lg-3 mb-4">
              <AnimatedSection animation="fadeInUp" delay={index * 100}>
                <div className="text-center h-100 p-4">
                  <div className="feature-icon mb-3" style={{ fontSize: '3rem' }}>
                    {feature.icon}
                  </div>
                  <h4 className="h5 mb-3">{feature.title}</h4>
                  <p className="text-muted">{feature.description}</p>
                </div>
              </AnimatedSection>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <AnimatedSection animation="fadeInUp">
          <div className="row text-center">
            {stats.map((stat, index) => (
              <div key={index} className="col-6 col-md-3 mb-4">
                <div className="stat-item">
                  <div className="stat-number h2 text-primary mb-2">
                    <CountUpAnimation 
                      end={stat.number} 
                      suffix={stat.suffix}
                      duration={2000}
                    />
                  </div>
                  <div className="stat-label text-muted">{stat.label}</div>
                </div>
              </div>
            ))}
          </div>
        </AnimatedSection>

        {/* How It Works Steps */}
        <AnimatedSection animation="fadeInUp" delay={200}>
          <div className="text-center mt-5 mb-4">
            <h3 className="h3 text-primary">
              {t('standout.howItWorks.title', 'How It Works')}
            </h3>
          </div>
        </AnimatedSection>

        <StaggeredAnimatedSection className="row" staggerDelay={150}>
          <div className="col-md-4 text-center mb-4">
            <div className="step-number bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                 style={{ width: '60px', height: '60px', fontSize: '1.5rem', fontWeight: 'bold' }}>
              1
            </div>
            <h5>{t('standout.howItWorks.step1.title', 'Browse & Select')}</h5>
            <p className="text-muted">
              {t('standout.howItWorks.step1.description', 'Browse available services and select the right vendor for your needs')}
            </p>
          </div>
          <div className="col-md-4 text-center mb-4">
            <div className="step-number bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                 style={{ width: '60px', height: '60px', fontSize: '1.5rem', fontWeight: 'bold' }}>
              2
            </div>
            <h5>{t('standout.howItWorks.step2.title', 'Chat & Confirm')}</h5>
            <p className="text-muted">
              {t('standout.howItWorks.step2.description', 'Discuss details, pricing, and timing directly with your chosen vendor')}
            </p>
          </div>
          <div className="col-md-4 text-center mb-4">
            <div className="step-number bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                 style={{ width: '60px', height: '60px', fontSize: '1.5rem', fontWeight: 'bold' }}>
              3
            </div>
            <h5>{t('standout.howItWorks.step3.title', 'Get Service & Pay')}</h5>
            <p className="text-muted">
              {t('standout.howItWorks.step3.description', 'Receive quality service and pay securely through the app')}
            </p>
          </div>
        </StaggeredAnimatedSection>
      </div>
    </div>
  );
};

export default WhatMakesFixMixStandOut;
