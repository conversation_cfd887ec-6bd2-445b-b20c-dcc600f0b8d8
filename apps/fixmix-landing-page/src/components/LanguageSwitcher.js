import React, { useState } from 'react';
import useTranslation from '@/hooks/useTranslation';

const LanguageSwitcher = ({ className = '' }) => {
  const { locale, changeLanguage, availableLanguages } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const languages = {
    en: {
      name: 'English',
      flag: '🇺🇸',
      code: 'EN'
    },
    ar: {
      name: 'العربية',
      flag: '🇸🇦',
      code: 'AR'
    }
  };

  const handleLanguageChange = (newLocale) => {
    changeLanguage(newLocale);
    setIsOpen(false);
  };

  const currentLanguage = languages[locale];

  return (
    <div className={`language-switcher ${className}`}>
      <div className="dropdown">
        <button
          className="btn btn-outline-secondary btn-sm dropdown-toggle"
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          onBlur={() => setTimeout(() => setIsOpen(false), 150)}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              setIsOpen(false);
            } else if (e.key === 'ArrowDown' && !isOpen) {
              setIsOpen(true);
            }
          }}
          aria-expanded={isOpen}
          aria-haspopup="true"
          aria-label={`Select language. Current language: ${currentLanguage.name}`}
          id="language-switcher-button"
        >
          <span className="flag-icon me-2">{currentLanguage.flag}</span>
          <span className="language-code">{currentLanguage.code}</span>
        </button>
        
        {isOpen && (
          <ul
            className="dropdown-menu show"
            role="menu"
            aria-labelledby="language-switcher-button"
          >
            {availableLanguages.map((langCode, index) => {
              const lang = languages[langCode];
              const isActive = langCode === locale;

              return (
                <li key={langCode} role="none">
                  <button
                    className={`dropdown-item ${isActive ? 'active' : ''}`}
                    onClick={() => handleLanguageChange(langCode)}
                    disabled={isActive}
                    role="menuitem"
                    aria-current={isActive ? 'true' : 'false'}
                    onKeyDown={(e) => {
                      if (e.key === 'Escape') {
                        setIsOpen(false);
                        document.getElementById('language-switcher-button')?.focus();
                      } else if (e.key === 'ArrowDown') {
                        const nextItem = e.currentTarget.parentElement?.nextElementSibling?.querySelector('button');
                        nextItem?.focus();
                      } else if (e.key === 'ArrowUp') {
                        const prevItem = e.currentTarget.parentElement?.previousElementSibling?.querySelector('button');
                        prevItem?.focus();
                      }
                    }}
                  >
                    <span className="flag-icon me-2">{lang.flag}</span>
                    <span className="language-name">{lang.name}</span>
                    {isActive && (
                      <span className="ms-auto">
                        <i className="bi bi-check" aria-hidden="true"></i>
                      </span>
                    )}
                  </button>
                </li>
              );
            })}
          </ul>
        )}
      </div>
    </div>
  );
};

// Compact version for mobile
export const CompactLanguageSwitcher = ({ className = '' }) => {
  const { locale, changeLanguage } = useTranslation();

  const toggleLanguage = () => {
    const newLocale = locale === 'en' ? 'ar' : 'en';
    changeLanguage(newLocale);
  };

  const languages = {
    en: { flag: '🇺🇸', code: 'EN' },
    ar: { flag: '🇸🇦', code: 'AR' }
  };

  const currentLanguage = languages[locale];

  return (
    <button
      className={`btn btn-outline-secondary btn-sm ${className}`}
      onClick={toggleLanguage}
      aria-label={`Switch to ${locale === 'en' ? 'Arabic' : 'English'}`}
      title={`Switch to ${locale === 'en' ? 'Arabic' : 'English'}`}
    >
      <span className="flag-icon me-1">{currentLanguage.flag}</span>
      <span className="language-code">{currentLanguage.code}</span>
    </button>
  );
};

export default LanguageSwitcher;
