import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error caught by boundary:', error, errorInfo);
    }
    
    // You can also log the error to an error reporting service here
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI
      return (
        <div className="error-boundary">
          <div className="container text-center py-5">
            <div className="row justify-content-center">
              <div className="col-md-6">
                <h2 className="text-danger mb-4">Oops! Something went wrong</h2>
                <p className="text-muted mb-4">
                  We're sorry, but something unexpected happened. Please try refreshing the page.
                </p>
                <button 
                  className="btn btn-primary me-3"
                  onClick={() => window.location.reload()}
                >
                  Refresh Page
                </button>
                <button 
                  className="btn btn-outline-secondary"
                  onClick={() => window.location.href = '/'}
                >
                  Go Home
                </button>
                
                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <details className="mt-4 text-start">
                    <summary className="text-danger">Error Details (Development Only)</summary>
                    <pre className="bg-light p-3 mt-2 text-small">
                      {this.state.error && this.state.error.toString()}
                      <br />
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </details>
                )}
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
