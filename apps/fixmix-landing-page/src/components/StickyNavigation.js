import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { smoothScrollTo } from '@/utils/smoothScroll';
import { trackCTAClick } from '@/utils/analytics';
import { CompactLanguageSwitcher } from './LanguageSwitcher';
import { useTranslation } from '@/hooks/useTranslation';

const StickyNavigation = () => {
  const { t } = useTranslation();
  const [isVisible, setIsVisible] = useState(false);
  const [activeSection, setActiveSection] = useState('');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // Show sticky nav after scrolling past hero section
      const heroHeight = window.innerHeight * 0.8;
      setIsVisible(window.scrollY > heroHeight);

      // Update active section
      const sections = ['features', 'reviews', 'what-makes-fixmix-stand-out', 'faq'];
      const scrollPosition = window.scrollY + 100;

      sections.forEach((sectionId) => {
        const element = document.getElementById(sectionId);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(sectionId);
          }
        }
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavClick = (sectionId, label) => {
    trackCTAClick(`Nav - ${label}`, 'Sticky Navigation');
    smoothScrollTo(sectionId, 80); // 80px offset for sticky nav
    setIsMobileMenuOpen(false);
  };

  const handleScrollToTop = () => {
    trackCTAClick('Logo', 'Sticky Navigation');
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setIsMobileMenuOpen(false);
  };

  if (!isVisible) return null;

  return (
    <nav className={`sticky-nav ${isVisible ? 'sticky-nav-visible' : ''}`}>
      <div className="container">
        <div className="sticky-nav-content">
          {/* Logo */}
          <div className="sticky-nav-logo" onClick={handleScrollToTop}>
            <Image
              src="/images/FixMix.png"
              alt={t('navigation.logo.alt', 'FixMix Logo')}
              height={32}
              width={100}
              priority
            />
          </div>

          {/* Desktop Navigation */}
          <div className="sticky-nav-links d-none d-lg-flex">
            <button
              className={`nav-link ${activeSection === 'features' ? 'active' : ''}`}
              onClick={() => handleNavClick('features', 'Features')}
            >
              {t('navigation.links.features', 'Features')}
            </button>
            <button
              className={`nav-link ${activeSection === 'reviews' ? 'active' : ''}`}
              onClick={() => handleNavClick('reviews', 'Reviews')}
            >
              {t('navigation.links.reviews', 'Reviews')}
            </button>
            <button
              className={`nav-link ${activeSection === 'what-makes-fixmix-stand-out' ? 'active' : ''}`}
              onClick={() => handleNavClick('what-makes-fixmix-stand-out', 'How It Works')}
            >
              {t('navigation.links.howItWorks', 'How It Works')}
            </button>
            <button
              className={`nav-link ${activeSection === 'faq' ? 'active' : ''}`}
              onClick={() => handleNavClick('faq', 'FAQ')}
            >
              {t('navigation.links.faq', 'FAQ')}
            </button>
          </div>

          {/* CTA Buttons */}
          <div className="sticky-nav-cta d-none d-md-flex align-items-center">
            <CompactLanguageSwitcher className="me-3" />
            <Link
              href={process.env.NEXT_PUBLIC_APP_STORE_URL || 'https://apps.apple.com/app/fixmix'}
              target="_blank"
              rel="noopener noreferrer"
              className="btn btn-primary btn-sm"
              onClick={() => trackCTAClick('Download App', 'Sticky Navigation')}
            >
              {t('navigation.cta.label', 'Download App')}
            </Link>
          </div>

          {/* Mobile Menu Toggle */}
          <button
            className="mobile-menu-toggle d-lg-none"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <span className={`hamburger ${isMobileMenuOpen ? 'active' : ''}`}>
              <span></span>
              <span></span>
              <span></span>
            </span>
          </button>
        </div>

        {/* Mobile Menu */}
        <div className={`mobile-menu d-lg-none ${isMobileMenuOpen ? 'mobile-menu-open' : ''}`}>
          <div className="mobile-menu-content">
            <button
              className={`mobile-nav-link ${activeSection === 'features' ? 'active' : ''}`}
              onClick={() => handleNavClick('features', 'Features')}
            >
              {t('navigation.links.features', 'Features')}
            </button>
            <button
              className={`mobile-nav-link ${activeSection === 'reviews' ? 'active' : ''}`}
              onClick={() => handleNavClick('reviews', 'Reviews')}
            >
              {t('navigation.links.reviews', 'Reviews')}
            </button>
            <button
              className={`mobile-nav-link ${activeSection === 'what-makes-fixmix-stand-out' ? 'active' : ''}`}
              onClick={() => handleNavClick('what-makes-fixmix-stand-out', 'How It Works')}
            >
              {t('navigation.links.howItWorks', 'How It Works')}
            </button>
            <button
              className={`mobile-nav-link ${activeSection === 'faq' ? 'active' : ''}`}
              onClick={() => handleNavClick('faq', 'FAQ')}
            >
              {t('navigation.links.faq', 'FAQ')}
            </button>
            <div className="mobile-menu-cta">
              <Link
                href={process.env.NEXT_PUBLIC_APP_STORE_URL || 'https://apps.apple.com/app/fixmix'}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-primary w-100"
                onClick={() => {
                  trackCTAClick('Download App', 'Mobile Navigation');
                  setIsMobileMenuOpen(false);
                }}
              >
                {t('navigation.cta.label', 'Download App')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default StickyNavigation;
