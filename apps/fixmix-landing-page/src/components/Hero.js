import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Image from 'next/image';
import OptimizedImage, { HeroImage, PhoneScreenshot } from '@/components/OptimizedImage';
import styles from '@/styles/Hero.module.scss';
import Link from 'next/link';
import AppLinks from '@/components/AppLinks';
import { trackCTAClick } from '@/utils/analytics';
import { useTranslation } from '@/hooks/useTranslation';

const Hero = ({
  title = 'Your One-Stop Solution for Getting Work Done',
  subtitle = 'FixMix is an on-demand service marketplace that lets users book expert vendors for various tasks like plumbing, electrical work, painting, carpentry, and more.'
} = {}) => {
  const { t } = useTranslation();

  const handleLearnMoreClick = () => {
    trackCTAClick('Learn More', 'Hero Section');
    // Smooth scroll to features section
    const featuresSection = document.getElementById('features');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleHowItWorksClick = () => {
    trackCTAClick('How It Works', 'Hero Section');
    const howItWorksSection = document.getElementById('what-makes-fixmix-stand-out');
    if (howItWorksSection) {
      howItWorksSection.scrollIntoView({ behavior: 'smooth' });
    }
  };
  return (
    <>
      <div id={styles.section_headline_container}>
        <div id={styles.section_headline}>
            <div id={styles.qr_code} className={`d-none d-lg-block`}>
              {/* <Image
                  src="/images/FixMixAppQR.png"
                  alt="FixMix Logo"
                  height={100}
                  width={100}
                  className="pr-3"
                /> */}
            </div>
            <span className={styles.vLine1}>
              <Image
                src="/images/node-bright.svg"
                width={15}
                height={15}
                alt="Circle used for background style"
                className={`${styles.node_sm1} d-none d-md-block`}
              />
            </span>
            <span className={`${styles.vLine2} d-none d-md-block`}></span>
            <span className={styles.vLine3}></span>
            <span className={`${styles.vLine4} d-none d-md-block`}></span>
            <span className={`${styles.vLine5} d-none d-md-block`}></span>
            <span className={styles.vLine6}>
              <Image
                src="/images/node-bright.svg"
                width={15}
                height={15}
                alt="Circle used for background style"
                className={styles.node_sm2}
              />
            </span>

            <nav className="navbar text-center text-md-left">
              <Link className="navbar-brand mx-auto mx-md-4" href="/">
                <Image
                  src="/images/FixMix.png"
                  alt="FixMix Logo"
                  height={30}
                  width={100}
                  className=""
                />
              </Link>
            </nav>


            <div className='text-center text-light'>
              {/* Hero Badge */}
              <div className="hero-badge mb-4">
                <span className="badge bg-danger bg-opacity-15 text-danger px-3 py-2 rounded-pill border border-danger border-opacity-30">
                  {t('hero.badge', '🎉 متوفر الآن على iOS و Android')}
                </span>
              </div>

              <h1 className='pt-1 px-3 pb-3 hero-title'>{title}</h1>
              <div className='row justify-content-center px-3'>
                <p className='col-md-10 col-lg-6 col-xl-6 hero-subtitle'>
                  {subtitle}
                </p>
              </div>

              <AppLinks />

              {/* Additional CTAs */}
              <div className="hero-additional-ctas mt-4">
                <button
                  className="btn btn-outline-light btn-lg me-3 mb-2"
                  onClick={handleLearnMoreClick}
                >
                  {t('hero.cta.primary.label', 'Learn More')}
                </button>
                <button
                  className="btn btn-link text-light text-decoration-none"
                  onClick={handleHowItWorksClick}
                >
                  {t('hero.cta.secondary.label', 'How It Works →')}
                </button>
              </div>


            </div>

            <div className={`${styles.cover_imgs_container}  text-center d-flex justify-content-center animate-slide-up`}>
              <PhoneScreenshot
                src="/images/phones/Home.png"
                alt="Screenshot of FixMix app showing the home screen with service categories."
                height={700}
                width={300}
                className='phone_shadow mt-5 mx-5 d-none d-md-block'
                style={{ height: "auto" }}
                priority={true}
                placeholder="empty"
              />
              <PhoneScreenshot
                src="/images/phones/Splash Screen.png"
                alt="Screenshot of FixMix app showing the splash screen and branding."
                height={700}
                width={300}
                className='phone_shadow mt-5 mx-5 d-none d-md-block'
                style={{ height: "auto" }}
                priority={true}
                placeholder="empty"
              />
              <PhoneScreenshot
                src="/images/phones/Home.png"
                alt="Screenshot of FixMix app showing the service booking interface."
                height={700}
                width={300}
                className='phone_shadow mt-5 mx-5 d-none d-md-block'
                style={{ height: "auto" }}
                lazy={true}
                placeholder="empty"
              />
            </div>
        </div>
      </div>
    </>
  );
};

Hero.propTypes = {
  title: PropTypes.string,
  subtitle: PropTypes.string
};

Hero.defaultProps = {
  title: 'Your One-Stop Solution for Getting Work Done',
  subtitle: 'FixMix is an on-demand service marketplace that lets users book expert vendors for various tasks like plumbing, electrical work, painting, carpentry, and more.'
};

export default Hero;
