import React from 'react';

const SkipToContent = () => {
  return (
    <a 
      href="#main-content" 
      className="skip-to-content visually-hidden-focusable"
      style={{
        position: 'absolute',
        top: '-40px',
        left: '6px',
        background: '#000',
        color: '#fff',
        padding: '8px',
        textDecoration: 'none',
        zIndex: 10000,
        borderRadius: '4px'
      }}
      onFocus={(e) => {
        e.target.style.top = '6px';
      }}
      onBlur={(e) => {
        e.target.style.top = '-40px';
      }}
    >
      Skip to main content
    </a>
  );
};

export default SkipToContent;
