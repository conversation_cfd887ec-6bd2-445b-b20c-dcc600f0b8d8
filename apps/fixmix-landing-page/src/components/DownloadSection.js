import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import AnimatedSection from './AnimatedSection';
import { useTranslation } from '@/hooks/useTranslation';
import styles from '@/styles/Home.module.scss';

const DownloadSection = () => {
  const { t, isRTL } = useTranslation();

  const appStoreUrl = process.env.NEXT_PUBLIC_APP_STORE_URL || 'https://apps.apple.com/app/fixmix';
  const googlePlayUrl = process.env.NEXT_PUBLIC_GOOGLE_PLAY_URL || 'https://play.google.com/store/apps/details?id=com.fixmix';

  return (
    <div className={`${styles.section_download} py-5 bg-light`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="container">
        <div className="row align-items-center">
          <div className="col-lg-6">
            <AnimatedSection animation="fadeInLeft">
              <div className="download-content">
                <h2 className="h2-lg text-primary mb-4">
                  {t('download.title', 'Get Professional Help Instantly – Anytime, Anywhere!')}
                </h2>
                <p className="lead text-muted mb-4">
                  {t('download.subtitle', 'Download the FixMix app and connect with trusted professionals in your area')}
                </p>
                
                <div className="download-buttons d-flex flex-column flex-sm-row gap-3">
                  <Link
                    href={appStoreUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-download"
                  >
                    <Image
                      src="/images/download_apple.png"
                      alt={t('download.stores.appStore', 'Download on the App Store')}
                      width={200}
                      height={60}
                      className="download-badge"
                    />
                  </Link>

                  <Link
                    href={googlePlayUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-download"
                  >
                    <Image
                      src="/images/download_google.png"
                      alt={t('download.stores.googlePlay', 'Get it on Google Play')}
                      width={200}
                      height={60}
                      className="download-badge"
                    />
                  </Link>
                </div>

                <div className="download-features mt-4">
                  <div className="row">
                    <div className="col-sm-6 mb-3">
                      <div className="d-flex align-items-center">
                        <div className="feature-check me-3">
                          <i className="bi bi-check-circle-fill text-success"></i>
                        </div>
                        <span className="text-muted">
                          {t('download.features.free', 'Free to download')}
                        </span>
                      </div>
                    </div>
                    <div className="col-sm-6 mb-3">
                      <div className="d-flex align-items-center">
                        <div className="feature-check me-3">
                          <i className="bi bi-check-circle-fill text-success"></i>
                        </div>
                        <span className="text-muted">
                          {t('download.features.instant', 'Instant booking')}
                        </span>
                      </div>
                    </div>
                    <div className="col-sm-6 mb-3">
                      <div className="d-flex align-items-center">
                        <div className="feature-check me-3">
                          <i className="bi bi-check-circle-fill text-success"></i>
                        </div>
                        <span className="text-muted">
                          {t('download.features.secure', 'Secure payments')}
                        </span>
                      </div>
                    </div>
                    <div className="col-sm-6 mb-3">
                      <div className="d-flex align-items-center">
                        <div className="feature-check me-3">
                          <i className="bi bi-check-circle-fill text-success"></i>
                        </div>
                        <span className="text-muted">
                          {t('download.features.support', '24/7 support')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          </div>
          
          <div className="col-lg-6">
            <AnimatedSection animation="fadeInRight" delay={200}>
              <div className="download-mockup text-center">
                <Image
                  src="/images/app-mockup.svg"
                  alt="FixMix App Screenshots"
                  width={400}
                  height={500}
                  className="img-fluid"
                  style={{ maxWidth: '100%', height: 'auto' }}
                />
              </div>
            </AnimatedSection>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadSection;
