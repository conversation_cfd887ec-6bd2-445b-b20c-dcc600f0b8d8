import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { trackEvent } from '@/utils/analytics';

const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  priority = false,
  lazy = true,
  placeholder = 'empty',
  blurDataURL,
  className = '',
  style = {},
  sizes,
  quality = 75,
  onLoad,
  onError,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(!lazy || priority);
  const imgRef = useRef(null);

  // Generate blur data URL if not provided
  const generateBlurDataURL = (w = 8, h = 8) => {
    const canvas = document.createElement('canvas');
    canvas.width = w;
    canvas.height = h;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#f3f4f6';
    ctx.fillRect(0, 0, w, h);
    return canvas.toDataURL();
  };

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || priority || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      if (imgRef.current) {
        observer.unobserve(imgRef.current);
      }
    };
  }, [lazy, priority, isInView]);

  const handleLoad = (event) => {
    setIsLoaded(true);
    
    // Track image load performance
    if (performance && performance.now) {
      const loadTime = performance.now();
      trackEvent('image_performance', 'Image Load', src, Math.round(loadTime));
    }

    if (onLoad) {
      onLoad(event);
    }
  };

  const handleError = (event) => {
    setHasError(true);
    console.warn('Image failed to load:', src);
    
    // Track image errors
    trackEvent('image_error', 'Image Error', src);

    if (onError) {
      onError(event);
    }
  };

  // Responsive sizes if not provided
  const responsiveSizes = sizes || (
    width && height 
      ? `(max-width: 768px) ${Math.min(width, 400)}px, ${width}px`
      : '100vw'
  );

  // Error fallback
  if (hasError) {
    return (
      <div 
        ref={imgRef}
        className={`image-error-fallback ${className}`}
        style={{
          width: width || '100%',
          height: height || 'auto',
          backgroundColor: '#f3f4f6',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#6b7280',
          fontSize: '14px',
          ...style
        }}
        role="img"
        aria-label={alt || 'Image failed to load'}
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
        </svg>
        <span className="ms-2">Image unavailable</span>
      </div>
    );
  }

  // Loading placeholder
  if (!isInView) {
    return (
      <div 
        ref={imgRef}
        className={`image-placeholder ${className}`}
        style={{
          width: width || '100%',
          height: height || 'auto',
          backgroundColor: '#f3f4f6',
          ...style
        }}
        role="img"
        aria-label={`Loading ${alt || 'image'}`}
      />
    );
  }

  return (
    <div ref={imgRef} className={`image-container ${isLoaded ? 'loaded' : 'loading'}`}>
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        priority={priority}
        placeholder={blurDataURL ? placeholder : 'empty'}
        blurDataURL={blurDataURL}
        className={className}
        style={style}
        sizes={responsiveSizes}
        quality={quality}
        onLoad={handleLoad}
        onError={handleError}
        {...props}
      />
      
      {/* Loading overlay */}
      {!isLoaded && (
        <div className="image-loading-overlay">
          <div className="loading-spinner" />
        </div>
      )}
    </div>
  );
};

// Predefined image variants
export const HeroImage = (props) => (
  <OptimizedImage
    priority={true}
    quality={85}
    sizes="(max-width: 768px) 100vw, 50vw"
    {...props}
  />
);

export const FeatureIcon = (props) => (
  <OptimizedImage
    width={48}
    height={48}
    quality={90}
    lazy={true}
    {...props}
  />
);

export const PhoneScreenshot = (props) => (
  <OptimizedImage
    quality={80}
    sizes="(max-width: 768px) 300px, 400px"
    style={{ height: "auto" }}
    {...props}
  />
);

export const Avatar = (props) => (
  <OptimizedImage
    width={64}
    height={64}
    quality={85}
    style={{ borderRadius: '50%' }}
    {...props}
  />
);

export default OptimizedImage;
