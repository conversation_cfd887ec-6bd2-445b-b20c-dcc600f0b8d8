import React from 'react';
import { trackEvent } from '@/utils/analytics';

const Card = ({ 
  children, 
  variant = 'default',
  size = 'md',
  hover = true,
  className = '',
  onClick,
  ...props 
}) => {
  const baseClasses = 'card-modern';
  const variantClasses = `card-${variant}`;
  const sizeClasses = `card-${size}`;
  const hoverClasses = hover ? 'card-hover' : '';
  const clickableClasses = onClick ? 'card-clickable' : '';
  
  const cardClasses = [
    baseClasses,
    variantClasses,
    sizeClasses,
    hoverClasses,
    clickableClasses,
    className
  ].filter(Boolean).join(' ');

  return (
    <div 
      className={cardClasses}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick(e);
        }
      } : undefined}
      {...props}
    >
      {children}
    </div>
  );
};

// Feature Card Component
export const FeatureCard = ({
  icon,
  title,
  description,
  href,
  className = ''
}) => {
  const handleClick = () => {
    trackEvent('feature_card_click', 'Features', title);
    if (href) {
      window.open(href, '_blank');
    }
  };

  return (
    <Card
      variant="feature"
      hover={true}
      className={className}
      onClick={href ? handleClick : undefined}
    >
      {icon && (
        <div className="card-icon">
          {typeof icon === 'string' ? (
            <img src={icon} alt="" className="icon-image" />
          ) : (
            icon
          )}
        </div>
      )}
      <div className="card-content">
        <h3 className="card-title">{title}</h3>
        <p className="card-description">{description}</p>
      </div>
    </Card>
  );
};

// Testimonial Card Component
export const TestimonialCard = ({ 
  quote, 
  author, 
  role, 
  avatar,
  rating = 5,
  className = '' 
}) => {
  return (
    <Card variant="testimonial" className={className}>
      <div className="testimonial-content">
        <div className="testimonial-rating">
          {Array.from({ length: 5 }).map((_, i) => (
            <span 
              key={i} 
              className={`star ${i < rating ? 'star-filled' : 'star-empty'}`}
              aria-hidden="true"
            >
              ★
            </span>
          ))}
        </div>
        <blockquote className="testimonial-quote">
          "{quote}"
        </blockquote>
        <div className="testimonial-author">
          {avatar && (
            <img 
              src={avatar} 
              alt={author}
              className="author-avatar"
            />
          )}
          <div className="author-info">
            <cite className="author-name">{author}</cite>
            {role && <span className="author-role">{role}</span>}
          </div>
        </div>
      </div>
    </Card>
  );
};

// Service Card Component
export const ServiceCard = ({ 
  image, 
  title, 
  description, 
  price,
  features = [],
  ctaText = "Learn More",
  onCTA,
  className = '' 
}) => {
  return (
    <Card variant="service" className={className}>
      {image && (
        <div className="service-image">
          <img src={image} alt={title} />
        </div>
      )}
      <div className="service-content">
        <h3 className="service-title">{title}</h3>
        <p className="service-description">{description}</p>
        
        {features.length > 0 && (
          <ul className="service-features">
            {features.map((feature, index) => (
              <li key={index} className="feature-item">
                <span className="feature-check">✓</span>
                {feature}
              </li>
            ))}
          </ul>
        )}
        
        <div className="service-footer">
          {price && (
            <div className="service-price">{price}</div>
          )}
          <button 
            className="btn btn-primary btn-sm"
            onClick={onCTA}
          >
            {ctaText}
          </button>
        </div>
      </div>
    </Card>
  );
};

export default Card;
