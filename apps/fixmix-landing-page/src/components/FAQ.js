import React from 'react';
import PropTypes from 'prop-types';
import FaqItem from './FaqItem';
import AnimatedSection from './AnimatedSection';
import { useTranslation } from '@/hooks/useTranslation';
import styles from '@/styles/Home.module.scss';

const FAQ = () => {
  const { t, isRTL } = useTranslation();

  return (
    <section
      className={`${styles.section_faq} mt-4`}
      dir={isRTL ? 'rtl' : 'ltr'}
      aria-labelledby="faq-heading"
    >
      <AnimatedSection animation="fadeInUp">
        <h2
          id="faq-heading"
          className="h2-lg my-lg text-center text-primary pt-4"
        >
          {t('faq.title', "FAQ's")}
        </h2>
        <div className="text-center mb-5">
          <p className="lead text-muted">
            {t('faq.subtitle', 'Find answers to commonly asked questions about FixMix')}
          </p>
        </div>
      </AnimatedSection>
      <div className="row justify-content-center">
        <div className="col-10 col-md-8 col-lg-6">
          <AnimatedSection animation="fadeInUp" delay={200}>
            <div
              className="accordion"
              id="faq_accordion"
              role="region"
              aria-labelledby="faq-heading"
            >
              <FaqItem
                itemNum="one"
                question={t('faq.items.booking.question', 'How do I book a service on FixMix?')}
                answer={t('faq.items.booking.answer', 'Simply browse categories, chat with a vendor, and confirm your booking.')}
              />
              <FaqItem
                itemNum="two"
                question={t('faq.items.payments.question', 'How do vendors receive payments?')}
                answer={t('faq.items.payments.answer', 'Payments are credited to the FixMix wallet, and vendors can withdraw earnings anytime.')}
              />
              <FaqItem
                itemNum="three"
                question={t('faq.items.verification.question', 'Are vendors background-checked?')}
                answer={t('faq.items.verification.answer', 'Yes, all vendors go through a verification process before listing their services.')}
              />
              <FaqItem
                itemNum="four"
                question={t('faq.items.tracking.question', 'Can I track my service request?')}
                answer={t('faq.items.tracking.answer', 'Absolutely! You get real-time updates on your service request and can communicate directly with the vendor.')}
              />
              <FaqItem
                itemNum="five"
                question={t('faq.items.parts.question', 'What if I need additional spare parts?')}
                answer={t('faq.items.parts.answer', 'Vendors can bring additional parts upon request. You can discuss this during the chat before starting the service.')}
              />
            </div>
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
