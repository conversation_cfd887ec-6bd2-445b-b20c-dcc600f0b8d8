# Contributing to FixMix Landing Page

Thank you for your interest in contributing to the FixMix Landing Page! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- Git

### Setup Development Environment
1. Fork the repository
2. Clone your fork:
   ```bash
   git clone https://github.com/your-username/FixMix.git
   cd FixMix/apps/fixmix-landing-page
   ```
3. Install dependencies:
   ```bash
   npm install
   ```
4. Start development server:
   ```bash
   npm run dev
   ```

## 📋 Development Guidelines

### Code Standards
- **Components**: Use functional components with hooks
- **Styling**: Use SCSS modules and Bootstrap classes
- **Accessibility**: Follow WCAG 2.1 AA guidelines
- **Internationalization**: Support both LTR and RTL layouts
- **Testing**: Write tests for new components and features

### File Naming Conventions
- Components: `PascalCase.js` (e.g., `Hero.js`, `FaqItem.js`)
- Styles: `kebab-case.scss` (e.g., `faq-enhancements.scss`)
- Tests: `ComponentName.test.js`
- Utilities: `camelCase.js`

### Component Structure
```javascript
import React from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import styles from './ComponentName.module.scss';

const ComponentName = ({ prop1, prop2 }) => {
  const { t, isRTL } = useTranslation();

  return (
    <div className={styles.container} dir={isRTL ? 'rtl' : 'ltr'}>
      <h2>{t('section.title', 'Default Title')}</h2>
      {/* Component content */}
    </div>
  );
};

export default ComponentName;
```

## 🌍 Internationalization

### Adding Translations
1. Add English text to `src/locales/en.json`
2. Add Arabic translation to `src/locales/ar.json`
3. Use the translation hook in components:
   ```javascript
   const { t } = useTranslation();
   const text = t('section.key', 'Default English text');
   ```

### RTL Support Checklist
- [ ] Text direction set with `dir={isRTL ? 'rtl' : 'ltr'}`
- [ ] Layout adjustments for RTL in CSS
- [ ] Icons and images properly mirrored if needed
- [ ] Navigation and interactive elements work in RTL
- [ ] Text alignment appropriate for language direction

## 🧪 Testing

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run with coverage
npm run test:coverage
```

### Writing Tests
- Test component rendering
- Test user interactions
- Test translation functionality
- Test accessibility features
- Test responsive behavior

### Test Example
```javascript
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ComponentName from '../ComponentName';

describe('ComponentName', () => {
  test('renders correctly', () => {
    render(<ComponentName />);
    expect(screen.getByRole('heading')).toBeInTheDocument();
  });

  test('handles user interaction', async () => {
    const user = userEvent.setup();
    render(<ComponentName />);
    
    await user.click(screen.getByRole('button'));
    expect(screen.getByText('Expected result')).toBeInTheDocument();
  });
});
```

## 🎨 Styling Guidelines

### SCSS Structure
- Use CSS custom properties for theming
- Follow BEM methodology for class naming
- Use Bootstrap utilities when possible
- Create component-specific styles in modules

### Responsive Design
- Mobile-first approach
- Test on multiple screen sizes
- Use Bootstrap breakpoints
- Ensure touch-friendly interfaces

### Accessibility
- Use semantic HTML elements
- Provide proper ARIA labels
- Ensure keyboard navigation
- Test with screen readers
- Maintain color contrast ratios

## 📝 Pull Request Process

### Before Submitting
1. Run all tests: `npm test`
2. Check code formatting: `npm run format`
3. Run linting: `npm run lint`
4. Test in both English and Arabic
5. Test responsive design
6. Update documentation if needed

### PR Guidelines
- Use descriptive commit messages
- Reference related issues
- Include screenshots for UI changes
- Test on multiple browsers
- Ensure no console errors

### Commit Message Format
```
type(scope): description

feat(hero): add new animation to hero section
fix(faq): resolve RTL layout issue in Arabic
docs(readme): update installation instructions
test(components): add tests for Review component
```

## 🐛 Bug Reports

### Before Reporting
- Check existing issues
- Test in multiple browsers
- Test in both languages
- Provide reproduction steps

### Bug Report Template
```markdown
**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. See error

**Expected behavior**
What you expected to happen.

**Screenshots**
If applicable, add screenshots.

**Environment:**
- Browser: [e.g. Chrome, Safari]
- Language: [e.g. English, Arabic]
- Device: [e.g. Desktop, Mobile]
```

## 💡 Feature Requests

### Proposal Format
- Clear description of the feature
- Use cases and benefits
- Implementation considerations
- Impact on existing functionality
- Internationalization requirements

## 📞 Getting Help

- Create an issue for bugs or feature requests
- Join discussions in existing issues
- Check the README for setup instructions
- Review existing code for patterns and examples

## 📄 License

By contributing to this project, you agree that your contributions will be licensed under the same license as the project.

Thank you for contributing to FixMix! 🚀
