{"name": "fixmix-landing-page", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --check --ignore-path .gitignore .", "format:fix": "prettier --write --ignore-path .gitignore .", "type-check": "tsc --noEmit", "export": "next build && next export"}, "dependencies": {"bootstrap": "^5.2.3", "eslint": "8.37.0", "eslint-config-next": "^14.2.4", "jquery": "^3.6.4", "lorem-ipsum": "^2.0.8", "next": "^14.2.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-ga4": "^2.1.0", "react-icons": "^5.3.0", "sass": "^1.60.0"}, "devDependencies": {"@types/node": "18.15.11", "@types/react": "18.0.31", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint-config-prettier": "^8.8.0", "prettier": "^2.8.7", "tailwindcss": "^3.4.17", "typescript": "^5.6.3"}}