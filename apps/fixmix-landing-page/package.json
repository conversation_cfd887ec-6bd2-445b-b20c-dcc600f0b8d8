{"name": "fixmix-landing-page", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --check --ignore-path .gitignore .", "format:fix": "prettier --write --ignore-path .gitignore .", "type-check": "tsc --noEmit"}, "dependencies": {"bootstrap": "^5.2.3", "eslint": "8.37.0", "eslint-config-next": "^14.2.4", "jquery": "^3.6.4", "lorem-ipsum": "^2.0.8", "next": "^14.2.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-ga4": "^2.1.0", "react-icons": "^5.3.0", "sass": "^1.60.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "18.15.11", "@types/react": "18.0.31", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint-config-prettier": "^8.8.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^2.8.7", "tailwindcss": "^3.4.17", "typescript": "^5.6.3"}}