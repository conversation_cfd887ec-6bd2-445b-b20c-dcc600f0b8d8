{"buildFiles": ["C:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Fixmix mono repo\\FixMix\\apps\\fixmix\\android\\app\\.cxx\\Debug\\4h4m5s2t\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Fixmix mono repo\\FixMix\\apps\\fixmix\\android\\app\\.cxx\\Debug\\4h4m5s2t\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}