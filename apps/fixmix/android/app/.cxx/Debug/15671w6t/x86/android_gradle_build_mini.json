{"buildFiles": ["/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Fixmix mono repo/FixMix/apps/fixmix/android/app/.cxx/Debug/15671w6t/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Fixmix mono repo/FixMix/apps/fixmix/android/app/.cxx/Debug/15671w6t/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}