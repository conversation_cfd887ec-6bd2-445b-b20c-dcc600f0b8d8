# FixMix Admin Panel

The FixMix Admin Panel is a comprehensive React-based web application that provides administrators with powerful tools to manage the entire FixMix platform. It offers complete oversight of users, vendors, orders, services, and platform analytics.

## 🚀 Overview

The admin panel serves as the central command center for platform management:
- User and vendor management
- Service category and subcategory management
- Order monitoring and management
- Financial oversight and payouts
- Analytics and reporting
- Notification management
- Platform settings and configuration

## 🛠️ Technology Stack

- **Framework**: React.js
- **Styling**: Tailwind CSS
- **HTTP Client**: Axios
- **Routing**: React Router DOM
- **State Management**: React Context/Hooks
- **UI Components**: Custom components with Tailwind
- **Charts**: Chart.js/React Chart.js
- **Notifications**: SweetAlert2
- **Build Tool**: Create React App

## 📁 Project Structure

```
apps/Fixmix-Admin/
├── public/
│   ├── index.html
│   └── assets/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── Layout.js       # Main layout wrapper
│   │   ├── Login.js        # Authentication component
│   │   ├── UserDetail.js   # User detail view
│   │   ├── VendorDetails.js # Vendor detail view
│   │   └── ...
│   ├── pages/              # Main page components
│   │   ├── Dashboard.js    # Main dashboard
│   │   ├── Users.js        # User management
│   │   ├── Vendors.js      # Vendor management
│   │   ├── Orders.js       # Order management
│   │   ├── Category.js     # Category management
│   │   └── ...
│   ├── services/           # API service layer
│   │   └── api.js         # Axios configuration
│   ├── styles/            # CSS and styling
│   │   ├── global.css
│   │   └── index.css
│   ├── utils/             # Utility functions
│   ├── App.js             # Main app component
│   └── index.js           # App entry point
├── build/                 # Production build output
├── serve-app.js          # Production server
└── package.json          # Dependencies
```

## 🔧 Installation & Setup

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- Access to FixMix backend API

### 1. Install Dependencies
```bash
cd apps/Fixmix-Admin
npm install
```

### 2. Environment Configuration
Create a `.env` file in the root directory:

```env
REACT_APP_API_BASE_URL=https://api.fixmix.co
REACT_APP_ENVIRONMENT=development
```

### 3. Start Development Server
```bash
npm start
```

The application will start on `http://localhost:3000`.

### 4. Build for Production
```bash
npm run build
```

### 5. Serve Production Build
```bash
node serve-app.js
```

## 🔐 Authentication

### Admin Login
The admin panel requires authentication through the admin login system:

```javascript
// Login API call
const loginAdmin = async (email, password) => {
  try {
    const response = await api.post('/api/admins/login', {
      email,
      password
    });

    localStorage.setItem('accessToken', response.data.accessToken);
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

### Protected Routes
All admin routes are protected and require valid authentication:

```javascript
const PrivateRoute = ({ children }) => {
  const token = localStorage.getItem('accessToken');
  return token ? children : <Navigate to="/login" />;
};
```

## 📊 Dashboard Features

### Main Dashboard
- **Platform Statistics**: Total users, vendors, orders, and revenue
- **Recent Activity**: Latest orders and user registrations
- **Performance Metrics**: Platform growth and engagement metrics
- **Quick Actions**: Common administrative tasks

### Analytics Overview
```javascript
const DashboardStats = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalVendors: 0,
    totalOrders: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const response = await api.get('/api/admins/dashboard-stats');
      setStats(response.data);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  return (
    <div className="grid grid-cols-4 gap-6">
      <StatCard title="Total Users" value={stats.totalUsers} />
      <StatCard title="Total Vendors" value={stats.totalVendors} />
      <StatCard title="Total Orders" value={stats.totalOrders} />
      <StatCard title="Total Revenue" value={stats.totalRevenue} />
    </div>
  );
};
```

## 👥 User Management

### User Overview
- **User List**: Paginated list of all registered users
- **User Details**: Comprehensive user profile information
- **User Status**: Activate/deactivate user accounts
- **User Analytics**: User engagement and activity metrics

### User Management Features
```javascript
const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);

  const updateUserStatus = async (userId, status) => {
    try {
      await api.put(`/api/admins/users/${userId}/status`, { status });
      fetchUsers(); // Refresh user list
      Swal.fire('Success', 'User status updated', 'success');
    } catch (error) {
      Swal.fire('Error', 'Failed to update user status', 'error');
    }
  };

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await api.get('/api/admins/users');
      setUsers(response.data);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="user-management">
      <UserTable
        users={users}
        onStatusUpdate={updateUserStatus}
        loading={loading}
      />
    </div>
  );
};
```

## 🔧 Vendor Management

### Vendor Oversight
- **Vendor Applications**: Review and approve new vendor registrations
- **Vendor Profiles**: Manage vendor information and services
- **Vendor Verification**: Document and credential verification
- **Performance Monitoring**: Track vendor ratings and completion rates

### Vendor Approval Process
```javascript
const VendorApproval = () => {
  const approveVendor = async (vendorId) => {
    try {
      await api.put(`/api/admins/vendors/${vendorId}/approve`);
      Swal.fire('Success', 'Vendor approved successfully', 'success');
      fetchPendingVendors();
    } catch (error) {
      Swal.fire('Error', 'Failed to approve vendor', 'error');
    }
  };

  const rejectVendor = async (vendorId, reason) => {
    try {
      await api.put(`/api/admins/vendors/${vendorId}/reject`, { reason });
      Swal.fire('Success', 'Vendor application rejected', 'success');
      fetchPendingVendors();
    } catch (error) {
      Swal.fire('Error', 'Failed to reject vendor', 'error');
    }
  };

  return (
    <VendorApprovalInterface
      onApprove={approveVendor}
      onReject={rejectVendor}
    />
  );
};
```

## 📦 Order Management

### Order Oversight
- **Order Tracking**: Monitor all platform orders in real-time
- **Order Details**: Comprehensive order information and history
- **Dispute Resolution**: Handle customer-vendor disputes
- **Refund Management**: Process refunds and cancellations

### Order Management Interface
```javascript
const OrderManagement = () => {
  const [orders, setOrders] = useState([]);
  const [filters, setFilters] = useState({
    status: 'all',
    dateRange: 'all'
  });

  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      await api.put(`/api/admins/orders/${orderId}/status`, {
        status: newStatus
      });
      fetchOrders();
      Swal.fire('Success', 'Order status updated', 'success');
    } catch (error) {
      Swal.fire('Error', 'Failed to update order', 'error');
    }
  };

  const processRefund = async (orderId, amount, reason) => {
    try {
      await api.post(`/api/admins/orders/${orderId}/refund`, {
        amount,
        reason
      });
      Swal.fire('Success', 'Refund processed successfully', 'success');
    } catch (error) {
      Swal.fire('Error', 'Failed to process refund', 'error');
    }
  };

  return (
    <div className="order-management">
      <OrderFilters filters={filters} onFilterChange={setFilters} />
      <OrderTable
        orders={orders}
        onStatusUpdate={updateOrderStatus}
        onRefund={processRefund}
      />
    </div>
  );
};
```

## 🏷️ Category Management

### Service Categories
- **Category Creation**: Add new service categories
- **Category Editing**: Update existing categories
- **Subcategory Management**: Manage service subcategories
- **Category Analytics**: Track category popularity and usage

### Category Management System
```javascript
const CategoryManagement = () => {
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);

  const createCategory = async (categoryData) => {
    try {
      await api.post('/api/admins/categories', categoryData);
      fetchCategories();
      Swal.fire('Success', 'Category created successfully', 'success');
    } catch (error) {
      Swal.fire('Error', 'Failed to create category', 'error');
    }
  };

  const updateCategory = async (categoryId, updateData) => {
    try {
      await api.put(`/api/admins/categories/${categoryId}`, updateData);
      fetchCategories();
      Swal.fire('Success', 'Category updated successfully', 'success');
    } catch (error) {
      Swal.fire('Error', 'Failed to update category', 'error');
    }
  };

  const deleteCategory = async (categoryId) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: 'This action cannot be undone',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!'
    });

    if (result.isConfirmed) {
      try {
        await api.delete(`/api/admins/categories/${categoryId}`);
        fetchCategories();
        Swal.fire('Deleted!', 'Category has been deleted', 'success');
      } catch (error) {
        Swal.fire('Error', 'Failed to delete category', 'error');
      }
    }
  };

  return (
    <div className="category-management">
      <CategoryForm onSubmit={createCategory} />
      <CategoryList
        categories={categories}
        onEdit={updateCategory}
        onDelete={deleteCategory}
        onSelect={setSelectedCategory}
      />
      {selectedCategory && (
        <SubcategoryManager categoryId={selectedCategory.id} />
      )}
    </div>
  );
};
```

## 💰 Financial Management

### Revenue Tracking
- **Revenue Analytics**: Platform revenue and commission tracking
- **Vendor Payouts**: Manage vendor payment processing
- **Transaction Monitoring**: Monitor all platform transactions
- **Financial Reports**: Generate comprehensive financial reports

### Payout Management
```javascript
const PayoutManagement = () => {
  const [payoutRequests, setPayoutRequests] = useState([]);

  const approvePayout = async (payoutId) => {
    try {
      await api.put(`/api/admins/payouts/${payoutId}/approve`);
      fetchPayoutRequests();
      Swal.fire('Success', 'Payout approved', 'success');
    } catch (error) {
      Swal.fire('Error', 'Failed to approve payout', 'error');
    }
  };

  const rejectPayout = async (payoutId, reason) => {
    try {
      await api.put(`/api/admins/payouts/${payoutId}/reject`, { reason });
      fetchPayoutRequests();
      Swal.fire('Success', 'Payout rejected', 'success');
    } catch (error) {
      Swal.fire('Error', 'Failed to reject payout', 'error');
    }
  };

  return (
    <PayoutInterface
      requests={payoutRequests}
      onApprove={approvePayout}
      onReject={rejectPayout}
    />
  );
};
```

## 📢 Notification Management

### Platform Notifications
- **User Notifications**: Send notifications to users
- **Vendor Notifications**: Send notifications to vendors
- **Bulk Notifications**: Send notifications to multiple recipients
- **Notification Templates**: Manage notification templates

### Notification System
```javascript
const NotificationManager = () => {
  const sendNotification = async (notificationData) => {
    try {
      await api.post('/api/admins/notifications/send', notificationData);
      Swal.fire('Success', 'Notification sent successfully', 'success');
    } catch (error) {
      Swal.fire('Error', 'Failed to send notification', 'error');
    }
  };

  const sendBulkNotification = async (recipients, message) => {
    try {
      await api.post('/api/admins/notifications/bulk', {
        recipients,
        message
      });
      Swal.fire('Success', 'Bulk notification sent', 'success');
    } catch (error) {
      Swal.fire('Error', 'Failed to send bulk notification', 'error');
    }
  };

  return (
    <div className="notification-manager">
      <NotificationForm onSend={sendNotification} />
      <BulkNotificationForm onSend={sendBulkNotification} />
    </div>
  );
};
```

## 📊 Analytics & Reporting

### Platform Analytics
- **User Growth**: Track user registration and engagement
- **Revenue Analytics**: Monitor platform revenue and trends
- **Service Performance**: Analyze popular services and categories
- **Vendor Performance**: Track vendor success metrics

### Analytics Dashboard
```javascript
const AnalyticsDashboard = () => {
  const [analyticsData, setAnalyticsData] = useState({});

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      const response = await api.get('/api/admins/analytics');
      setAnalyticsData(response.data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
    }
  };

  return (
    <div className="analytics-dashboard">
      <div className="grid grid-cols-2 gap-6">
        <RevenueChart data={analyticsData.revenue} />
        <UserGrowthChart data={analyticsData.userGrowth} />
        <ServicePopularityChart data={analyticsData.services} />
        <VendorPerformanceChart data={analyticsData.vendors} />
      </div>
    </div>
  );
};
```

## 🎨 UI Components

### Reusable Components
- **DataTable**: Sortable and filterable data tables
- **StatCard**: Dashboard statistics display
- **Modal**: Custom modal dialogs
- **FormComponents**: Standardized form inputs
- **Charts**: Analytics visualization components

### Component Example
```javascript
const DataTable = ({
  data,
  columns,
  onEdit,
  onDelete,
  loading = false
}) => {
  const [sortConfig, setSortConfig] = useState(null);
  const [filterText, setFilterText] = useState('');

  const sortedData = useMemo(() => {
    if (!sortConfig) return data;

    return [...data].sort((a, b) => {
      if (a[sortConfig.key] < b[sortConfig.key]) {
        return sortConfig.direction === 'ascending' ? -1 : 1;
      }
      if (a[sortConfig.key] > b[sortConfig.key]) {
        return sortConfig.direction === 'ascending' ? 1 : -1;
      }
      return 0;
    });
  }, [data, sortConfig]);

  const filteredData = useMemo(() => {
    if (!filterText) return sortedData;

    return sortedData.filter(item =>
      Object.values(item).some(value =>
        value.toString().toLowerCase().includes(filterText.toLowerCase())
      )
    );
  }, [sortedData, filterText]);

  return (
    <div className="data-table">
      <div className="table-controls mb-4">
        <input
          type="text"
          placeholder="Search..."
          value={filterText}
          onChange={(e) => setFilterText(e.target.value)}
          className="px-4 py-2 border rounded-lg"
        />
      </div>

      <table className="w-full border-collapse border">
        <thead>
          <tr className="bg-gray-100">
            {columns.map(column => (
              <th
                key={column.key}
                className="border p-3 text-left cursor-pointer"
                onClick={() => handleSort(column.key)}
              >
                {column.label}
                {sortConfig?.key === column.key && (
                  <span className="ml-2">
                    {sortConfig.direction === 'ascending' ? '↑' : '↓'}
                  </span>
                )}
              </th>
            ))}
            <th className="border p-3">Actions</th>
          </tr>
        </thead>
        <tbody>
          {loading ? (
            <tr>
              <td colSpan={columns.length + 1} className="text-center p-8">
                Loading...
              </td>
            </tr>
          ) : (
            filteredData.map(item => (
              <tr key={item.id} className="hover:bg-gray-50">
                {columns.map(column => (
                  <td key={column.key} className="border p-3">
                    {column.render ? column.render(item) : item[column.key]}
                  </td>
                ))}
                <td className="border p-3">
                  <button
                    onClick={() => onEdit(item)}
                    className="mr-2 px-3 py-1 bg-blue-500 text-white rounded"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => onDelete(item.id)}
                    className="px-3 py-1 bg-red-500 text-white rounded"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};
```

## 🔧 API Integration

### API Service Configuration
```javascript
// services/api.js
import axios from "axios";

export const BASE_URL = "https://api.fixmix.co";

const axiosApi = axios.create({
  baseURL: BASE_URL,
});

export const api = axiosApi;

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("accessToken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem("accessToken");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);
```

### API Service Functions
```javascript
// Admin API services
export const adminServices = {
  // Dashboard
  getDashboardStats: () => api.get('/api/admins/dashboard-stats'),

  // User management
  getAllUsers: () => api.get('/api/admins/users'),
  getUserById: (id) => api.get(`/api/admins/users/${id}`),
  updateUserStatus: (id, status) => api.put(`/api/admins/users/${id}/status`, { status }),

  // Vendor management
  getAllVendors: () => api.get('/api/admins/vendors'),
  getVendorById: (id) => api.get(`/api/admins/vendors/${id}`),
  approveVendor: (id) => api.put(`/api/admins/vendors/${id}/approve`),
  rejectVendor: (id, reason) => api.put(`/api/admins/vendors/${id}/reject`, { reason }),

  // Order management
  getAllOrders: () => api.get('/api/admins/orders'),
  getOrderById: (id) => api.get(`/api/admins/orders/${id}`),
  updateOrderStatus: (id, status) => api.put(`/api/admins/orders/${id}/status`, { status }),

  // Category management
  getCategories: () => api.get('/api/admins/categories'),
  createCategory: (data) => api.post('/api/admins/categories', data),
  updateCategory: (id, data) => api.put(`/api/admins/categories/${id}`, data),
  deleteCategory: (id) => api.delete(`/api/admins/categories/${id}`),

  // Notifications
  sendNotification: (data) => api.post('/api/admins/notifications/send', data),
  sendBulkNotification: (data) => api.post('/api/admins/notifications/bulk', data),
};
```

## 🚀 Deployment

### Production Build
```bash
# Create production build
npm run build

# The build folder contains the production-ready files
```

### Deployment Options

#### 1. Static Hosting (Netlify, Vercel)
```bash
# Build and deploy to Netlify
npm run build
netlify deploy --prod --dir=build
```

#### 2. Server Deployment
```bash
# Use the included server
node serve-app.js
```

#### 3. Docker Deployment
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the app
RUN npm run build

# Expose port
EXPOSE 4000

# Start the server
CMD ["node", "serve-app.js"]
```

### Environment Variables for Production
```env
REACT_APP_API_BASE_URL=https://api.fixmix.co
REACT_APP_ENVIRONMENT=production
```

## 🧪 Testing

### Unit Testing
```bash
# Run tests
npm test

# Run tests with coverage
npm test -- --coverage
```

### Component Testing
```javascript
import { render, screen, fireEvent } from '@testing-library/react';
import UserManagement from '../pages/Users';

test('renders user management page', () => {
  render(<UserManagement />);
  expect(screen.getByText('User Management')).toBeInTheDocument();
});

test('updates user status', async () => {
  render(<UserManagement />);
  const statusButton = screen.getByText('Activate');
  fireEvent.click(statusButton);

  // Assert status update
});
```

## 🔒 Security

### Authentication Security
- **JWT Token Management**: Secure token storage and refresh
- **Route Protection**: All admin routes require authentication
- **Role-based Access**: Admin-only access control
- **Session Management**: Automatic logout on token expiry

### Data Security
- **Input Validation**: Client-side and server-side validation
- **XSS Protection**: Sanitized user inputs
- **CSRF Protection**: Cross-site request forgery prevention
- **Secure Communication**: HTTPS-only API communication

## 🐛 Troubleshooting

### Common Issues
1. **Login Issues**: Check API connectivity and credentials
2. **Data Not Loading**: Verify API endpoints and authentication
3. **Build Errors**: Run `npm install` and check dependencies
4. **Styling Issues**: Ensure Tailwind CSS is properly configured

### Debug Mode
```javascript
// Enable debug logging
if (process.env.NODE_ENV === 'development') {
  console.log('Debug mode enabled');
  // Add debug logging
}
```

## 📈 Performance Optimization

### Optimization Strategies
- **Code Splitting**: Lazy load components and routes
- **Memoization**: Use React.memo for expensive components
- **Virtual Scrolling**: For large data tables
- **Image Optimization**: Compress and lazy load images
- **Bundle Analysis**: Analyze and optimize bundle size

### Performance Monitoring
```javascript
// Performance monitoring
const performanceObserver = new PerformanceObserver((list) => {
  list.getEntries().forEach((entry) => {
    console.log('Performance entry:', entry);
  });
});

performanceObserver.observe({ entryTypes: ['navigation', 'resource'] });
```

## 🤝 Contributing

### Development Guidelines
1. Follow React best practices and hooks patterns
2. Use Tailwind CSS for styling consistency
3. Implement proper error handling and loading states
4. Add comprehensive tests for new features
5. Update documentation for new functionality

### Code Style
- Use functional components with hooks
- Implement proper TypeScript types (if migrating)
- Follow ESLint and Prettier configurations
- Use meaningful component and variable names

## 📞 Support

### Admin Panel Support
- **Technical Issues**: Check browser console for errors
- **API Issues**: Verify backend connectivity and authentication
- **UI Issues**: Check responsive design and browser compatibility
- **Performance Issues**: Monitor network requests and component rendering

## 🔗 Related Documentation

- [Main Project README](../../README.md)
- [Backend API Documentation](../fixmix_backend/README.md)
- [User Mobile App Documentation](../fixmix/README.md)
- [Vendor Mobile App Documentation](../fixmix_vendor/README.md)
```
```