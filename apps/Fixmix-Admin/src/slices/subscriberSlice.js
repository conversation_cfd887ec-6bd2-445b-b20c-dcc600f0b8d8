// slices/subscriberSlice.js
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { fetchSubscribersByCategory } from "../services/api";

// Thunk to fetch subscribers data
export const fetchSubscribers = createAsyncThunk(
  "subscribers/fetchSubscribers",
  async (timeline) => {
    const response = await fetchSubscribersByCategory(timeline);
    return response;
  }
);

const subscriberSlice = createSlice({
  name: "subscribers",
  initialState: {
    data: [],
    status: "idle",
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchSubscribers.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchSubscribers.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(fetchSubscribers.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });
  },
});

export default subscriberSlice.reducer;
