import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { activateUser, createUser, deActivateUser, deleteUser, getAllUser, getUserById } from "../services/api";

// Thunk to fetch users with filtering and pagination parameters
export const fetchUsers = createAsyncThunk(
    "users/fetchAll",
    async (params, { rejectWithValue }) => {
        try {
            const data = await getAllUser(params); // API Call
            return data;
        } catch (error) {
            return rejectWithValue(error.response?.data || "Failed to fetch users");
        }
    }
);

export const fetchUserById = createAsyncThunk(
    "users/fetchById",
    async (id, { rejectWithValue }) => {
        try {
            const response = await getUserById(id);
            return response;
        } catch (error) {
            return rejectWithValue(error.response?.data || "Failed to fetch user");
        }
    }
);

// Thunk to create a new user
export const createUserThunk = createAsyncThunk(
    "users/createUser",
    async ({ name, email, phone, gender, countryCode }, { rejectWithValue }) => {
        try {
            const response = await createUser(name, email, phone, gender, countryCode);
            return response;
        } catch (error) {
            return rejectWithValue(error.response?.data || "Failed to create user");
        }
    }
);

// Thunk to delete a user
export const deleteUserById = createAsyncThunk(
    "users/deleteUser",
    async (id) => {
        await deleteUser(id);
        return id;
    }
);

// Toggle User Activation Status
export const toggleUserStatus = createAsyncThunk(
    "users/toggleStatus",
    async ({ id, isActive }, { rejectWithValue }) => {
        try {
            if (isActive) {
                await deActivateUser(id);
            } else {
                await activateUser(id);
            }
            return { id, isActive: !isActive };
        } catch (error) {
            return rejectWithValue(error.response?.data || "Failed to update status");
        }
    }
);

const initialState = {
    data: [],
    loading: false,
    error: null,
    selectedUser: null,
    totalUsers: 0,
    totalPages: 1,
    currentPage: 1,
    limit: 10,
    sortBy: "createdAt",
    order: "desc",
    searchText: "", // NEW: Added searchText
};

const usersSlice = createSlice({
    name: "users",
    initialState,
    reducers: {
        setSearchText: (state, action) => {
            state.searchText = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            // Fetch Users
            .addCase(fetchUsers.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchUsers.fulfilled, (state, action) => {
                state.loading = false;
                state.data = action.payload.body.users;
                state.totalUsers = action.payload.totalUsers;
                state.totalPages = action.payload.totalPages;
                state.currentPage = action.payload.currentPage;
                state.searchText = action.meta?.arg?.search || "";
            })
            .addCase(fetchUsers.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'Failed to fetch users';
            })

            // Fetch User By ID
            .addCase(fetchUserById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchUserById.fulfilled, (state, action) => {
                state.loading = false;
                state.selectedUser = action.payload;
            })
            .addCase(fetchUserById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || "Failed to fetch user";
            })

            // Create User
            .addCase(createUserThunk.pending, (state) => {
                state.loading = true;
            })
            .addCase(createUserThunk.fulfilled, (state, action) => {
                state.loading = false;
                state.data.push(action.payload); // Add new user to state
            })
            .addCase(createUserThunk.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            // Delete User
            .addCase(deleteUserById.pending, (state) => {
                state.loading = true;
            })
            .addCase(deleteUserById.fulfilled, (state, action) => {
                state.loading = false;
                state.data = state.data.filter(user => user.id !== action.payload);
                state.totalUsers -= 1;
            })
            .addCase(deleteUserById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
            })

            // Toggle User Activation Status
            .addCase(toggleUserStatus.pending, (state) => {
                state.loading = true;
            })
            .addCase(toggleUserStatus.fulfilled, (state, action) => {
                state.loading = false;
                const index = state.data.findIndex(user => user._id === action.payload.id);
                if (index !== -1) {
                    state.data[index].isActive = action.payload.isActive;
                }
            })
            .addCase(toggleUserStatus.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            });
    },
});

export const { setSearchText } = usersSlice.actions;
export default usersSlice.reducer;
