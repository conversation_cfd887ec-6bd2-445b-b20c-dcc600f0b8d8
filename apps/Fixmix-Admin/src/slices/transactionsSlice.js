import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { getAllTransactions } from "../services/api";

// Async thunk to fetch transactions
export const fetchTransactions = createAsyncThunk(
    "transactions/fetchTransactions",
    async (params, { rejectWithValue }) => {
        try {
            const response = getAllTransactions();
            return response;
        } catch (error) {
            return rejectWithValue(error.response?.data || "Something went wrong");
        }
    }
);

const transactionsSlice = createSlice({
    name: "transactions",
    initialState: {
        data: [],
        status: "idle", // "idle" | "loading" | "succeeded" | "failed"
        error: null
    },
    reducers: {}, // No synchronous reducers needed

    extraReducers: (builder) => {
        builder
            .addCase(fetchTransactions.pending, (state) => {
                state.status = "loading";
            })
            .addCase(fetchTransactions.fulfilled, (state, action) => {
                state.status = "succeeded";
                state.data = action.payload;
            })
            .addCase(fetchTransactions.rejected, (state, action) => {
                state.status = "failed";
                state.error = action.payload;
            });
    }
});

export default transactionsSlice.reducer;
