import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { getAllOrders, getOrderById } from "../services/api";

// Thunk to fetch vendors data
export const fetchOrders = createAsyncThunk(
  'order/fetchOrder',
  async (params, { rejectWithValue }) => {
    try {
      const data = await getAllOrders(params);
      return data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch queries');
    }
  }
);

// Thunk to fetch order details by ID
export const fetchOrderById = createAsyncThunk(
  "orders/fetchOrderById",
  async (id, { rejectWithValue }) => {
    try {
      const response = await getOrderById(id);
      return response; // Ensure correct API response structure
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch order");
    }
  }
);
const initialState = {
  orders: [],
  isLoading: false,
  error: null,
  totalOrders: 0,
  totalPages: 1,
  currentPage: 1,
  limit: 10,
  sortBy: "createdAt",
  order: "desc",
  searchText: "", // NEW: Added searchText
};

const orderSlice = createSlice({
  name: "orders",
  initialState,
  reducers: {
    setSearchText: (state, action) => {
      state.searchText = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchOrders.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.orders = action.payload.body?.orders || []; // Ensure it's an array
        state.totalOrders = action.payload.totalOrders || 0;
        state.totalPages = action.payload.totalPages || 1;
        state.currentPage = action.payload.currentPage || 1;
        state.searchText = action?.meta?.arg?.search || "";
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Fetch order by ID
      .addCase(fetchOrderById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchOrderById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedOrder = action.payload || null;
      })
      .addCase(fetchOrderById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || "Failed to fetch order";
      });
  },
});
export const { setSearchText } = orderSlice.actions;

export default orderSlice.reducer;
