import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import {
  getUserNotification,
  getVendorNotification,
  sendNotificationToUser,
  sendNotificationToVendor,
} from "./../services/api";

// Async actions to fetch notifications
export const fetchUserNotifications = createAsyncThunk(
  "notifications/fetchUserNotifications",
  async (_, { rejectWithValue }) => {
    try {
      const response = await getUserNotification();
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch user notifications");
    }
  }
);

export const fetchVendorNotifications = createAsyncThunk(
  "notifications/fetchVendorNotifications",
  async (_, { rejectWithValue }) => {
    try {
      const response = await getVendorNotification();
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch vendor notifications");
    }
  }
);

// Async actions to send notifications
export const sendUserNotification = createAsyncThunk(
  "notifications/sendUserNotification",
  async ({ title, body }, { rejectWithValue }) => {
    try {
      const response = await sendNotificationToUser(title, body);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to send notification to user");
    }
  }
);

export const sendVendorNotification = createAsyncThunk(
  "notifications/sendVendorNotification",
  async ({ title, body }, { rejectWithValue }) => {
    try {
      const response = await sendNotificationToVendor(title, body);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to send notification to vendor");
    }
  }
);

// Slice
const notificationsSlice = createSlice({
  name: "notifications",
  initialState: {
    users: [],
    vendors: [],
    status: "idle",
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserNotifications.fulfilled, (state, action) => {
        state.users = action.payload.notifications; // Extract the notifications array
        state.status = "succeeded";
      })
      .addCase(fetchVendorNotifications.fulfilled, (state, action) => {
        state.vendors = action.payload.notifications; // Extract the notifications array
        state.status = "succeeded";
      })
      .addCase(sendUserNotification.fulfilled, (state, action) => {
        state.users.unshift(action.payload.notification); // Add new notification to the top
        state.status = "succeeded";
      })
      .addCase(sendVendorNotification.fulfilled, (state, action) => {
        state.vendors.unshift(action.payload.notification); // Add new notification to the top
        state.status = "succeeded";
      })
      .addMatcher(
        (action) => action.type.endsWith("/pending"),
        (state) => {
          state.status = "loading";
        }
      )
      .addMatcher(
        (action) => action.type.endsWith("/rejected"),
        (state, action) => {
          state.status = "failed";
          state.error = action.error.message;
        }
      );
  },
});
export default notificationsSlice.reducer;
