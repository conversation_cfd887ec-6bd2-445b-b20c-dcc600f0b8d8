import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { getDashboard, getDashboardDataCard } from "../services/api";

// Initial state
const initialState = {
    data: {},
    loading: false,
    error: null,
};

// Async thunk for fetching general dashboard data
export const fetchDashboardData = createAsyncThunk(
    "dashboard/fetchData",
    async (_, { rejectWithValue }) => {
        try {
            const response = await getDashboard();
            return response; // Assuming response.data is already structured properly
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || "Failed to fetch dashboard data");
        }
    }
);

// Async thunk for fetching dashboard card data
export const fetchDashboardCardData = createAsyncThunk(
    "dashboard/fetchDataCard", // Unique action type
    async (params = {}, { rejectWithValue }) => {
        try {
            const response = await getDashboardDataCard(params); // Call API with params
            return response;
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || "Failed to fetch dashboard card data");
        }
    }
);

const dashboardSlice = createSlice({
    name: "dashboard",
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            // General Dashboard Data
            .addCase(fetchDashboardData.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchDashboardData.fulfilled, (state, action) => {
                state.loading = false;
                state.data = action.payload;
            })
            .addCase(fetchDashboardData.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            // Dashboard Card Data
            .addCase(fetchDashboardCardData.pending, (state, action) => {
                state.loading = true;
                state.error = null;
                // Clear previous data for this tab while loading new data
                state.data[action.meta.arg.period] = {};
            })
            .addCase(fetchDashboardCardData.fulfilled, (state, action) => {
                state.loading = false;

                // 🔥 Store data per tab to avoid re-fetching
                state.data[action.meta.arg.period] = action.payload;
            })
            .addCase(fetchDashboardCardData.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
    },
});

export default dashboardSlice.reducer;
