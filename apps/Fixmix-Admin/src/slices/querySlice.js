import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { getAllQueries, getQueryById } from '../services/api';

// Async thunk for fetching queries with search params
export const fetchQueries = createAsyncThunk(
    'query/fetchQueries',
    async (params, { rejectWithValue }) => {
        try {
            const data = await getAllQueries(params);
            return data;
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Failed to fetch queries');
        }
    }
);

// Async thunk for fetching single query by ID
export const fetchQueryById = createAsyncThunk(
    "query/fetchById",
    async (id, { rejectWithValue }) => {
        try {
            const response = await getQueryById(id);
            return response;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

const initialState = {
    query: [],
    isLoading: false,
    error: null,
    totalQueries: 0,
    totalPages: 1,
    currentPage: 1,
    limit: 10,
    sortBy: "createdAt",
    order: "desc",
    searchText: "", // NEW: Added searchText
};

const querySlice = createSlice({
    name: 'query',
    initialState,
    reducers: {
        setSearchText: (state, action) => {
            state.searchText = action.payload; // Update search text
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchQueries.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(fetchQueries.fulfilled, (state, action) => {
                state.isLoading = false;
                state.query = action.payload.body.queries;
                state.totalQueries = action.payload.totalQueries;
                state.totalPages = action.payload.totalPages;
                state.currentPage = action.payload.currentPage;
                state.searchText = action.meta.arg?.search || ""; // Store latest search text
            })
            .addCase(fetchQueries.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
            })
            // Fetch query By ID
            .addCase(fetchQueryById.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(fetchQueryById.fulfilled, (state, action) => {
                state.isLoading = false;
                state.selectedUser = action.payload;
            })
            .addCase(fetchQueryById.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload?.message || "Failed to fetch query";
            })
    },
});

export const { setSearchText } = querySlice.actions;
export default querySlice.reducer;
