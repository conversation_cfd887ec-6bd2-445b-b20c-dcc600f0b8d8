import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { loginAdmin, logoutAdmin } from "../services/api";

export const login = createAsyncThunk(
  "auth/login",
  async ({ email, password }, { rejectWithValue }) => {
    try {
      const response = await loginAdmin(email, password);
      console.log(response)
      await localStorage.setItem("email", email);
      await localStorage.setItem("profileImage", response.profileImageUrl);

      // await localStorage.setItem("password", password);
      // await localStorage.setItem("id", response.id);
      await localStorage.setItem("accessToken", response.accessToken);
      // await localStorage.setItem("refreshToken", response.refreshToken);

      return response;
    } catch (error) {
      console.error("Login error:", error);
      return rejectWithValue(error.response.data);
    }
  }
);

export const logout = createAsyncThunk("auth/logout", async () => {
  try {
    const response = await logoutAdmin();
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");

    return response;
  } catch (error) {
    console.error("Logout error:", error);
    throw error;
  }
});

const authSlice = createSlice({
  name: "auth",
  initialState: {
    user: null,
    isAuthenticated: !!localStorage.getItem("accessToken"),
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(logout.fulfilled, (state) => {
        state.user = null;
        state.isAuthenticated = false;
      });
  },
});

export default authSlice.reducer;