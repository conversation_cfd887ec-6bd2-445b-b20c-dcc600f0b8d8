import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { payout } from "../services/api";

// Thunk to fetch vendors data
export const fetchPayouts = createAsyncThunk(
  "payout/fetchPayouts",
  async () => {
    const response = await payout();
    return response;
  }
);

const payoutSlice = createSlice({
  name: "payouts",
  initialState: {
    data: [],
    status: "idle",
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchPayouts.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchPayouts.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(fetchPayouts.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });
  },
});

export default payoutSlice.reducer;
