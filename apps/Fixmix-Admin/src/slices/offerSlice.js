import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { getAllOffers, getOfferById } from '../services/api';

// Async thunk to fetch offers from API
export const fetchOffers = createAsyncThunk(
    'offer/fetchOffers',
    async (params = {}, { rejectWithValue }) => {
        try {
            const offers = await getAllOffers(params); // Pass params to the API call
            return offers; // Return the fetched offers
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Failed to fetch offers');
        }
    }
);

export const fetchOfferById = createAsyncThunk(
    "offer/fetchById",
    async (id, { rejectWithValue }) => {
        try {
            const response = await getOfferById(id);
            return response;
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Failed to fetch offer');
        }
    }
);

const initialState = {
    offer: [],
    isLoading: false,
    error: null,
    selectedOffer: null, // Fixed variable name
    totalOffers: 0,
    totalPages: 1,
    currentPage: 1,
    limit: 10,
    sortBy: "createdAt",
    order: "desc",
    searchText: "",
};

const offerSlice = createSlice({
    name: 'offer',
    initialState,
    reducers: {
        setSearchText: (state, action) => {
            state.searchText = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchOffers.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(fetchOffers.fulfilled, (state, action) => {
                state.isLoading = false;
                state.offer = action.payload.body?.offers || []; // Ensure it's an array
                state.totalOffers = action.payload.totalOffers || 0;
                state.totalPages = action.payload.totalPages || 1;
                state.currentPage = action.payload.currentPage || 1;
                state.searchText = action?.meta.arg?.search || "";
            })
            .addCase(fetchOffers.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
            })

            // Fetch offer By ID
            .addCase(fetchOfferById.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(fetchOfferById.fulfilled, (state, action) => {
                state.isLoading = false;
                state.selectedOffer = action.payload; // Fixed from `selectedUser`
            })
            .addCase(fetchOfferById.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload || "Failed to fetch offer"; // Fixed error message
            });
    },
});

// Export actions
export const { setSearchText } = offerSlice.actions;

// Export reducer
export default offerSlice.reducer;
