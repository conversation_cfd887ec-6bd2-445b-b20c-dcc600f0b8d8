import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { activateVendor, deActivateVendor, deleteVendors, getAllVendor, getVendorById } from "../services/api";

// Thunk to fetch vendors data
export const fetchVendors = createAsyncThunk(
  "vendors/fetchVendors",
  async ({ search = "", isActive, startDate, endDate, page = 1, limit = 10, sortBy = "createdAt", order = "desc" } = {}, { rejectWithValue }) => {
    try {
      const params = { search, isActive, startDate, endDate, page, limit, sortBy, order };
      const data = await getAllVendor(params);
      return data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch vendors");
    }
  }
);

export const fetchVendorById = createAsyncThunk(
  "vendors/fetchById",
  async (id, { rejectWithValue }) => {
    try {
      const response = await getVendorById(id);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch vendor");
    }
  }
);

// Thunk to delete a vendor by ID
export const deleteVendorById = createAsyncThunk(
  "vendors/deleteVendorById",
  async (id, { rejectWithValue }) => {
    try {
      await deleteVendors(id);
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to delete vendor");
    }
  }
);

// Toggle Vendor Activation Status
export const toggleVendorStatus = createAsyncThunk(
  "vendors/toggleStatus",
  async ({ id, isActive }, { rejectWithValue }) => {
    try {
      if (isActive) {
        await deActivateVendor(id);
      } else {
        await activateVendor(id);
      }
      return { id, isActive: !isActive };
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to update status");
    }
  }
);

const initialState = {
  data: [],
  loading: false,
  error: null,
  selectedVendor: null,
  totalVendors: 0,
  totalPages: 1,
  currentPage: 1,
  limit: 10,
  sortBy: "createdAt",
  order: "desc",
  searchText: "", // Added searchText
};

const vendorsSlice = createSlice({
  name: "vendors",
  initialState,
  reducers: {
    setSearchText: (state, action) => {
      state.searchText = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Vendors
      .addCase(fetchVendors.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVendors.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload.body.vendors;
        state.totalVendors = action.payload.totalVendors;
        state.totalPages = action.payload.totalPages;
        state.currentPage = action.payload.currentPage;
        state.searchText = action.meta?.arg?.search || ""; // ✅ FIXED: Prevents undefined error
      })
      .addCase(fetchVendors.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || "Failed to fetch vendors";
      })

      // Fetch Vendor By ID
      .addCase(fetchVendorById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVendorById.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedVendor = action.payload;
      })
      .addCase(fetchVendorById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || "Failed to fetch vendor";
      })

      // Delete Vendor
      .addCase(deleteVendorById.fulfilled, (state, action) => {
        state.data = state.data.filter((vendor) => vendor.id !== action.payload);
      })

      // Toggle Vendor Activation Status
      .addCase(toggleVendorStatus.pending, (state) => {
        state.loading = true;
      })
      .addCase(toggleVendorStatus.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.data.findIndex((vendor) => vendor._id === action.payload.id);
        if (index !== -1) {
          state.data[index].isActive = action.payload.isActive;
        }
      })
      .addCase(toggleVendorStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { setSearchText } = vendorsSlice.actions;
export default vendorsSlice.reducer;
