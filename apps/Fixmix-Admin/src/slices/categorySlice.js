import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { createCategory, createSubcategory, getAllCategory, getAllSubCategory } from "../services/api";

export const fetchCategories = createAsyncThunk(
    "categories/fetchAll",
    async (_, { rejectWithValue }) => {
        try {
            const categories = await getAllCategory();
            return categories;
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || "Failed to fetch categories");
        }
    }
);
export const fetchCategoryById = createAsyncThunk(
    "users/fetchById",
    async (id, { rejectWithValue }) => {
        try {
            const response = await getAllSubCategory(id);
            return response;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);
export const createNewCategory = createAsyncThunk(
    "categories/create",
    async ({ name, imageUrl }, { rejectWithValue }) => {
        try {
            const response = await createCategory(name, imageUrl);
            return response.data; // Assuming response.data contains the created category
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || "Failed to create category");
        }
    }
);

// Create new subcategory
export const createNewSubCategory = createAsyncThunk(
    "subcategories/create",
    async ({ name, categoryId, imageUrl = "" }, { rejectWithValue }) => {
        try {
            const response = await createSubcategory(name, categoryId, imageUrl);
            return response.data; // Assuming response.data contains the created subcategory
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || "Failed to create subcategory");
        }
    }
);

const categorySlice = createSlice({
    name: "categories",
    initialState: {
        data: [],
        loading: false,
        error: null
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchCategories.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchCategories.fulfilled, (state, action) => {
                state.loading = false;
                state.data = action.payload;
            })
            .addCase(fetchCategories.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            // Fetch query By ID
            .addCase(fetchCategoryById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchCategoryById.fulfilled, (state, action) => {
                state.loading = false;
                state.selectedUser = action.payload;
            })
            .addCase(fetchCategoryById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || "Failed to fetch user";
            })

            // Create category
            .addCase(createNewCategory.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(createNewCategory.fulfilled, (state, action) => {
                state.loading = false;
                state.data.push(action.payload); // Append the newly created category to the list
            })
            .addCase(createNewCategory.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            // Create new subcategory
            .addCase(createNewSubCategory.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(createNewSubCategory.fulfilled, (state, action) => {
                state.loading = false;
                // Find the category and append the new subcategory
                const categoryIndex = state.data.findIndex(category => category._id === action.payload.categoryId);
                if (categoryIndex !== -1) {
                    if (!state.data[categoryIndex].subcategories) {
                        state.data[categoryIndex].subcategories = [];
                    }
                    state.data[categoryIndex].subcategories.push(action.payload);
                }
            })
            .addCase(createNewSubCategory.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
    }
});

export default categorySlice.reducer;