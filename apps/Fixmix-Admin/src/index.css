@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

body {
  background-color: rgb(32, 34, 33);
  font-family: "Poppins", sans-serif;
  /* color: #ffffff; */
}

.user-input {
  @apply block w-full mt-2 p-3 bg-gray-800 rounded-md text-black;
}

.user-detail-container {
  @apply ml-4 mt-4;
}

.user-metrics {
  @apply flex flex-col md:flex-row gap-6 mt-6;
}

.user-input {
  @apply block w-full mt-2 p-3 bg-gray-800 rounded-md text-black;
}

.user-image {
  @apply w-64 h-64 rounded-full bg-cover bg-center;
}

.breadcrumb {
  @apply flex items-center text-light-gray text-sm mb-4 cursor-pointer;
}

.breadcrumb-separator {
  @apply border-t border-gray-700 mb-8;
}

.flex-1 {
  /* margin-left: 16rem; */
  /* Adjust as needed */
}

/* Responsive adjustments */
@media (max-width: 1200px) {

  .breadcrumb,
  .breadcrumb-separator,
  .user-image,
  .user-input {
    @apply w-full;
  }

  .user-image {
    @apply mx-auto;
  }

  .user-metrics {
    @apply flex-col gap-6 items-center;
  }
}

/* custom-scrollbar.css */

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  /* Width of the scrollbar */
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #1f2937;
  /* Track color (darker background) */
  border-radius: 10px;
  /* Rounded edges */
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #4a90e2;
  /* Scrollbar color */
  border-radius: 10px;
  /* Rounded edges */
  border: 2px solid #1f2937;
  /* Add padding-like border */
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #357abd;
  /* Darker blue on hover */
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #4a90e2 #1f2937;
  /* Thumb and track colors */
}