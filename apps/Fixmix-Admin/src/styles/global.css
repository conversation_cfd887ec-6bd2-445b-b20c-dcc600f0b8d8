/* Global CSS */

body {
  margin: 0;
  font-family: "Poppins", sans-serif;
  background-color: #faf9f9;
  color: #ffffff;
}

.custom-gradient-bg {
  background: linear-gradient(273.58deg, #2c2e2d 3.18%, #202221 97.33%);
}

.selected-option-bg {
  background: linear-gradient(93.63deg,
      rgba(5, 126, 182, 0.7) 20%,
      rgba(7, 173, 250, 0.9) 80%);
}

.card-bg {
  background: #2c2e2d;
  box-shadow: 2px 2px 8.3px -5px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
}

/* topbar  */
/* TopBar */
.topbar {
  position: absolute;
  width: 100%;
  height: 65px;
  left: 327px;
  top: 44px;
  background: #2c2e2d;
  backdrop-filter: blur(9.55px);
  border-radius: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

/* Search Field */
.topbar .search-field {
  display: flex;
  align-items: center;
}

.topbar .search-field .ant-input {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  font-family: "Manrope", sans-serif;
  font-size: 17px;
  line-height: 23px;
  width: 219px;
  height: 21px;
}

.topbar .search-field .ant-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.topbar .search-field .anticon {
  color: rgba(255, 255, 255, 0.5);
}

/* User Info */
.topbar .user-info {
  display: flex;
  align-items: center;
}

.topbar .user-info .name {
  text-align: right;
  font-family: "Poppins", sans-serif;
}

.topbar .user-info .name .full-name {
  font-weight: 500;
  font-size: 18px;
  line-height: 27px;
  color: #ffffff;
}

.topbar .user-info .name .role {
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;
  color: rgba(255, 255, 255, 0.29);
}

.topbar .user-info .profile-pic {
  width: 56px;
  height: 56px;
  border: 1px solid #34a853;
  border-radius: 50%;
  overflow: hidden;
  margin-left: 10px;
}

.topbar .user-info .profile-pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* topbar end  */

.table-header-bg {
  background: #373938;
  box-shadow: 2px 2px 8.3px -5px rgba(0, 0, 0, 0.25);
  border-radius: 5px 5px 0px 0px;
}

.table-row-bg {
  background: rgba(29, 31, 30, 0.5);
  box-shadow: 2px 2px 8.3px -5px rgba(0, 0, 0, 0.25);
}

.table-scrollbar-bg {
  background: #373938;
  border-radius: 60px;
}

/* global.css */
.bg-secondary {
  background-color: #faf9f9;
}

.bg-secondary-light {
  background-color: rgba(224, 226, 225, 0.5);
}

.bg-accent-dark {
  background-color: #373938;
}

.tab-button {
  padding: 12px 20px;
  border-radius: 5px;
  background-color: #faf9f9;
  color: rgba(0, 0, 0, 0.5);
  font-family: "Manrope", sans-serif;
  font-weight: 300;
  /* font-size: 12px; */
  text-align: center;
  cursor: pointer;
}

.tab-button-active {
  background-color: #0096c7;
  color: #ffffff;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(1);
}

/* global.css */
/* .bg-primary {
  background-color: rgb(32, 34, 33);
} */

.bg-secondary {
  background-color: #faf9f9;
}

.bg-secondary-light {
  background-color: rgba(168, 172, 170, 0.5);
}

.bg-accent-dark {
  background-color: #373938;
}

.text-white {
  color: #ffffff;
}

.text-light-gray {
  color: rgba(255, 255, 255, 0.5);
}

.text-yellow {
  color: #f6d724;
}

.rounded-lg {
  border-radius: 20px;
}

.shadow-lg {
  box-shadow: 2px 2px 8.3px -5px rgba(0, 0, 0, 0.25);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .ml-64 {
    margin-left: 0;
  }

  .mt-16 {
    margin-top: 8px;
  }

  .p-6 {
    padding: 2px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-cols-4 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .ml-64 {
    margin-left: 16px;
  }

  .mt-16 {
    margin-top: 8px;
  }

  .p-6 {
    padding: 4px;
  }
}