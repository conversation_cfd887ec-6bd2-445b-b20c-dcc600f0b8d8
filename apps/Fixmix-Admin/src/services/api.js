import axios from "axios";

export const BASE_URL = "https://api.fixmix.co";
// export const BASE_URL = "http://192.168.29.207:5000";
const axiosApi = axios.create({
  baseURL: BASE_URL,
});

export const api = axiosApi;

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("accessToken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// export const verifyToken = async()=>{
//   const refreshToken = localStorage.getItem('refreshToken');
//   const response = await api.post("/api/auth/refresh-token",{refreshToken});
//   return response.data;
// }
export const verifyToken = async () => {
  // const refreshToken = localStorage.getItem("refreshToken");
  try {
    const response = await api.post("/api/admins/refresh");
    return response.data;
  } catch (error) {
    throw new Error("Token verification failed");
  }
};

export const loginAdmin = async (email, password) => {
  const response = await api.post(
    "/api/admins/login",
    { email, password }, // Pass the email and password as the second argument
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data;
};
export const getDashboard = async () => {
  const response = await api.get(`/api/admins/dashbord/stakes`, {
    headers: { "Content-Type": "application/json" }
  });
  return response.data.body;
};
export const getAllUser = async (params = {}) => {
  const response = await api.get(`/api/admins/getAllUser`, {
    params,
    headers: { "Content-Type": "application/json" }
  });
  return response.data;
};
export const createUser = async (name, email, phone, gender, countryCode) => {
  const response = await api.post("/api/admins/create/user",
    { name, email, phone, gender, countryCode },
    { headers: { "Content-Type": "application/json" } });
  return response.data;
};
export const getUserById = async (id) => {
  const response = await api.get(
    `/api/admins/getUser/${id}`,
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data.body.user;
};
export const activateUser = async (id) => {
  const response = await api.put(
    `/api/admins/update-user-status/active/${id}`,
    { headers: { "Content-Type": "application/json" } }
  );
  return response;
}
export const deActivateUser = async (id) => {
  const response = await api.put(
    `/api/admins/update-user-status/deactive/${id}`,
    { headers: { "Content-Type": "application/json" } }
  )
  return response;
}
export const getAllVendor = async (params = {}) => {
  const response = await api.get("/api/admins/getAllVendor", {
    params,
    headers: { "Content-Type": "application/json" }
  } // Ensure headers are set correctly
  );
  return response.data;
};
export const getVendorById = async (id) => {
  const response = await api.get(
    `/api/admins/getVendor/${id}`,
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data.body.vendor;
};
export const activateVendor = async (id) => {
  const response = await api.put(
    `/api/admins/update-vendor-status/active/${id}`,
    { headers: { "Content-Type": "application/json" } }
  );
  return response;
}
export const deActivateVendor = async (id) => {
  const response = await api.put(
    `/api/admins/update-vendor-status/deactive/${id}`,
    { headers: { "Content-Type": "application/json" } }
  )
  return response;
}
export const getAllQueries = async (params = {}) => {
  const response = await api.get("/api/admins/getAllQueries", {
    params,
    headers: { "Content-Type": "application/json" }
  });
  return response.data;
};
export const getQueryById = async (id) => {
  const response = await api.get(
    `/api/admins/getQueries/${id}`,
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data.body.queries;
};
export const getAllOffers = async (params = {}) => {
  const response = await api.get("/api/admins/getAllOffers", {
    params,
    headers: { "Content-Type": "application/json" }
  });
  return response.data;
};
export const getOfferById = async (id) => {
  const response = await api.get(
    `/api/admins/getOffers/${id}`,
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data.body.offer;
};
export const getAllCategory = async () => {
  const response = await api.get(
    "/api/admins/getAllCategory",
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data.body.categories;
};
export const getAllSubCategory = async (id) => {
  const response = await api.get(
    `/api/admins/getAllSubCategory-categoryId/${id}`,
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data.body.subCategory;
};
export const updateImageCategory = async ({ imageUrl, id }) => {
  // const _id = localStorage.getItem("id");
  const response = await api.put(`/api/admins/update/image/category/${id}`, {
    imageUrl,
  });
  return response;
};
export const updateImageSubCategory = async ({ imageUrl, id }) => {
  // const _id = localStorage.getItem("id");
  const response = await api.put(`/api/admins/update/image/subcategory/${id}`, {
    imageUrl,
  });
  return response;
};
export const getAllOrders = async (params = {}) => {
  const response = await api.get("/api/admins/getAllOrders", {
    params,
    headers: { "Content-Type": "application/json" }
  });
  return response.data;
};
export const getOrderById = async (id) => {
  const response = await api.get(
    `/api/admins/getOrders/${id}`,
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data.body.order;
};
export const getUserNotification = async () => {
  const response = await api.get(
    "/api/admins/notification/user",
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data.body;
};
export const getVendorNotification = async () => {
  const response = await api.get(
    "/api/admins/notification/vendor",
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data.body;
};
export const sendNotificationToUser = async (title, body) => {
  const response = await api.post(
    "/api/admins/notification/user/create",
    { title, body },
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data;
};
export const sendNotificationToVendor = async (title, body) => {
  const response = await api.post(
    "/api/admins/notification/user/create",
    { title, body },
    { headers: { "Content-Type": "application/json" } } // Ensure headers are set correctly
  );
  return response.data;
};
export const logoutAdmin = async () => {
  localStorage.clear(); // Removes all items from local storage
};
export const uploadAdminImage = async (formdata) => {
  const response = await api.post("/api/image/upload", formdata, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};
export const updateImage = async (imageUrl) => {
  // const _id = localStorage.getItem("id");
  const response = await api.put("/api/admins/update/image", {
    imageUrl,
  });
  return response;
};
export const createCategory = async (name, imageUrl = "") => {
  const response = await api.post("/api/admins/createCategory", {
    name,
    imageUrl,
  });
  return response;
};
export const createSubcategory = async (name, categoryId, imageUrl = "") => {
  console.log(category);

  const response = await api.post("/api/admins/createSubCategory", {
    name,
    categoryId,
    imageUrl,
  });
  return response;
};
export const changepassword = async ({ oldpass, newpass }) => {
  const response = await api.put("/api/admins/change-password", {
    oldpass,
    newpass
  });
  return response;
};
export const getDashboardDataCard = async (params = {}) => {
  const response = await api.get(`/api/admins/admin-dashbord`, {
    params,
    headers: { "Content-Type": "application/json" }
  });
  return response.data.data;
};
export const getAllTransactions = async (params = {}) => {
  const response = await api.get("/api/admins/all-transaction", {
    params,
    headers: { "Content-Type": "application/json" }
  });
  return response.data;
};
export const forgotPassword = async (email) => {
  const response = await api.put("/api/admins/forgot-password", {
    email,
  });
  return response;
};







export const vendors = async () => {
  const response = await api.get("/api/vendors");
  return response.data;
};
export const deleteVendors = async (id) => {
  const response = await api.delete(`/api/vendors/${id}`);
  return response.data;
};
export const user = async () => {
  const response = await api.get("/api/users/");
  return response.data;
};
export const deleteUser = async (id) => {
  const response = await api.delete(`/api/users/${id}`);
  return response.data;
};
export const order = async () => {
  const response = await api.get("/api/orders/admin/");
  return response.data;
};
export const payout = async () => {
  const response = await api.get("/api/payouts/");
  return response.data;
};


export const createVendor = async (
  name,
  email,
  phoneNumber,
  businessName,
  category,
  password
) => {
  const response = await api.post("/api/auth/vendor/register", {
    name,
    email,
    phoneNumber,
    businessName,
    category,
    password,
  });
  return response;
};

// export const updateSubCategoryImage = async (imageUrl, id) => {
//   console.log(category);

//   const response = await api.put(`/api/subcategories/image/${id}`, {
//     imageUrl,
//     id,

//   });
//   return response;
// };
export const category = async () => {
  const response = await api.get("/api/categories/");
  return response.data;
};

export const updateUserStatus = async (userId, status) => {
  try {
    const response = await api.patch(`/api/users/${userId}/status`, {
      status,
    });
    console.log("Status updated successfully", response.data);
  } catch (error) {
    console.error("Error updating status", error);
  }
};

export const updateVendorStatus = async (vendorId, newStatus) => {
  try {
    const response = await api.put(`/api/vendors/${vendorId}/status`, {
      status: newStatus,
    });
    console.log("Vendor status updated:", response.data);
  } catch (error) {
    console.error("Error updating vendor status:", error);
  }
};
export const updatePayout = async (payoutId) => {
  try {
    const response = await api.put(`/api/payouts/${payoutId}/complete`);
    console.log("Vendor status updated:", response.data);
  } catch (error) {
    console.error("Error updating vendor status:", error);
  }
};
// Vendor Detail Stats
export const fetchVendorAnalytics = async (vendorId) => {
  try {
    const response = await api.get(
      `/api/analytics/vendor/${vendorId}/analytics`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching analytics:", error);
    throw error;
  }
};
export const sendNotification = async (title, body, view) => {
  console.log(category);

  const response = await api.post("/api/notifications/", {
    title,
    body,
    view,
  });
  return response;
};

// Function to fetch category vendor analytics
export const fetchCategoryVendorAnalytics = async (timeline) => {
  const response = await api.get(
    `/api/analytics/category/analytics?timeline=${timeline}`
  );
  return response.data;
};
export const updateCategoryName = async (cateogryId, categoryName) => {
  try {
    const response = await api.put(`/api/categories/${cateogryId}`, {
      name: categoryName,
    });
    console.log("Category Name updated:", response.data);
  } catch (error) {
    console.error("Error updating Category Name:", error);
  }
};
export const updateSubCategoryName = async (subcategoryId, categoryName) => {
  try {
    const response = await api.put(`/api/subcategories/${subcategoryId}`, {
      name: categoryName,
    });
    console.log("SubCategory Name updated:", response.data);
  } catch (error) {
    console.error("Error updating SubCategory Name:", error);
  }
};
export const fetchUserNotification = async (timeline) => {
  const response = await api.get(`/api/notifications/user-notifications`);
  return response.data;
};
export const fetchVendorNotification = async (timeline) => {
  const response = await api.get(`/api/notifications/vendor-notifications`);
  return response.data;
};
// Fetch subscribers by category - Dashboard
export const fetchSubscribersByCategory = async (timeline) => {
  const response = await api.get(
    `/api/subscriptions/category/analytics?timeline=${timeline}`
  );
  return response.data;
};
export const deleteCategory = async (id) => {
  const response = await api.delete(`/api/categories/${id}`);
  return response.data;
};
export const deleteSubCategory = async (id) => {
  const response = await api.delete(`/api/subcategories/${id}`);
  console.log(id);

  return response.data;
};
export default api;
