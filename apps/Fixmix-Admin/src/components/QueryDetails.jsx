import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import userIcon from "../assets/userIcon.webp";
import { fetchQueryById } from '../slices/querySlice';


const QueryDetails = () => {
    const { Id } = useParams();
    const dispatch = useDispatch();
    const { selectedUser: user, loading, error } = useSelector((state) => state.query);
    useEffect(() => {
        if (Id) {
            dispatch(fetchQueryById(Id)); // Fetch user when component mounts
        }
    }, [dispatch, Id]);
    if (loading) return <div className="text-white">Loading user details...</div>;
    if (error) return <div className="text-white">Error: {error}</div>;
    if (!user) return <div className="text-white">User not found</div>;
    const categoryImageUrl = user?.userId?.profilePhoto
        ? `${user?.userId?.profilePhoto}`
        : userIcon;

    return (
        <div className="user-detail-container mx-auto max-w-6xl pt-20">
            <div className="breadcrumb text-light-gray text-sm mb-4 flex items-center">
                <span
                    className="cursor-pointer mr-2"
                    onClick={() => window.history.back()}
                >
                    &larr;
                </span>
                <span className="ml-2 text-black">Dashboard / Query / QueryDetails</span>
            </div>
            <div className="breadcrumb-separator border-t border-gray-700 mb-8"></div>
            <div className="user-detail-grid grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="user-info space-y-6">
                    <div>
                        <label className="block text-black text-sm">Full Name</label>
                        <input
                            type="text"
                            value={user?.userId?.name || ""}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>
                    {/* <div>
            <label className="block text-light-gray text-sm">Address</label>
            <textarea
              // value={user.address}
              readOnly
              className="text-black border border-black/25"
            ></textarea>
          </div> */}
                    <div>
                        <label className="block text-light-gray text-sm">
                            Mobile
                        </label>
                        <input
                            type="text"
                            value={user?.userId?.phone || ""}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>
                    <div>
                        <label className="block text-light-gray text-sm">
                            Status
                        </label>
                        <input
                            type="text"
                            value={user.status}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>
                    <div>
                        <label className="block text-light-gray text-sm">
                            Category Name
                        </label>
                        <input
                            type="text"
                            value={user?.categoryId?.name || ""}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>
                    <div>
                        <label className="block text-light-gray text-sm">
                            Title
                        </label>
                        <input
                            type="text"
                            value={user.title}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>
                    <div>
                        <label className="block text-light-gray text-sm">
                            Description
                        </label>
                        <input
                            type="text"
                            value={user.description}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>

                </div>
                <div className="user-image-wrapper flex justify-center items-center">
                    <div
                        className="user-image w-64 h-64 rounded-full mx-auto bg-cover bg-center"
                        style={{ backgroundImage: `url(${categoryImageUrl})` }}
                    ></div>
                </div>
            </div>
            <div className="user-metrics mt-10 grid grid-cols-3 gap-6">
                {/* <UserMetrics title="Total Orders" count={user.totalOrders} />
        <UserMetrics title="Total wishlist" count={user.wishlist.length} />
        <UserMetrics title="Total Spending" count={"€ " + user.totalSpending} /> */}
            </div>
        </div>
    )
}

export default QueryDetails
