// import React from "react";
// import "./CreateVendorModal.css";

// const CreateVendorModal = ({ closeModal }) => {
//   return (
//     <div className="modal-overlay">
//       <div className="modal-container">
//         <div className="back-arrow" onClick={closeModal}>&#x2190;</div>
//         <h1>Create New Vendor!</h1>
//         <p>Start your FixMix's vendor journey today!</p>
//         <form>
//           <div className="form-row">
//             <div className="form-group">
//               <label htmlFor="full-name" className="label">Full Name</label>
//               <div className="input-container">
//                 <input type="text" id="full-name" placeholder="Full Name" />
//                 <span className="input-icon">&#128100;</span>
//               </div>
//             </div>
//             <div className="form-group">
//               <label htmlFor="email-id" className="label">Email ID</label>
//               <div className="input-container">
//                 <input type="email" id="email-id" placeholder="<EMAIL>" />
//                 <span className="input-icon">&#x2709;</span>
//               </div>
//             </div>
//           </div>
//           <div className="form-row">
//             <div className="form-group">
//               <label htmlFor="mobile-number" className="label">Mobile Number</label>
//               <div className="input-container">
//                 <input type="tel" id="mobile-number" placeholder="Enter Number" />
//                 <span className="input-icon">&#x260E;</span>
//               </div>
//             </div>
//             <div className="form-group">
//               <label htmlFor="business-name" className="label">Business Name</label>
//               <div className="input-container">
//                 <input type="text" id="business-name" placeholder="Name Here" />
//                 <span className="input-icon">&#128188;</span>
//               </div>
//             </div>
//           </div>
//           <div className="form-row">
//             <div className="form-group">
//               <label htmlFor="product-category" className="label">Product Category</label>
//               <div className="input-container ">
//                 <select id="product-category" className="bg-gray-300 text-black pb-2">

//                   <option value="66b721bfa5440a3cdee3ed84">Woman</option>
//                   <option value="66b721c0a5440a3cdee3edb4">Man</option>
//                   <option value="66b721c1a5440a3cdee3edd4">Kids</option>
//                   <option value="66b721c2a5440a3cdee3edee">Pharmacy's</option>
//                   <option value="66b721c2a5440a3cdee3ee00">Furniture</option>
//                   <option value="66b721c2a5440a3cdee3ee12">Luxury Authentic</option>
//                 </select>

//                 <span className="input-icon">&#9662;</span>
//               </div>
//             </div>
//             <div className="form-group">
//               <label htmlFor="password" className="label">Password</label>
//               <div className="input-container">
//                 <input type="password" id="password" placeholder="Enter Password" />
//                 <span className="input-icon">&#128274;</span>
//               </div>
//             </div>
//           </div>
//           <button type="submit" className="create-button-popup">Create</button>
//         </form>
//       </div>
//     </div>
//   );
// };

// export default CreateVendorModal;
import React, { useState } from "react";
import "./CreateVendorModal.css";
import { createVendor } from "../services/api";

const CreateVendorModal = ({ closeModal, updateVendorData }) => {
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [mobileNumber, setMobileNumber] = useState("");
  const [businessName, setBusinessName] = useState("");
  const [productCategory, setProductCategory] = useState("");
  const [password, setPassword] = useState("");

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const response = await createVendor(
        fullName,
        email,
        mobileNumber,
        businessName,
        productCategory,
        password
      );
      console.log(response);

      if (response.status == 201) {
        updateVendorData(); // Refresh vendor data
        closeModal(); // Close the modal
      } else {
        console.error("Failed to create vendor:", response.statusText);
      }
    } catch (error) {
      console.error("Error creating vendor:", error);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-container">
        <div className="back-arrow" onClick={closeModal}>
          &#x2190;
        </div>
        <h1>Create New Vendor!</h1>
        <p>Start your FixMix's vendor journey today!</p>
        <form onSubmit={handleSubmit}>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="full-name" className="label">
                Full Name
              </label>
              <div className="input-container">
                <input
                  type="text"
                  id="full-name"
                  placeholder="Full Name"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                />
                <span className="input-icon">&#128100;</span>
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="email-id" className="label">
                Email ID
              </label>
              <div className="input-container">
                <input
                  type="email"
                  id="email-id"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
                <span className="input-icon">&#x2709;</span>
              </div>
            </div>
          </div>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="mobile-number" className="label">
                Mobile Number
              </label>
              <div className="input-container">
                <input
                  type="tel"
                  id="mobile-number"
                  placeholder="Enter Number"
                  value={mobileNumber}
                  onChange={(e) => setMobileNumber(e.target.value)}
                />
                <span className="input-icon">&#x260E;</span>
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="business-name" className="label">
                Business Name
              </label>
              <div className="input-container">
                <input
                  type="text"
                  id="business-name"
                  placeholder="Name Here"
                  value={businessName}
                  onChange={(e) => setBusinessName(e.target.value)}
                />
                <span className="input-icon">&#128188;</span>
              </div>
            </div>
          </div>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="product-category" className="label">
                Product Category
              </label>
              <div className="input-container">
                <select
                  id="product-category"
                  className="bg-gray-300 text-black pb-2"
                  value={productCategory}
                  onChange={(e) => setProductCategory(e.target.value)}
                >
                  <option value="">Select</option>
                  <option value="66b721bfa5440a3cdee3ed84">Woman</option>
                  <option value="66b721c0a5440a3cdee3edb4">Man</option>
                  <option value="66b721c1a5440a3cdee3edd4">Kids</option>
                  <option value="66b721c2a5440a3cdee3edee">Pharmacy's</option>
                  <option value="66b721c2a5440a3cdee3ee00">Furniture</option>
                  <option value="66b721c2a5440a3cdee3ee12">
                    Luxury Authentic
                  </option>
                </select>
                <span className="input-icon">&#9662;</span>
              </div>
            </div>
            <div className="form-group">
              <label htmlFor="password" className="label">
                Password
              </label>
              <div className="input-container">
                <input
                  type="password"
                  id="password"
                  placeholder="Enter Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <span className="input-icon">&#128274;</span>
              </div>
            </div>
          </div>
          <button type="submit" className="create-button-popup">
            Create
          </button>
        </form>
      </div>
    </div>
  );
};

export default CreateVendorModal;
