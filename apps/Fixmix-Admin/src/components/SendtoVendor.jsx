import React, { useState, useEffect } from "react";
import axios from "axios";
import { vendors, user } from "./../services/api";
import "./notification.css";
import noDataImage from "./../assets/icons/nodataimage.svg";

const Notifications = () => {
  const [view, setView] = useState("userNotifications"); // Default selected as User Notifications
  const [users, setUsers] = useState([]);
  const [vendorsList, setVendorsList] = useState([]);
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const [title, setTitle] = useState("");
  const [body, setBody] = useState("");

  // Dummy data for users and vendors
  const dummyUserData = [
    { id: 1, name: "User One", text: "Hello, this is a user notification" },
    { id: 2, name: "User Two", text: "User notification example text" },
  ];

  const dummyVendorData = [
    { id: 1, name: "Vendor One", text: "Hello, this is a vendor notification" },
    { id: 2, name: "Vendor Two", text: "Vendor notification example text" },
  ];

  useEffect(() => {
    const fetchUsersAndVendors = async () => {
      try {
        const usersData = await user();
        const vendorsData = await vendors();
        setUsers(usersData);
        setVendorsList(vendorsData);
      } catch (error) {
        console.error("Error fetching users and vendors:", error);
      }
    };

    fetchUsersAndVendors();
  }, []);

  const togglePopup = () => {
    setIsPopupVisible(!isPopupVisible);
  };

  const handleSendNotification = async () => {
    if (!title.trim() || !body.trim()) {
      alert("Title and body cannot be empty");
      return;
    }

    try {
      const response = await axios.post(
        "http://192.168.29.138:3501/api/notifications/",
        {
          title,
          body,
        }
      );

      if (response.status === 200) {
        alert("Notification sent successfully");
        setIsPopupVisible(false);
        setTitle(""); // Clear title after sending
        setBody(""); // Clear body after sending
      } else {
        alert("Failed to send notification");
      }
    } catch (error) {
      console.error("Error sending notification:", error);
      alert("Failed to send notification");
    }
  };

  return (
    <div className="notifications-container flex flex-col">
      <div className="buttons-container">
        <button
          className={`btn1 ${
            view === "userNotifications" ? "active-btn" : "de-btn"
          }`}
          onClick={() => setView("userNotifications")}
        >
          Send to Users
        </button>
        <button
          className={`btn ${
            view === "sendToVendor" ? "active-btn" : "de-btn"
          } ml-4`}
          onClick={() => setView("sendToVendor")}
        >
          Send to Vendors
        </button>
      </div>

      {view === "userNotifications" ? (
        users.length === 0 ? (
          <div className="no-notifications text-center">
            <img src={noDataImage} alt="No data" className="no-data-image" />
            <h3 className="text-lg font-semibold">
              No Notifications to show right now!
            </h3>
            <p className="text-light-gray">
              Notifications will be shown here. Notifications will be shown
              here.
            </p>
            <button
              className="btn bg-yellow-400 mt-4 active-btn"
              onClick={togglePopup}
            >
              CREATE NOW
            </button>
          </div>
        ) : (
          <div className="notifications-list">
            {dummyUserData.map((entry) => (
              <div
                className="user-notification-item flex items-center p-4 mb-4 rounded-lg"
                key={entry.id}
              >
                <div className="user-notification-avatar bg-gray-500 rounded-full w-12 h-12 flex items-center justify-center text-white text-lg mr-4">
                  {entry.name.charAt(0)}
                </div>
                <div className="user-notification-content">
                  <h4 className="text-xl font-semibold">{entry.name}</h4>
                  <p className="text-light-gray">{entry.text}</p>
                </div>
              </div>
            ))}
          </div>
        )
      ) : view === "sendToVendor" ? (
        vendorsList.length === 0 ? (
          <div className="no-notifications text-center">
            <img src={noDataImage} alt="No data" className="no-data-image" />
            <h3 className="text-lg font-semibold">
              No Notifications to show right now!
            </h3>
            <p className="text-light-gray">
              Notifications will be shown here. Notifications will be shown
              here.
            </p>
            <button
              className="btn bg-yellow-400 mt-4 active-btn"
              onClick={togglePopup}
            >
              CREATE NOW
            </button>
          </div>
        ) : (
          <div className="notifications-list">
            {dummyVendorData.map((entry) => (
              <div
                className="vendor-notification-item flex items-center p-4 mb-4 rounded-lg"
                key={entry.id}
              >
                <div className="vendor-notification-avatar bg-gray-500 rounded-full w-12 h-12 flex items-center justify-center text-white text-lg mr-4">
                  {entry.name.charAt(0)}
                </div>
                <div className="vendor-notification-content">
                  <h4 className="text-xl font-semibold">{entry.name}</h4>
                  <p className="text-light-gray">{entry.text}</p>
                </div>
              </div>
            ))}
          </div>
        )
      ) : null}

      {/* Floating action button (FAB) to open the popup */}
      <button className="fab bg-yellow-400 text-white" onClick={togglePopup}>
        ✎
      </button>

      {/* Popup for sending notifications */}
      {isPopupVisible && (
        <div className="popup-overlay" onClick={togglePopup}>
          <div className="popup-content" onClick={(e) => e.stopPropagation()}>
            <button className="close-btn" onClick={togglePopup}>
              &larr;
            </button>
            <h2 className="popup-title">Create Notification!</h2>
            <p className="popup-subtitle">
              Write what you want to say to your{" "}
              {view === "userNotifications" ? "users" : "vendors"}!
            </p>
            <input
              type="text"
              className="popup-input"
              placeholder="Title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
            <textarea
              className="popup-textarea"
              placeholder="Write here..."
              value={body}
              onChange={(e) => setBody(e.target.value)}
            ></textarea>
            <div className="popup-send-btn-container">
              <button
                className="popup-send-btn"
                onClick={handleSendNotification}
              >
                Send
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Notifications;
