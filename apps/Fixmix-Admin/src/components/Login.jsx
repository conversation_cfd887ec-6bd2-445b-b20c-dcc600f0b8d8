import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { login } from "../slices/authSlice";
import "./Login.css";
import FixMix_logo_admin from "../assets/FixMix App Screens/Fixmix User.png";
import EmailIcon from "./../assets/EmailIcon";
import ShowPasswordIcon from "./../assets/ShowPasswordIcon";
import Swal from "sweetalert2";
import { forgotPassword } from "./../services/api.js";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [emailforForgot, setemailforForgot] = useState("");

  const handleEmilChange = (e) => {
    setemailforForgot(e.target.value);
  };

  const sendEmailForForgot = async (e) => {
    try {
      const response = await forgotPassword(emailforForgot);

      if (response.status == 200) {
        localStorage.setItem("password", password);

        Swal.fire({
          title: `New password send on you mail.`,
          icon: "Success",

          confirmButtonText: "Ok",
        });
      }
      try {
        if (response.status == 429) {
          Swal.fire({
            title: `try after some time`,
            icon: "Error",

            confirmButtonText: "Ok",
          });
        } else {
          Swal.fire({
            title: `try after some time`,
            icon: "Error",

            confirmButtonText: "Ok",
          });
        }
      } catch (error) {
        if (response.status == 429) {
          Swal.fire({
            title: `try after some time`,
            icon: "Error",

            confirmButtonText: "Ok",
          });
        } else {
          Swal.fire({
            title: `try after some time`,
            icon: "Error",

            confirmButtonText: "Ok",
          });
        }
      }
    } catch (error) {
      Swal.fire({
        title: `try after some time`,
        icon: "Error",

        confirmButtonText: "Ok",
      });
    }
  };

  const dispatch = useDispatch();

  const navigate = useNavigate();

  const { loading, error, isAuthenticated } = useSelector(
    (state) => state.auth
  );

  const handleLogin = async (e) => {
    e.preventDefault();
    if (!email || !password) {
      Swal.fire({
        title: "Error",
        text: "Email and Password are required",
        icon: "error",
        confirmButtonText: "Ok",
      });
      return;
    }

    try {
      await dispatch(login({ email, password })).unwrap();
      navigate("/");
    } catch (err) {
      console.error("Login failed:", err);
    }
  };

  const handleForgotPasswordClick = () => {
    setShowForgotPassword(true);
  };

  const handleClosePopup = () => {
    setShowForgotPassword(false);
  };

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="login-page h-full min-h-screen">
      <div className="login-container ">
        <div className="logo flex gap-4 items-center mb-4">
          <img src={FixMix_logo_admin} alt="FixMix Logo" />
          <div className="md:hidden">
            <h2 className="welcometxt">Welcome Back!</h2>
            <p>Step back into your FixMix journey with ease!</p>
          </div>
        </div>
        <h2 className="welcometxt hidden md:block">Welcome Back!</h2>
        <p className="hidden md:block">Step back into your FixMix journey with ease!</p>
        <form onSubmit={handleLogin}>
          <div className="input-group">
            <label htmlFor="email">Email ID</label>
            <div className="input-wrapper">
              <input
                type="email"
                id="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
              <span className="icon">
                <EmailIcon />
              </span>
            </div>
          </div>
          <div className="input-group">
            <label htmlFor="password">Password</label>
            <div className="input-wrapper">
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                className="text-black"
                placeholder="Enter Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
              <span className="icon" onClick={toggleShowPassword}>
                <ShowPasswordIcon />
              </span>
            </div>
          </div>
          <div className="fpogo">
            <a href="#" onClick={handleForgotPasswordClick}>
              Forgot Password?
            </a>
          </div>
          <div className="forgot-passwod">
            <button className="sign-in-btn" type="submit" disabled={loading}>
              {loading ? "Signing In..." : "Sign In"}
            </button>
          </div>
          {error && <div className="error">Invalid email or password</div>}
        </form>
      </div>
      <div className="empty-space"></div>

      {showForgotPassword && (
        <div className="forgot-password-popup">
          <div className="popup-content">
            <button className="back-btn" onClick={handleClosePopup}>
              ←
            </button>
            <h2>Forgot Password?</h2>
            <p>
              Do not worry! We will help you in logging back to your FixMix
              account safely! Enter Your Email ID below, we will send a new
              password creation link there!
            </p>
            <div className="input-group">
              <label htmlFor="reset-email">Email</label>
              <input
                value={emailforForgot}
                onChange={handleEmilChange}
                type="email"
                id="reset-email"
                placeholder="<EMAIL>"
              />
            </div>
            <button className="send-email-btn" onClick={sendEmailForForgot}>
              Send Email
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Login;
