.vendor-detail-container {
  margin-left: 1rem;
  margin-top: 0.5rem;
  padding: 1rem;
  position: relative;
  max-width: 24in;
}

.breadcrumb {
  font-family: "Poppins", sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px;
  line-height: 24px;
  color: #9d9d9d;
}

.breadcrumb-separator {
  width: 100%;
}

.vendor-image {
  width: 255px;
  height: 255px;
  background-size: cover;
  border-radius: 50%;
}

.vendor-info label {
  display: block;
  margin-top: 20px;
  font-family: "Poppins", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 16px;
  color: #9d9d9d;
}

.vendor-input {
  display: block;
  width: 100%;
  height: 60px;
  margin-top: 10px;
  padding: 10px;
  background: #2c2e2d;
  border-radius: 10px;
  font-family: "Poppins", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 16px;
  color: #ffffff;
  border: none;
  resize: none;
}

.vendor-metrics {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.metric-card {
  width: 245px;
  height: 120px;
  background: #2c2e2d;
  box-shadow: 2px 2px 8.3px -5px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.metric-title {
  font-family: "Poppins", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #9d9d9d;
}

.metric-name {
  font-family: "Poppins", sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 30px;
  color: #ffffff;
}

.metric-value {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 40px;
  line-height: 48px;
  margin-top: 5px;
}

.star-icon {
  color: #f2b51a;
  margin-left: 0.25rem;
}

.vendor-metrics {
  margin-top: 2rem;
  display: flex;
  gap: 1.5rem;
}

@media (max-width: 1200px) {
  .breadcrumb,
  .breadcrumb-separator,
  .vendor-image,
  .vendor-info input,
  .vendor-info textarea {
    left: 20px;
    width: calc(100% - 40px);
  }
  .vendor-image {
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
  }
  .vendor-info input,
  .vendor-info textarea {
    width: calc(100% - 40px);
  }
  .vendor-metrics {
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }
  .metric-card {
    width: calc(100% - 40px);
  }
}

@media (max-width: 768px) {
  .vendor-detail-container {
    margin-left: 0;
  }
  .breadcrumb,
  .breadcrumb-separator,
  .vendor-image,
  .vendor-info input,
  .vendor-info textarea,
  .metric-card {
    width: calc(100% - 20px);
    left: 10px;
  }
}
