import React, { useEffect } from "react";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import userIcon from "../assets/userIcon.webp";
import UserMetrics from "./UserMetrics";
import "./UserDetail.css";
import userIccon from "../assets/userIcon.webp";
import { BASE_URL } from "./../services/api";
import { fetchUserById } from "../slices/userSlice";

const UserDetail = () => {
  const { userId } = useParams();
  const dispatch = useDispatch();

  const { selectedUser: user, loading, error } = useSelector((state) => state.users);
  useEffect(() => {
    if (userId) {
      dispatch(fetchUserById(userId)); // Fetch user when component mounts
    }
  }, [dispatch, userId]);
  if (loading) return <div className="text-white">Loading user details...</div>;
  if (error) return <div className="text-white">Error: {error}</div>;
  if (!user) return <div className="text-white">User not found</div>;
  const profileImageUrl = user?.profilePhoto
    ? `${user.profilePhoto}`
    : userIccon;
  console.log(user?.profilePhoto);

  if (!user) {
    return <div className="text-white">User not found</div>;
  }
  console.log(user);
  const avarageOrderValue = (user.totalSpending / user.totalOrders).toFixed(0);

  // const address =
  //   user?.addresses[0]?.addressLine1 ??
  //   " " + " , " + user?.addresses[0]?.addressLine2 ??
  //   " " + " , " + user?.addresses[0]?.city ??
  //   " " + " , " + user?.addresses[0]?.state ??
  //   " " + " , " + user?.addresses[0]?.country ??
  //   " " + " , " + user?.addresses[0]?.postalCode ??
  //   " ";

  return (
    <div className="user-detail-container mx-auto max-w-6xl pt-20">
      <div className="breadcrumb text-light-gray text-sm mb-4 flex items-center">
        <span
          className="cursor-pointer mr-2"
          onClick={() => window.history.back()}
        >
          &larr;
        </span>
        <span className="ml-2 text-black">Dashboard/Users/<USER>/span>
      </div>
      <div className="breadcrumb-separator border-t border-gray-700 mb-8"></div>
      <div className="user-detail-grid grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="user-info space-y-6">
          <div>
            <label className="block text-black text-sm">Full Name</label>
            <input
              type="text"
              value={user.name}
              readOnly
              className="text-black border border-black/25"
            />
          </div>
          <div>
            <label className="block text-light-gray text-sm">Email ID</label>
            <input
              type="email"
              value={user.email}
              readOnly
              className="text-black border border-black/25"

            />
          </div>
          {/* <div>
            <label className="block text-light-gray text-sm">Password</label>
            <input
              // type="password"
              value={user.password}
              readOnly
                            className="text-black border border-black/25"
            />
          </div> */}
          {/* <div>
            <label className="block text-light-gray text-sm">Address</label>
            <textarea
              // value={user.address}
              readOnly
              className="text-black border border-black/25"
            ></textarea>
          </div> */}
          <div>
            <label className="block text-light-gray text-sm">
              Mobile
            </label>
            <input
              type="text"
              value={user.phone}
              readOnly
              className="text-black border border-black/25"
            />
          </div>
          <div>
            <label className="block text-light-gray text-sm">
              Role
            </label>
            <input
              type="text"
              value={user.role}
              readOnly
              className="text-black border border-black/25"
            />
          </div>
        </div>
        <div className="user-image-wrapper flex justify-center items-center">
          <div
            className="user-image w-64 h-64 rounded-full mx-auto bg-cover bg-center"
            style={{ backgroundImage: `url(${profileImageUrl})` }}
          ></div>
        </div>
      </div>
      <div className="user-metrics mt-10 grid grid-cols-3 gap-6">
        {/* <UserMetrics title="Total Orders" count={user.totalOrders} />
        <UserMetrics title="Total wishlist" count={user.wishlist.length} />
        <UserMetrics title="Total Spending" count={"€ " + user.totalSpending} /> */}
      </div>
    </div>
  );
};

export default UserDetail;
