import React, { useState } from "react";
import "./CreateUserModal.css";
import { createUser, user } from "../services/api";
import { useDispatch, useSelector } from "react-redux";
import { createUserThunk, fetchUsers } from "../slices/userSlice";

// Full list of country codes
const countryCodes = [
  { code: "+1", country: "United States" },
  { code: "+44", country: "United Kingdom" },
  { code: "+91", country: "India" },
  { code: "+61", country: "Australia" },
  { code: "+81", country: "Japan" },
  { code: "+33", country: "France" },
  { code: "+49", country: "Germany" },
  { code: "+86", country: "China" },
  { code: "+39", country: "Italy" },
  { code: "+7", country: "Russia" },
  { code: "+55", country: "Brazil" },
  { code: "+27", country: "South Africa" },
  { code: "+34", country: "Spain" },
  { code: "+82", country: "South Korea" },
  { code: "+92", country: "Pakistan" },
  { code: "+966", country: "Saudi Arabia" },
  { code: "+971", country: "United Arab Emirates" },
  { code: "+65", country: "Singapore" },
  { code: "+93", country: "Afghanistan" },
  { code: "+20", country: "Egypt" },
  { code: "+98", country: "Iran" },
  { code: "+31", country: "Netherlands" },
  { code: "+46", country: "Sweden" },
  { code: "+32", country: "Belgium" },
  { code: "+90", country: "Turkey" },
  { code: "+66", country: "Thailand" },
  { code: "+60", country: "Malaysia" },
  { code: "+964", country: "Iraq" },
  { code: "+45", country: "Denmark" },
  { code: "+351", country: "Portugal" },
  { code: "+47", country: "Norway" },
  { code: "+36", country: "Hungary" },
  { code: "+420", country: "Czech Republic" },
  { code: "+48", country: "Poland" },
  { code: "+353", country: "Ireland" },
  { code: "+375", country: "Belarus" },
  { code: "+591", country: "Bolivia" },
  { code: "+964", country: "Iraq" },
  { code: "+212", country: "Morocco" },
  { code: "+372", country: "Estonia" },
  { code: "+993", country: "Turkmenistan" },
  { code: "+994", country: "Azerbaijan" },
  { code: "+256", country: "Uganda" },
  { code: "+249", country: "Sudan" },
  { code: "+995", country: "Georgia" },
  { code: "+372", country: "Estonia" },
  { code: "+357", country: "Cyprus" },
  { code: "+268", country: "Eswatini" },
];

const UserModel = ({ closeModal, updateUserData }) => {
  const dispatch = useDispatch();
  const { loading, error } = useSelector((state) => state.users);

  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [gender, setGender] = useState("");
  const [countryCode, setCountryCode] = useState("");

  const handleSubmit = async (e) => {
    e.preventDefault();
    await dispatch(createUserThunk({ name, email, phone, gender, countryCode }));
    dispatch(fetchUsers()); // Refresh user list
    closeModal(); // Close the modal
  };

  return (
    <div className="form-container">
      <div className="back-arrow" onClick={closeModal}>
        &#x2190;
      </div>
      <h1 className="text-black">Create New User!</h1>
      <p>Start your FixMix's vendor journey today!</p>
      <form onSubmit={handleSubmit}>
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="full-name" className="label">Full Name</label>
            <div className="input-container">
              <input
                type="text"
                id="full-name"
                placeholder="Full Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="p-4"
              />
              <span className="input-icon">&#128100;</span>
            </div>
          </div>
        </div>
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="email-id" className="label">Email ID</label>
            <div className="input-container">
              <input
                type="email"
                id="email-id"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="p-4"
              />
              <span className="input-icon">&#x2709;</span>
            </div>
          </div>
        </div>
        {/* Country Code Dropdown */}
        <div className="flex gap-4">
          <div className="form-group">
            <label htmlFor="country-code" className="label">Country Code</label>
            <div className="input-container">
              <select
                id="country-code"
                value={countryCode}
                onChange={(e) => setCountryCode(e.target.value)}
                required
                className="p-4"
              >
                <option value="" disabled>Select Country Code</option>
                {countryCodes.map(({ code, country }) => (
                  <option key={code} value={code}>
                    {country} ({code})  {/* This renders as "United States (+1)" */}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div className="form-group">
            <label htmlFor="mobile" className="label">Mobile No</label>
            <div className="input-container">
              <input
                type="number"
                id="mobile"
                placeholder="98765432"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                required
                className="no-arrows p-4"
              />
              <span className="input-icon">&#128222;</span>
            </div>
          </div>
        </div>

        {/* Mobile Number */}
        <div className="form-row">

        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="label ">Gender</label>
            <div className="gender-options flex gap-4">
              <label className="flex gap-2 items-center">
                <input
                  type="radio"
                  name="gender"
                  value="Male"
                  checked={gender === "Male"}
                  onChange={(e) => setGender(e.target.value)}
                  required
                />
                <div className="pb-3">Male</div>
              </label>
              <label className="flex gap-2 items-center">
                <input
                  type="radio"
                  name="gender"
                  value="Female"
                  checked={gender === "Female"}
                  onChange={(e) => setGender(e.target.value)}
                  required
                />
                <div className="pb-3">Female</div>
              </label>
              <label className="flex gap-2 items-center">
                <input
                  type="radio"
                  name="gender"
                  value="Other"
                  checked={gender === "Other"}
                  onChange={(e) => setGender(e.target.value)}
                  required
                />
                <div className="pb-3">Other</div>
              </label>
            </div>
          </div>
        </div>

        <button type="submit" className="create-button-popup">
          Create
        </button>
      </form>
    </div>

  );
};

export default UserModel;
