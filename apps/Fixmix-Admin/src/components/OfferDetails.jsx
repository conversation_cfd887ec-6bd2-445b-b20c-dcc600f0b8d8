import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import userIcon from "../assets/userIcon.webp";
import { fetchOfferById } from '../slices/offerSlice';


const OfferDetails = () => {
    const { Id } = useParams();
    const dispatch = useDispatch();
    const { selectedOffer: offers, loading, error } = useSelector((state) => state.offer);
    useEffect(() => {
        if (Id) {
            dispatch(fetchOfferById(Id)); // Fetch user when component mounts
        }
    }, [dispatch, Id]);
    if (loading) return <div className="text-white">Loading user details...</div>;
    if (error) return <div className="text-white">Error: {error}</div>;
    if (!offers) return <div className="text-white">User not found</div>;
    const categoryImageUrl = offers?.categoryId?.imageUrl
        ? `${offers?.categoryId?.imageUrl}`
        : userIcon;

    return (
        <div className="user-detail-container mx-auto max-w-6xl pt-20">
            <div className="breadcrumb text-light-gray text-sm mb-4 flex items-center">
                <span
                    className="cursor-pointer mr-2"
                    onClick={() => window.history.back()}
                >
                    &larr;
                </span>
                <span className="ml-2 text-black" onClick={() => console.log(offers)}>Dashboard / Offers / OfferDetails</span>
            </div>
            <div className="breadcrumb-separator border-t border-gray-700 mb-8"></div>
            <div className="user-detail-grid grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="user-info space-y-6">
                    <div>
                        <label className="block text-black text-sm">Name of User</label>
                        <input
                            type="text"
                            value={offers?.queryId?.userId?.name || ""}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>
                    <div>
                        <label className="block text-light-gray text-sm">
                            Mobile
                        </label>
                        <input
                            type="text"
                            value={offers?.queryId?.userId?.phone || ""}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>
                    <div>
                        <div className=" inline-block text-black/50 text-sm">
                            Status : &nbsp;
                        </div>
                        <div className='inline-block capitalize text-black'>
                            {offers.status}
                        </div>
                    </div>
                    <div>
                        <label className="block text-light-gray text-sm">
                            Description
                        </label>
                        <input
                            type="text"
                            value={offers.description}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>
                    <div>
                        <label className="block text-black text-sm">Name of Vendor</label>
                        <input
                            type="text"
                            value={offers?.vendorId?.name || ""}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>
                    <div>
                        <label className="block text-light-gray text-sm">
                            Mobile
                        </label>
                        <input
                            type="text"
                            value={offers?.vendorId?.phone || ""}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>
                    {/* <div>
                        <label className="block text-light-gray text-sm">
                            Title
                        </label>
                        <input
                            type="text"
                            value={offers.title}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div>
                    <div>
                        <label className="block text-light-gray text-sm">
                            Description
                        </label>
                        <input
                            type="text"
                            value={offers.description}
                            readOnly
                            className="text-black border border-black/25"
                        />
                    </div> */}

                </div>
                <div className="user-image-wrapper flex justify-center items-center">
                    <div
                        className="user-image w-64 h-64 rounded-full mx-auto bg-cover bg-center"
                        style={{ backgroundImage: `url(${categoryImageUrl})` }}
                    ></div>
                </div>
            </div>
            <div className="user-metrics mt-10 grid grid-cols-3 gap-6">
                {/* <UserMetrics title="Total Orders" count={user.totalOrders} />
        <UserMetrics title="Total wishlist" count={user.wishlist.length} />
        <UserMetrics title="Total Spending" count={"€ " + user.totalSpending} /> */}
            </div>
        </div>
    )
}

export default OfferDetails
