import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchDashboardCardData } from "../slices/dashboardSlice";

const BusinessCategoryTable = () => {
  const [selectedTab, setSelectedTab] = useState("All");
  const [activeMode, setActiveMode] = useState("query");
  const dispatch = useDispatch();

  // Get dashboard state from Redux
  const { data, loading, error } = useSelector((state) => state.dashboard);

  // Fetch data for all tabs only once when the component mounts
  useEffect(() => {
    const tabs = ["All", "Weekly", "Monthly", "Yearly"];
    tabs.forEach((tab) => {
      if (!data[tab]) {
        dispatch(fetchDashboardCardData({ period: tab }));
      }
    });
  }, [dispatch, data]); // Dependency includes data to prevent redundant calls

  // Get data for the current tab
  const currentTabData = data[selectedTab] || {};
  const queryData = currentTabData.queriesPerCategory || [];
  const orderData = currentTabData.ordersPerCategory || [];

  return (
    <div className="bg-secondary p-6 rounded-lg shadow-lg">
      {/* Tabs */}
      <div className="flex justify-start items-center mb-4 gap-3">
        {["All", "Weekly", "Monthly", "Yearly"].map((tab) => (
          <button
            key={tab}
            className={`tab-button text-sm lg:text-base ${selectedTab === tab ? "tab-button-active" : ""}`}
            onClick={() => setSelectedTab(tab)}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Toggle Buttons */}
      <div className="grid grid-cols-2 gap-4 mb-4 w-full">
        <button
          className={`px-4 py-2 text-base lg:text-lg rounded-md ${activeMode === "query" ? "bg-primary text-white" : "bg-gray-300"
            }`}
          onClick={() => setActiveMode("query")}
        >
          Query wise
        </button>
        <button
          className={`px-4 py-2 text-base lg:text-lg rounded-md ${activeMode === "order" ? "bg-primary text-white" : "bg-gray-300"
            }`}
          onClick={() => setActiveMode("order")}
        >
          Order wise
        </button>
      </div>

      {/* Loading and Error Handling */}
      {loading && <p className="text-center">Loading...</p>}
      {error && <p className="text-red-500 text-center">{error}</p>}

      {/* Table */}
      {!loading && !error && (
        <div className="overflow-hidden">
          <table className="min-w-full bg-secondary">
            <thead>
              <tr className="bg-secondary-light rounded-md text-black">
                <th className="py-3 px-4 text-left text-sm lg:text-base">
                  {activeMode === "query" ? "Query Category" : "Order"}
                </th>
                <th className="py-3 px-4 text-left text-sm lg:text-base">
                  {activeMode === "query" ? "Number of Queries" : "Number of Orders"}
                </th>
              </tr>
            </thead>
            <tbody>
              {(activeMode === "query" ? queryData : orderData).map((item, index) => (
                <tr
                  key={index}
                  className={`${index % 2 === 0 ? "" : "bg-secondary"} text-black`}
                >
                  <td className="py-3 px-4 text-sm lg:text-base">{item.name}</td>
                  <td className="py-3 px-4 text-sm lg:text-base">{item.count}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default BusinessCategoryTable;
