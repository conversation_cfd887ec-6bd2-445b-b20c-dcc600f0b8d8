/* ProgressOrders.css */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');

body {
  font-family: 'Poppins', sans-serif;
  background-color: #2C2E2D;
  color: white;
}

.progress-orders-container {
  padding: 2rem;
}

.filter-container {
  display: flex;
  justify-content: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.filter-button {
  background-color: #373938;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  border: none;
  cursor: pointer;
}

.orders-table {
  width: 100%;
  border-collapse: collapse;
}

.orders-table th,
.orders-table td {
  padding: 0.75rem;
  text-align: left;
}

.orders-table th {
  background-color: #202221;
}

.orders-table tr:nth-child(even) {
  background-color: #2C2E2D;
}

.orders-table tr:nth-child(odd) {
  background-color: #202221;
}

.orders-table tr:hover {
  background-color: #373938;
}

.download-report {
  text-align: right;
  margin-top: 1rem;
}

.download-link {
  color: #F6D724;
  text-decoration: none;
  font-weight: bold;
}

@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .orders-table th,
  .orders-table td {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .progress-orders-container {
    padding: 1rem;
  }

  .filter-container {
    gap: 0.5rem;
  }

  .filter-button {
    padding: 0.5rem;
  }

  .orders-table th,
  .orders-table td {
    font-size: 0.875rem;
  }

  .download-link {
    font-size: 0.875rem;
  }
}
