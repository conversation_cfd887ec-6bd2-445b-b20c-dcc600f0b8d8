// DashboardMetrics.js
import React from "react";

const DashboardMetrics = ({ title, count }) => (
  <div className="bg-secondary p-6 rounded-lg shadow-lg relative flex flex-col items-start min-w-[20rem]">
    <div>
      <div className="text-light-gray font-medium text-lg">
        {title.split(" ")[0]}
      </div>
      <div className="text-white font-semibold text-xl">
        {title.split(" ")[1]}
      </div>
    </div>
    <div className="text-accent-green  font-bold text-4xl">{count}</div>
  </div>
);

export default DashboardMetrics;
