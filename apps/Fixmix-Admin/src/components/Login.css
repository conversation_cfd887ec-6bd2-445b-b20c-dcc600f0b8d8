body {
  margin: 0;
  font-family: "Arial", sans-serif;
  background-color: #121212;
  color: #fff;
}

.sign-in-btn {
  width: 100%;
  max-width: 1000px;
  padding: 20px;
  margin-top: 100px;
  padding-top: 20px;
  border: none;
  border-radius: 18px;
  background: linear-gradient(90deg, #0096c7 0%, #6ccaf0 100%);
  color: #fff;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.sign-in-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0) 100%);
  transition: all 0.4s ease;
  z-index: 0;
}

.sign-in-btn:hover::before {
  left: 100%;
}

.sign-in-btn:hover {
  background: linear-gradient(90deg, #0096c7 0%, #6ccaf0 100%);
  transform: scale(1.05);
}

.sign-in-btn span {
  position: relative;
  z-index: 1;
}

.input-wrapper .icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

.login-page {
  display: flex;
  /* height: 100vh; */
  width: 100%;
}

.login-container {
  color: #121212;
  padding-left: 5rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
  /* display: flex; */
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  background-color: #fff;
  width: 60%;
  padding-right: 2%;
}

.logo {
  padding-left: 1%;
  padding-right: 2%;
  /* position: absolute; */
  top: 20px;
  left: 20px;
  display: flex;
  align-items: center;
}

.logo img {
  width: 150px;
  height: auto;
  max-width: 100%;
  margin-right: 10px;
}

.welcometxt {
  font-size: 2rem;
  margin: 0;
  padding-top: 5%;
  padding-right: 2%;
}

.fpogo {
  align-items: end;
  align-self: flex-end;
  margin-top: -25px;
  margin-bottom: 20px;
  padding-right: 2%;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper input {
  width: 100%;
  padding-right: 2rem;
}

.input-wrapper .icon {
  position: absolute;
  margin-right: 1rem;
  right: 1rem;
  bottom: 0.5rem;
}

p {
  font-size: 0.9rem;
  margin-bottom: 30px;
  color: #bbb;
  padding-bottom: 7%;
  padding-right: 2%;
}

form {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1000px;
  padding-right: 2%;
}

.input-group {
  position: relative;
  margin-bottom: 20px;
  width: 100%;
  padding-right: 2%;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 0.8rem;
  color: #bbb;
}

.input-group input {
  width: 100%;
  height: 70px;
  padding: 10px 40px 10px 10px;
  margin-bottom: 20px;
  border: 2px solid #bbb;
  border-radius: 18px;
  background-color: #2a2a2a00;
  /* color: #fff; */
  box-sizing: border-box;
}

.input-group .icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #bbb;
}

.forgot-password {
  align-self: flex-end;
  margin-top: -25px;
  margin-bottom: 20px;
  padding-right: 2%;
}

.forgot-password a {
  color: #bbb;
  font-size: 0.8rem;
  text-decoration: none;
}

.forgot-password a:hover {
  text-decoration: underline;
}

.empty-space {
  flex: 1;
  background-color: #d3d0d0;
}

.forgot-password-popup {
  position: fixed;

  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.popup-content {
  background-color: #faf9f9;
  padding: 20px;
  border-radius: 10px;
  width: 100%;
  max-width: 600px;
  height: 100%;
  max-height: 600px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  text-align: center;
}

/* Additional styles for the popup */

.back-btn {
  position: absolute;
  top: 20px;
  left: 20px;
  background: none;
  border: none;
  color: #fff;
  font-size: 1.5rem;
  cursor: pointer;
}

.forgot-password-popup .popup-content {
  position: relative;
  padding-top: 60px;
  /* Ensure enough space for the back button */
}

.send-email-btn {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 10px;
  background: linear-gradient(90deg, #0096c7 0%, #6ccaf0 100%);
  color: #fff;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 20px;
}

.send-email-btn:hover {
  background: linear-gradient(90deg, #0096c7 0%, #6ccaf0 100%);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .login-container {
    width: 100%;
    padding-left: 5%;
    padding-right: 5%;
  }

  .login-page {
    display: block;
    height: 100vh;
    width: 100%;
  }

  .logo {
    position: inherit;
    display: block;
    margin-top: 10px;
  }

  .logo img {
    width: 100px;
    height: auto;
  }

  .welcometxt {
    font-size: 1.4rem;
  }

  p {
    font-size: 0.8rem;
  }

  .input-group input {
    height: 60px;
    padding: 8px 30px 8px 8px;
  }

  .input-group {
    margin-bottom: 10px;
  }

  .sign-in-btn {
    margin-top: 50px;
  }
}

@media (max-width: 480px) {
  .login-container {
    width: 100%;
    padding-left: 5%;
    padding-right: 5%;
  }

  .logo img {
    width: 80px;
    height: auto;
  }

  .welcometxt {
    font-size: 1.2rem;
  }

  p {
    font-size: 0.7rem;
  }

  .input-group input {
    height: 50px;
    padding: 8px 20px 8px 8px;
  }

  .forgot-password a {
    font-size: 0.7rem;
  }

  @media (max-width: 768px) {
    .login-container {
      width: 100%;
      padding-left: 5%;
      padding-right: 5%;
    }

    .logo img {
      width: 100px;
      height: auto;
    }

    .welcometxt {
      font-size: 1.4rem;
    }

    p {
      font-size: 0.8rem;
    }

    .input-group input {
      height: 60px;
      padding: 8px 30px 8px 8px;
    }
  }

  @media (max-width: 480px) {
    .login-container {
      width: 100%;
      padding-left: 5%;
      padding-right: 5%;
    }

    .logo img {
      width: 80px;
      height: auto;
    }

    .welcometxt {
      font-size: 1.2rem;
    }

    p {
      font-size: 0.7rem;
    }

    .input-group input {
      height: 50px;
      padding: 8px 20px 8px 8px;
    }

    .forgot-password a {
      font-size: 0.7rem;
    }
  }
}

.error {
  color: red;
  margin-top: 10px;
  font-size: 0.9rem;
}

.input-group .icon {
  cursor: pointer;
}

.input-wrapper input:focus {
  outline: none;
  border-color: #0096c7;
  color: #121212;
}

.sign-in-btn {
  transition: background 0.3s ease;
}

.sign-in-btn:hover {
  /* background: linear-gradient(90deg, #2c9547 0%, #f6d724 100%); */
  background: linear-gradient(90deg, #0096c7 0%, #6ccaf0 100%);
}

.send-email-btn {
  background: linear-gradient(90deg, #0096c7 0%, #6ccaf0 100%);
  transition: background 0.3s ease;
}

.send-email-btn:hover {
  background: linear-gradient(90deg, #0096c7 0%, #6ccaf0 100%);
}