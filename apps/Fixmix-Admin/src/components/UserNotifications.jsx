import React, { useState } from "react";
import "./UserNotifications.css";

const UserNotifications = ({ sendNotification }) => {
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const [title, setTitle] = useState("");
  const [body, setBody] = useState("");

  const togglePopup = () => {
    setIsPopupVisible(!isPopupVisible);
  };

  const handleSendNotification = () => {
    sendNotification(title, body);
    setTitle(""); // Clear the input after sending
    setBody("");
    setIsPopupVisible(false); // Close the popup
  };

  const dummyEntries = [
    { id: 1, name: "<PERSON><PERSON><PERSON><PERSON>", text: "Hello friends" },
    { id: 2, name: "<PERSON><PERSON><PERSON><PERSON>__1", text: "Hellow world" },
  ];

  return (
    <div className="user-notifications-container self-center flex flex-col items-center p-4">
      <button className="fab bg-yellow-400 text-white" onClick={togglePopup}>
        ✎
      </button>
      {isPopupVisible && (
        <div className="popup-overlay" onClick={togglePopup}>
          <div className="popup-content" onClick={(e) => e.stopPropagation()}>
            <button className="close-btn" onClick={togglePopup}>
              &larr;
            </button>
            <h2 className="popup-title">Create Notification!</h2>
            <p className="popup-subtitle">
              Write what you want to say to your users!
            </p>
            <input
              type="text"
              className="popup-input"
              placeholder="Title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
            <textarea
              className="popup-textarea"
              placeholder="Write here..."
              value={body}
              onChange={(e) => setBody(e.target.value)}
            ></textarea>
            <div className="popup-send-btn-container">
              <button
                className="popup-send-btn"
                onClick={handleSendNotification}
              >
                Send
              </button>
            </div>
          </div>
        </div>
      )}
      {dummyEntries.map((entry) => (
        <div
          className="user-notification-item flex items-center p-4 mb-4 rounded-lg"
          key={entry.id}
        >
          <div className="user-notification-avatar bg-gray-500 rounded-full w-12 h-12 flex items-center justify-center text-white text-lg mr-4">
            {entry.name.charAt(0)}
          </div>
          <div className="user-notification-content">
            <h4 className="text-xl font-semibold">{entry.name}</h4>
            <p className="text-light-gray">{entry.text}</p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default UserNotifications;
