import React from "react";

const VendorMetrics = ({ title, count }) => (
  <div className="bg-secondary p-6 rounded-lg shadow-lg relative flex flex-col items-start">
    <div>
      <div className="text-black font-medium text-lg">
        {title.split(" ")[0]}
      </div>
      <div className="text-black font-semibold text-xl">
        {title.split(" ")[1]}
      </div>
    </div>
    <div className="text-primary font-bold text-4xl">{count}</div>
  </div>
);

export default VendorMetrics;
