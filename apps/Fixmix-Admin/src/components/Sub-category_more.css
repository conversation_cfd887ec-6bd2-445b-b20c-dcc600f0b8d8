.sub-category-container {
    padding: 20px;
}

.sub-category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}



.create-button {
    float: right;
    
    margin-bottom: 20px;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #000;
    color: #fff;
    padding: 20px;
    border-radius: 8px;
    width: 400px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    position: relative;
}

.modal-close-button {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
}

.modal-close-button:hover {
    color: #bbb;
}

.modal-content h2 {
    margin-bottom: 20px;
}

.modal-content input {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    border: 1px solid #555;
    background-color: #222;
    color: #fff;
}

.create-button-gradient {
    width: 100%;
    padding: 10px;
    background: linear-gradient(90deg, #f6d724 0%, #2c9547 100%);
    color: #000;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.create-button-gradient:hover {
    background: linear-gradient(90deg, #f6d724 0%, #2c9547 100%);
}

.sub-category-table {
    width: 100%;
    border-collapse: collapse;
}

.sub-category-table th, .sub-category-table td {
    padding: 15px;
    text-align: left;
}

.sub-category-table th {
    background-color: #333333;
    color: #cccccc;
}

.sub-category-table tr {
    background-color: #2c2c2c;
}

.sub-category-table tr:nth-child(even) {
    background-color: #333333;
}

.sub-category-table tr td {
    border-bottom: 1px solid #444;
}
