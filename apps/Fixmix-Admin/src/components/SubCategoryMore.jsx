import React, { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON> } from "antd";
import { useParams, useLocation, useNavigate } from "react-router-dom"; // Import hooks from react-router-dom
import {
  createSubcategory,
  deleteSubCategory,
  updateSubCategoryImage,
  updateSubCategoryName,
  uploadAdminImage,
} from "./../services/api"; // Import required API functions
import "./Sub-category_more.css";
import Swal from "sweetalert2";

const SubCategoryMore = () => {
  const { categoryId } = useParams(); // Get the category ID from the URL
  const { state } = useLocation(); // Access passed state (which contains subcategories)
  const navigate = useNavigate();

  // Local state for sub-categories (initialized from passed state)
  const [subCategoryData, setSubCategoryData] = useState(
    state?.subcategories || []
  );
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [newSubCategory, setNewSubCategory] = useState("");
  const [loading, setLoading] = useState(false); // For create operation

  // Loading states for updates (keyed by subcategory id)
  const [updateLoading, setUpdateLoading] = useState({});
  const [imageLoading, setImageLoading] = useState({});

  // For inline editing of subcategory names
  const [editingCategory, setEditingCategory] = useState(null);
  const [updatedName, setUpdatedName] = useState({});

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;

  // File input refs (one per subcategory)
  const fileInputRefs = useRef({});

  // Toggle the modal for creating a new sub-category
  const toggleModal = () => {
    setIsModalVisible((prev) => !prev);
  };

  // Create a new sub-category (state is updated optimistically without navigating away)
  const handleCreate = async () => {
    if (!newSubCategory) {
      alert("Sub-category name is required");
      return;
    }

    try {
      setLoading(true);
      await createSubcategory(newSubCategory, categoryId);

      // Create a formatted new sub-category entry (using a temporary ID)
      const newSubCategoryData = {
        _id: `${Date.now()}`,
        serialNo: `${subCategoryData.length + 1}`,
        name: newSubCategory,
        imageUrl: "", // You can set a default image if needed
        date: new Date().toLocaleDateString(),
      };

      setSubCategoryData((prev) => [...prev, newSubCategoryData]);
      setIsModalVisible(false);
      setNewSubCategory("");
      // No navigation here—update the state to reflect the change instantly.
    } catch (error) {
      console.error("Error creating subcategory:", error);
      alert("Failed to create subcategory. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Delete a sub-category and update state without a page refresh
  const handleDelete = (id) => {
    try {
      Swal.fire({
        title: "Are you sure?",
        text: "Press on Ok if you want to delete this subcategory.",
        icon: "question",
        showCancelButton: true,
        cancelButtonText: "Cancel",
        confirmButtonText: "Ok",
      }).then(async (result) => {
        if (result.isConfirmed) {
          await deleteSubCategory(id);
          // Remove the deleted subcategory from state
          setSubCategoryData((prev) =>
            prev.filter((subCategory) => subCategory._id !== id)
          );
        }
      });
    } catch (err) {
      console.error("Delete failed:", err);
    }
  };

  // Helper function to upload an image
  const uploadImage = async (event) => {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append("image", file);
      try {
        const response = await uploadAdminImage(formData);
        return response.imageUrl;
      } catch (err) {
        console.error("Error uploading image:", err);
        return null;
      }
    }
    return null;
  };

  // Enable edit mode for a subcategory's name
  const handleEditClick = (subCategory) => {
    setEditingCategory(subCategory._id);
    setUpdatedName((prevNames) => ({
      ...prevNames,
      [subCategory._id]: subCategory.name,
    }));
  };

  // Save the updated subcategory name with an optimistic update
  const handleSaveClick = async (subcategoryId) => {
    const newName = updatedName[subcategoryId];

    if (!newName || !newName.trim()) {
      alert("Subcategory name cannot be empty");
      return;
    }

    try {
      // Set loading state for this subcategory's name update
      setUpdateLoading((prev) => ({ ...prev, [subcategoryId]: true }));
      await updateSubCategoryName(subcategoryId, newName);

      // Optimistically update the local state
      setSubCategoryData((prevData) =>
        prevData.map((subCategory) =>
          subCategory._id === subcategoryId
            ? { ...subCategory, name: newName }
            : subCategory
        )
      );
      setEditingCategory(null);
    } catch (error) {
      console.error("Error updating subcategory name:", error);
      alert("Failed to update subcategory name. Please try again.");
    } finally {
      setUpdateLoading((prev) => ({ ...prev, [subcategoryId]: false }));
    }
  };

  // Update the subcategory image with an optimistic update
  const handleUpdateCategoryImage = async (id, event) => {
    try {
      // Set loading state for image update
      setImageLoading((prev) => ({ ...prev, [id]: true }));
      const imageUrl = await uploadImage(event);
      // if (imageUrl) {
      //   await updateSubCategoryImage(imageUrl, id);
      //   setSubCategoryData((prevData) =>
      //     prevData.map((subCategory) =>
      //       subCategory._id === id ? { ...subCategory, imageUrl } : subCategory
      //     )
      //   );
      // }
    } catch (error) {
      console.error("Error updating subcategory image:", error);
      alert("Failed to update subcategory image. Please try again.");
    } finally {
      setImageLoading((prev) => ({ ...prev, [id]: false }));
    }
  };

  // Pagination functions
  const handlePrevious = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const handleNext = () => {
    if (currentPage < Math.ceil(subCategoryData.length / rowsPerPage)) {
      setCurrentPage(currentPage + 1);
    }
  };

  const getCurrentPageData = () => {
    const startIndex = (currentPage - 1) * rowsPerPage;
    return subCategoryData.slice(startIndex, startIndex + rowsPerPage);
  };

  return (
    <div className="sub-category-container">
      <div className="sub-category-header">
        <span className="cursor-pointer mr-2" onClick={() => navigate(-1)}>
          &larr; <span>Sub-Categories</span>
        </span>
      </div>

      <Button type="primary" className="create-button" onClick={toggleModal}>
        Add New Sub-Category
      </Button>

      {isModalVisible && (
        <div className="modal-overlay">
          <div className="modal-content">
            <button className="modal-close-button" onClick={toggleModal}>
              ×
            </button>
            <h2>Add New Sub-Category</h2>
            <input
              type="text"
              placeholder="Sub-Category Name"
              value={newSubCategory}
              onChange={(e) => setNewSubCategory(e.target.value)}
            />
            <Button
              type="primary"
              className="create-button-gradient text-white"
              onClick={handleCreate}
              loading={loading}
            >
              Add
            </Button>
          </div>
        </div>
      )}

      <table className="sub-category-table">
        <thead>
          <tr>
            <th>No.</th>
            <th>Sub-Category</th>
            <th>Image</th>
            <th>Sub-Category ID</th>
            <th>Edit</th>
            <th>Delete</th>
          </tr>
        </thead>
        <tbody>
          {getCurrentPageData().map((subCategory, index) => (
            <tr key={subCategory._id}>
              <td>{(currentPage - 1) * rowsPerPage + index + 1}</td>
              <td>
                {editingCategory === subCategory._id ? (
                  <>
                    <input
                      type="text"
                      value={updatedName[subCategory._id] || ""}
                      onChange={(e) =>
                        setUpdatedName((prevNames) => ({
                          ...prevNames,
                          [subCategory._id]: e.target.value,
                        }))
                      }
                      className="border border-green-500 text-green-500 p-1"
                    />
                    {updateLoading[subCategory._id] && (
                      <Spin size="small" style={{ marginLeft: 8 }} />
                    )}
                  </>
                ) : (
                  subCategory.name
                )}
              </td>
              <td>
                <div
                  className="circle-image"
                  style={{
                    backgroundImage: `url(${subCategory.imageUrl})`,
                    width: "50px",
                    height: "50px",
                    borderRadius: "50%",
                    backgroundSize: "cover",
                    cursor: "pointer",
                    position: "relative",
                  }}
                  onClick={() => {
                    Swal.fire({
                      title: "Are you sure?",
                      text: "Do you really want to change the subcategory image?",
                      icon: "warning",
                      showCancelButton: true,
                      confirmButtonText: "Yes, change it!",
                      cancelButtonText: "No, keep it",
                    }).then((result) => {
                      if (result.isConfirmed) {
                        if (!fileInputRefs.current[subCategory._id]) {
                          fileInputRefs.current[subCategory._id] =
                            React.createRef();
                        }
                        fileInputRefs.current[subCategory._id].current.click();
                      } else {
                        Swal.fire(
                          "Cancelled",
                          "The subcategory image was not changed.",
                          "info"
                        );
                      }
                    });
                  }}
                >
                  {imageLoading[subCategory._id] && (
                    <div
                      style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        backgroundColor: "rgba(255,255,255,0.7)",
                        borderRadius: "50%",
                      }}
                    >
                      <Spin size="small" />
                    </div>
                  )}
                </div>
                <input
                  type="file"
                  accept="image/*"
                  ref={
                    !fileInputRefs.current[subCategory._id]
                      ? (fileInputRefs.current[subCategory._id] =
                        React.createRef())
                      : fileInputRefs.current[subCategory._id]
                  }
                  style={{ display: "none" }}
                  onChange={(event) =>
                    handleUpdateCategoryImage(subCategory._id, event)
                  }
                />
              </td>
              <td>{subCategory._id}</td>
              <td>
                {editingCategory === subCategory._id ? (
                  <button
                    className="text-green-500 underline"
                    onClick={() => handleSaveClick(subCategory._id)}
                    disabled={updateLoading[subCategory._id]}
                  >
                    Save
                  </button>
                ) : (
                  <button
                    className="more-button"
                    onClick={() => handleEditClick(subCategory)}
                  >
                    Edit
                  </button>
                )}
              </td>
              <td>
                <button
                  className="more-button"
                  onClick={() => handleDelete(subCategory._id)}
                >
                  Delete
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      <div className="pagination-container">
        <Button onClick={handlePrevious} disabled={currentPage === 1}>
          Previous
        </Button>
        <span>
          {currentPage} out of {Math.ceil(subCategoryData.length / rowsPerPage)}
        </span>
        <Button
          onClick={handleNext}
          disabled={
            currentPage === Math.ceil(subCategoryData.length / rowsPerPage)
          }
        >
          Next
        </Button>
      </div>
    </div>
  );
};

export default SubCategoryMore;
