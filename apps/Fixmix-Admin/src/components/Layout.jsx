// Layout.jsx
import React from "react";
import { Outlet, useLocation } from "react-router-dom";
import Sidebar from "./Sidebar";

const Layout = () => {
  const location = useLocation();

  const getSearchPlaceholder = () => {
    switch (location.pathname) {
      case "/users":
        return "Search Users Here";
      case `/users/${location.pathname.split("/")[2]}`:
        return "Search Users Here";
      case "/vendors":
        return "Search Vendors Here";
      case `/vendors/${location.pathname.split("/")[2]}`:
        return "Search Vendors Here";
      case "/Category":
        return "Search Category Here";
      case "/orders":
        return "Search Orders Here";
      case "/notifications":
        return "Search notifications Here";
      case "/vendors-payout":
        return "Search vendors payout Here";
      default:
        return "Search anything here";
    }
  };

  return (
    <div className="min-h-screen flex bg-white overflow-hidden relative">
      <Sidebar />
      <main className="flex-1 overflow-y-auto ">
        <Outlet />
      </main>
    </div>
  );
};

export default Layout;
