.user-notifications-container {
  width: 100%;
  max-width: 800px;
  padding: 20px;
  box-sizing: border-box;
}

.user-notification-item {
  background: #383838;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 10px;
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.user-notification-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-notification-content {
  flex: 1;
}

.user-notification-content h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.user-notification-content p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  font-size: 0.9rem;
}

.fab {
  background: linear-gradient(90deg, #f6d724 0%, #2c9547 100%);
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive styles */
@media (max-width: 768px) {
  .user-notification-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .user-notification-avatar {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .fab {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .user-notification-item {
    padding: 15px;
  }

  .user-notification-avatar {
    width: 40px;
    height: 40px;
  }

  .user-notification-content h4 {
    font-size: 1rem;
  }

  .user-notification-content p {
    font-size: 0.8rem;
  }

  .fab {
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
}
