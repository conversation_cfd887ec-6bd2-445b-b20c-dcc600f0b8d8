/* Modal overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  /* Semi-transparent dark background for blur effect */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Modal container */
.modal-container {
  background: #fff;
  color: #000;
  /* Dark background for the modal */
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  position: relative;
}

/* Back arrow */
.back-arrow {
  font-size: 1.5rem;
  cursor: pointer;
  margin-bottom: 1rem;
  color: #0096c7;
  /* Highlight color */
}

/* Heading */
h1 {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
  color: #fff;
  /* White color for heading */
}

/* Paragraph text */
p {
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.7);
  /* Light gray color */
}

/* Form row */
.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

/* Form group */
.form-group {
  flex: 2;
}

/* Label */
.label {
  font-size: 1rem;
  margin-bottom: 0.2rem;
  display: block;
  color: #000;
  /* White color for labels */
}

/* Input container */
.input-container {
  position: relative;
}

/* Inputs and selects */
input,
select {
  width: 100%;
  height: 3rem;
  padding: 1rem;
  /* Increased padding for larger text boxes */
  padding-right: 5rem;
  /* Increased padding-right to accommodate icons */
  font-size: 1rem;
  /* Increased font size */
  border: 1px solid #555;
  /* Darker border color for better visibility */
  border-radius: 5px;
  background: #38383800;
  /* Dark background color */
  color: #fff;
  /* White text color */
}

a,
area,
button,
[role='button'],
input:not([type='range']),
label,
select,
summary,
textarea {
  touch-action: manipulation;
  margin-bottom: 0.8rem;
}

/* Input icons */
.input-icon {
  position: absolute;
  top: 50%;
  right: 0.5rem;
  transform: translateY(-50%);
  color: #ccc;
  /* Light gray color for icons */
}

/* Create button */
.create-button-popup {
  background: linear-gradient(90deg, #0096c7 0%, #4bd1fd 100%);
  max-width: 10rem;
  /* Button color */
  margin-left: auto;
  color: #fff;
  /* Button text color */
  border: none;
  padding: 12px 24px;
  /* Increased padding for larger button */
  border-radius: 10px;
  cursor: pointer;
  margin-top: 0.5rem;
  font-size: 1rem;
}


/* Responsive styles */
@media (max-width: 768px) {
  .modal-container {
    width: 90%;
  }
}

@media (max-width: 480px) {
  .modal-container {
    width: 100%;
    padding: 1rem;
  }

  .back-arrow {
    font-size: 1.25rem;
  }

  h1 {
    font-size: 1.5rem;
  }

  .form-row {
    flex-direction: column;
  }
}