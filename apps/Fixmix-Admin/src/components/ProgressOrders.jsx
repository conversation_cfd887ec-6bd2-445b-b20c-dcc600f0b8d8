import React from 'react';
import './ProgressOrders.css'; // Import the CSS file

const ProgressOrders = () => {
  const orders = [
    { id: 1, orderName: '1-2 years boy Blue Color Pure cotton.....', buyerName: 'Jay saini', sellerName: 'Jay saini', orderStatus: 'Shipped' },
    { id: 2, orderName: '1-2 years boy Blue Color Pure cotton.....', buyerName: 'Vinit patel', sellerName: 'Vinit patel', orderStatus: 'Out for Delivery' },
    { id: 3, orderName: '1-2 years boy Blue Color Pure cotton.....', buyerName: '<PERSON><PERSON><PERSON> jain', sellerName: '<PERSON><PERSON><PERSON> jain', orderStatus: 'Order Placed' },
    { id: 4, orderName: 'Girl Cloth Pair', buyerName: 'Aarushi vaidya', sellerName: 'Aarushi vaidya', orderStatus: 'Out for Delivery' },
    { id: 5, orderName: 'Mobile accessories', buyerName: 'Mannat patel', sellerName: 'Mannat patel', orderStatus: 'Out for Delivery' },
    { id: 6, orderName: 'Furniture', buyerName: 'Ruchita sindhvani', sellerName: 'Ruchita sindhvani', orderStatus: 'Order Placed' },
    { id: 7, orderName: 'Home Decoration Set', buyerName: 'Jaimin patel', sellerName: 'Jaimin patel', orderStatus: 'Order Placed' },
    { id: 8, orderName: 'Kitchen utensils set', buyerName: 'Meet patel', sellerName: 'Meet patel', orderStatus: 'Order Placed' },
    { id: 9, orderName: 'Kitchen utensils set', buyerName: 'Naren shah', sellerName: 'Naren shah', orderStatus: 'Shipped' },
  ];

  return (
    <div className="progress-orders-container">
      <div className="filter-container">
        <button className="filter-button">Category</button>
        <button className="filter-button">Status</button>
      </div>
      <table className="orders-table">
        <thead>
          <tr>
            <th>No.</th>
            <th>Order Name</th>
            <th>Buyer Name</th>
            <th>Seller Name</th>
            <th>Order Status</th>
          </tr>
        </thead>
        <tbody>
          {orders.map((order, index) => (
            <tr key={order.id}>
              <td>{index + 1}</td>
              <td>{order.orderName}</td>
              <td>{order.buyerName}</td>
              <td>{order.sellerName}</td>
              <td>{order.orderStatus}</td>
            </tr>
          ))}
        </tbody>
      </table>
      {/* <div className="download-report">
        <a href="#" className="download-link">Download Report</a>
      </div> */}
    </div>
  );
};

export default ProgressOrders;
