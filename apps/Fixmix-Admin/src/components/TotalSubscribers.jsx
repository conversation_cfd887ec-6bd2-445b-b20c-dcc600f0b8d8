import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchSubscribers } from "../slices/subscriberSlice";
import { ShimmerTable } from "shimmer-effects-react";

const TotalSubscribers = () => {
  // const dispatch = useDispatch();
  // const { data: subscribersData, status } = useSelector(
  //   (state) => state.subscribers
  // );

  // Dummy Data
  const dummyData = [
    { _id: "Retail", totalActiveSubscribers: 120, },
    { _id: "Technology", totalActiveSubscribers: 80, },
    { _id: "Healthcare", totalVtotalActiveSubscribersendors: 60, },
    { _id: "Education", totalActiveSubscribers: 40, },
    { _id: "Manufacturing", totalActiveSubscribers: 50, },
  ];

  // useEffect(() => {
  //   // Fetch subscribers data for "all" time without filtering
  //   dispatch(fetchSubscribers("all"));
  // }, [dispatch]);

  // if (status === "loading") {
  //   return (
  //     <div className="p-5">
  //       <ShimmerTable mode="light" row={7} col={2} border={0} borderColor={"#cbd5e1"} rounded={0.25} rowGap={16} colPadding={[10, 5, 10, 5]} />
  //     </div>
  //   );
  // }

  // if (status === "failed") {
  //   return <div>Error loading subscribers data.</div>;
  // }

  return (
    <div className="bg-secondary p-6 rounded-lg shadow-lg">
      <h2 className="text-xl font-bold text-black mb-4">Total Subscribers</h2>
      <ul className="space-y-2">
        {dummyData.map((item) => (
          <li key={item._id} className="flex justify-between text-black">
            <span>{item._id}</span>
            <span>{item.totalActiveSubscribers}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default TotalSubscribers;
