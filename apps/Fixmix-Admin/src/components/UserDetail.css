/* Container for user details */
.user-detail-container {
  margin-left: 1rem;
  margin-top: 0.5rem;
  padding: 1rem;
  position: relative;
  max-width: 24in;
}

/* Breadcrumb style */
.breadcrumb {
  font-family: "Poppins", sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px;
  line-height: 24px;
  color: #9d9d9d;
}

.breadcrumb-separator {
  margin-left: -1rem;
  width: calc(100% + 2rem);
}

/* Back button style */
.back-button {
  position: absolute;
  width: 14px;
  height: 14px;
  left: 20px;
  top: 25px;
  cursor: pointer;
  background: #ffffff;
}

/* User image style */
.user-image {
  width: 255px;
  height: 255px;
  background-size: cover;
  border-radius: 50%;
  background: url(../assets/userIcon.webp);
  background-position: center;
}

/* Common style for all labels */
.user-info label {
  display: block;
  margin-top: 20px;
  font-family: "Poppins", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 16px;
  color: #9d9d9d;
}

/* Common style for all inputs */
.user-info input,
.user-info textarea {
  display: block;
  width: 100%;
  height: 60px;
  margin-top: 10px;
  padding: 10px;
  /* background: #2c2e2d; */
  border-radius: 10px;
  font-family: "Poppins", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 16px;
  /* color: #ffffff; */
  /* border: none; */
  resize: none;
}

/* Specific styles for textarea */
.user-info textarea {
  height: 100px;
}

/* Metrics section style */
.user-metrics {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

/* Metrics card style */
.user-metrics .metric-card {
  width: 245px;
  height: 120px;
  background: #2c2e2d;
  box-shadow: 2px 2px 8.3px -5px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* Total text in metrics card */
.user-metrics .metric-card .metric-title {
  font-family: "Poppins", sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #9d9d9d;
}

/* Metric value in metrics card */
.user-metrics .metric-card .metric-value {
  font-family: "Inter", sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 40px;
  line-height: 48px;
  color: #34a853;
}

/* Responsive adjustments */
@media (max-width: 1200px) {

  .breadcrumb,
  .breadcrumb-separator,
  .back-button,
  .user-image,
  .user-info input,
  .user-info textarea {
    left: 20px;
    width: calc(100% - 40px);
  }

  .user-image {
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
  }

  .user-info input,
  .user-info textarea {
    width: calc(100% - 40px);
  }

  .user-metrics {
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }

  .user-metrics .metric-card {
    width: calc(100% - 40px);
  }
}

@media (max-width: 768px) {
  .user-detail-container {
    margin-left: 0;
  }

  .breadcrumb,
  .breadcrumb-separator,
  .back-button,
  .user-image,
  .user-info input,
  .user-info textarea,
  .user-metrics .metric-card {
    width: calc(100% - 20px);
    left: 10px;
  }
}