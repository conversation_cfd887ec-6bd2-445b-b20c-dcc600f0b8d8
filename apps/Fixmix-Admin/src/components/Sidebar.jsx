import React, { useState, useEffect } from "react";
import { NavLink, useNavigate, useNavigation } from "react-router-dom";
import { useDispatch } from "react-redux";
import { logout } from "../slices/authSlice";
import { ReactComponent as DashboardIcon } from "../assets/icons/dashboard.svg";
import { ReactComponent as UsersIcon } from "../assets/icons/users.svg";
import { ReactComponent as VendorsIcon } from "../assets/icons/vendors.svg";
import { ReactComponent as PayoutIcon } from "../assets/icons/payout.svg";
import { ReactComponent as NotificationsIcon } from "../assets/icons/notifications.svg";
import { ReactComponent as OrdersIcon } from "../assets/icons/order.svg";
import { ReactComponent as CategoryIcon } from "../assets/icons/orders.svg";
import { ReactComponent as QueriesIcon } from "../assets/icons/query.svg";
import { ReactComponent as OffersIcon } from "../assets/icons/offers.svg";
import { ReactComponent as ProfileIcon } from "../assets/icons/profile.svg";
import { ReactComponent as LogoutIcon } from "../assets/icons/logout.svg";
import { ReactComponent as Logo } from "../assets/FixMix App Screens/Fixmix User.svg";
import Swal from "sweetalert2"; // Import SweetAlert
import menuIcon from './../assets/icons/menuIcon.svg'

const Sidebar = () => {
  const [isOpen, setIsOpen] = useState(window.innerWidth >= 768);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleResize = () => {
    if (window.innerWidth >= 768) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  };

  const handleLogout = async () => {
    try {
      Swal.fire({
        title: "Are you sure ?",
        text: "Press on Ok if you want to Logout.",
        showCancelButton: true,

        cancelButtonText: "Cancle",
        icon: "question",
        confirmButtonText: "Ok",
      }).then(async (result) => {
        navigate("/login");
        if (result.isConfirmed) {
          await dispatch(logout()).unwrap();
          console.log("Logout successful");
        }
      });
    } catch (err) {
      console.error("Logout failed:", err);
    }
  };

  useEffect(() => {
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  const handleLogoClick = () => {
    navigate("/"); // Navigate to the home page
  };
  return (
    <>
      <div className={`flex items-center justify-between p-4 absolute  ${isOpen ? "left-40 top-4" : ""} `}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="text-white bg-primary px-3 py-2 rounded-xl focus:outline-none"
        >
          {isOpen ? (
            "Close"
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" width="35px" height="35px" fill="white">
                <path d="M 5 8 A 2.0002 2.0002 0 1 0 5 12 L 45 12 A 2.0002 2.0002 0 1 0 45 8 L 5 8 z M 5 23 A 2.0002 2.0002 0 1 0 5 27 L 45 27 A 2.0002 2.0002 0 1 0 45 23 L 5 23 z M 5 38 A 2.0002 2.0002 0 1 0 5 42 L 45 42 A 2.0002 2.0002 0 1 0 45 38 L 5 38 z" />
              </svg>
            </>
          )}
        </button>
        {/* <Logo onClick={handleLogoClick} className="w-8 h-8" /> */}
      </div>
      <div
        className={`min-h-screen
          ${isOpen ? "w-64" : "w-0"}
          bg-secondary transition-width duration-300 `}
      >
        {isOpen && (
          <div className="p-6 flex flex-col justify-between h-full">
            <nav className="mt-1">
              <Logo onClick={handleLogoClick} className="w-20 h-20 mb-6" />
              <NavLink
                to="/"
                className={({ isActive }) =>
                  `flex text-base lg:text-lg items-center p-4 mb-4 rounded-lg ${isActive ? "text-white font-semibold selected-option-bg" : "text-black hover:bg-gray-300"
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    <DashboardIcon
                      className={`w-6 h-6 mr-3 ${isActive ? "fill-white" : "fill-black"}`}
                    />
                    <span>Dashboard</span>
                  </>
                )}
              </NavLink>
              <NavLink
                to="/users"
                className={({ isActive }) =>
                  `flex text-base lg:text-lg items-center p-4 mb-4 rounded-lg ${isActive ? "text-white font-semibold selected-option-bg" : "text-black hover:bg-gray-300"
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    <UsersIcon className={`w-6 h-6 mr-3 ${isActive ? "fill-white" : "fill-black"}`} />
                    <span>Users</span>
                  </>
                )}
              </NavLink>
              <NavLink
                to="/vendors"
                className={({ isActive }) =>
                  `flex text-base lg:text-lg items-center p-4 mb-4 rounded-lg ${isActive ? "text-white font-semibold selected-option-bg" : "text-black hover:bg-gray-300"
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    <VendorsIcon className={`w-6 h-6 mr-3 ${isActive ? "fill-white" : "fill-black"}`} />
                    <span>Vendors</span>
                  </>
                )}
              </NavLink>
              <NavLink
                to="/queries"
                className={({ isActive }) =>
                  `flex text-base lg:text-lg items-center p-4 mb-4 rounded-lg ${isActive ? "text-white font-semibold selected-option-bg" : "text-black hover:bg-gray-300"
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    <QueriesIcon className={`w-6 h-6 mr-3 ${isActive ? "fill-white" : "fill-black"}`} />
                    <span>Queries</span>
                  </>
                )}
              </NavLink>
              <NavLink
                to="/offers"
                className={({ isActive }) =>
                  `flex text-base lg:text-lg items-center p-4 mb-4 rounded-lg ${isActive ? "text-white font-semibold selected-option-bg" : "text-black hover:bg-gray-300"
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    <OffersIcon className={`w-6 h-6 mr-3 ${isActive ? "fill-white" : "fill-black"}`} />
                    <span>Offers</span>
                  </>
                )}
              </NavLink>
              <NavLink
                to="/orders"
                className={({ isActive }) =>
                  `flex text-base lg:text-lg items-center p-4 mb-4 rounded-lg ${isActive ? "text-white font-semibold selected-option-bg" : "text-black hover:bg-gray-300"
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    <OrdersIcon className={`w-6 h-6 mr-3 ${isActive ? "fill-white" : "fill-black"}`} />
                    <span>Orders</span>
                  </>
                )}
              </NavLink>
              <NavLink
                to="/Category"
                className={({ isActive }) =>
                  `flex text-base lg:text-lg items-center p-4 mb-4 rounded-lg ${isActive ? "text-white font-semibold selected-option-bg" : "text-black hover:bg-gray-300"
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    <CategoryIcon className={`w-6 h-6 mr-3 ${isActive ? "fill-white" : "fill-black"}`} />
                    <span>Category</span>
                  </>
                )}
              </NavLink>
              <NavLink
                to="/transactions"
                className={({ isActive }) =>
                  `flex text-base lg:text-lg items-center p-4 mb-4 rounded-lg ${isActive ? "text-white font-semibold selected-option-bg" : "text-black hover:bg-gray-300"
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    <PayoutIcon className={`w-6 h-6 mr-3 ${isActive ? "fill-white" : "fill-black"}`} />
                    <span>Transaction</span>
                  </>
                )}
              </NavLink>
              <NavLink
                to="/notifications"
                className={({ isActive }) =>
                  `flex text-base lg:text-lg items-center p-4 mb-4 rounded-lg ${isActive ? "text-white font-semibold selected-option-bg" : "text-black hover:bg-gray-300"
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    <NotificationsIcon className={`w-6 h-6 mr-3 ${isActive ? "fill-white" : "fill-black"}`} />
                    <span>Notifications</span>
                  </>
                )}
              </NavLink>

              <NavLink
                to="/profile"
                className={({ isActive }) =>
                  `flex text-base lg:text-lg items-center p-4 mb-4 rounded-lg ${isActive ? "text-white font-semibold selected-option-bg" : "text-black hover:bg-gray-300"
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    <ProfileIcon className={`w-6 h-6 mr-3 ${isActive ? "fill-white" : "fill-black"}`} />
                    <span>My Profile</span>
                  </>
                )}
              </NavLink>
              {/* <NavLink
                to="/setting"
                className={({ isActive }) =>
                                    `flex text-base lg:text-lg items-center p-4 mb-4 rounded-lg ${isActive ? "text-white font-semibold selected-option-bg" : "text-black hover:bg-gray-300"
                   }`
                }
              >
                {({ isActive }) => (
                  <>
                <ProfileIcon className={`w-6 h-6 mr-3 ${isActive ? "fill-white" : "fill-black"}`} />
                <span>Setting</span>
                  </>
                )}
              </NavLink> */}
            </nav>
            <div className=" bottom-0 w-max mb-6">
              <button
                onClick={handleLogout}
                className="flex items-center p-4 text-black hover:bg-gray-300 rounded-lg"
              >
                <LogoutIcon className="w-6 h-6 mr-3 text-black" fill="black" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default Sidebar;
