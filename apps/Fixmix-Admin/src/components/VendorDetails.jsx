import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import userIcon from "../assets/userIcon.webp";
import "./VendorDetails.css";
import { BASE_URL, fetchVendorAnalytics } from "./../services/api";
import { fetchVendorById, fetchVendors } from "../slices/vendorSlice";

const VendorDetail = () => {
  const { vendorId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [analytics, setAnalytics] = useState(null);
  const { selectedVendor: vendors, loading, error } = useSelector((state) => state.vendors);
  useEffect(() => {
    if (vendorId) {
      dispatch(fetchVendorById(vendorId)); // Fetch user when component mounts
    }
  }, [dispatch, vendorId]);
  if (loading) return <div className="text-white">Loading user details...</div>;
  if (error) return <div className="text-white">Error: {error}</div>;
  if (!vendors) return <div className="text-white">User not found</div>;

  // const businessAddress = vendor?.businessAddress || {};
  // const {
  //   addressLine1 = "",
  //   addressLine2 = "",
  //   city = "",
  //   state = "",
  //   country = "",
  //   postalCode = "",
  // } = businessAddress;

  // const addresses = addressLine1
  //   ? `${addressLine1} ${addressLine2 ? ", " + addressLine2 : ""}${city ? ", " + city : ""
  //   }${state ? ", " + state : ""}${country ? ", " + country : ""}${postalCode ? ", " + postalCode : ""
  //   }`
  //   : "address not found";

  const profileImageUrl = vendors?.profilePhoto
    ? `${vendors.profilePhoto}`
    : userIcon;


  return (
    <div className="vendor-detail-container mx-auto max-w-6xl p-6">
      <div className="breadcrumb text-black text-sm mb-4 flex items-center">
        <span className="cursor-pointer mr-2" onClick={() => navigate(-1)}>
          &larr;
          <span onClick={() => console.log(vendors)} className="text-black">Vendor Details</span>
        </span>
      </div>
      <div className="breadcrumb-separator border-t border-gray-700 mb-6"></div>
      <div className="vendor-detail-grid grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 vendor-info space-y-6">
          <div>
            <label className="block text-black text-sm">Full Name</label>
            <input
              type="text"
              value={vendors?.name ?? "Vendor Name"}
              readOnly
              className="text-black border border-black/25"
            />
          </div>
          <div>
            <label className="block text-black text-sm">Email ID</label>
            <input
              type="email"
              value={vendors?.email ?? "Email"}
              readOnly
              className="text-black border border-black/25"
            />
          </div>
          <div>
            <label className="block text-black text-sm">Mobile No.</label>
            <input
              type="text"
              value={vendors?.phone ?? "Phone number"}
              readOnly
              className="text-black border border-black/25"
            />
          </div>
        </div>
        <div className="vendor-image-wrapper flex justify-center items-center">
          <div
            className="vendor-image w-full h-64 rounded-full bg-cover bg-center"
            style={{ backgroundImage: `url(${profileImageUrl})` }}
          ></div>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <div>
          <label className="block text-black text-sm">Company Name</label>
          <input
            type="text"
            value={vendors?.companyName ?? "Business Name"}
            readOnly
            className="text-black border border-black/25"
          />
        </div>
        <div>
          <label className="block text-black text-sm">
            Product Category
          </label>
          <input
            type="text"
            value={vendors?.category?.name}
            readOnly
            className="text-black border border-black/25"
          />
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <div>
          <label className="block text-black text-sm">Total Earning</label>
          <input
            type="text"
            value={vendors?.totalEarnings ?? "Business Name"}
            readOnly
            className="text-black border border-black/25"
          />
        </div>
        <div>
          <label className="block text-black text-sm">
            Total Order
          </label>
          <input
            type="text"
            value={vendors?.totalCompletedOrders}
            readOnly
            className="text-black border border-black/25"
          />
        </div>
      </div>
      <div className="vendor-info space-y-6 mt-6">
        {/* <div>
          <label className="block text-black text-sm">Password</label>
          <input
            type="text"
            value={
              vendor?.password && vendor?.password.length > 6
                ? `${vendor?.password.substring(
                    0,
                    3
                  )}...${vendor?.password.substring(
                    vendor?.password.length - 3
                  )}`
                : vendor?.password ?? "N/A"
            }
            readOnly
            className="vendor-input"
          />
        </div> */}
        {/* <div>
          <label className="block text-black text-sm">Address</label>
          <textarea
            value={addresses ?? "Address not found"}
            readOnly
            className="vendor-input h-28"
          ></textarea>
        </div> */}
        {/* <div>
          <label className="block text-black text-sm">
            Average Selling Price
          </label>
          <input
            type="text"
            value={analytics?.averageSellingPrice.toFixed(0) ?? "Not Found"}
            readOnly
            className="vendor-input"
          />
        </div> */}
      </div>
      {/* <div className="vendor-metrics mt-10 flex flex-col md:flex-row gap-6">
        <div className="metric-card">
          <div className="metric-title">Total</div>
          <div className="metric-name">Earning</div>
          <div className="metric-value text-green-500">
            <span className="font-normal">€</span>
            {analytics?.totalEarnings ?? 0}
          </div>
        </div>
        <div className="metric-card">
          <div className="metric-title">Total</div>
          <div className="metric-name">Sub Categories</div>
          <div className="metric-value text-green-500">
            {analytics?.totalSubcategories ?? 0}
          </div>
        </div>
        <div className="metric-card">
          <div className="metric-title">Total</div>
          <div className="metric-name">Ratings</div>
          <div className="metric-value text-yellow-500 flex items-center">
            {analytics?.totalRatings ?? 0}
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default VendorDetail;
