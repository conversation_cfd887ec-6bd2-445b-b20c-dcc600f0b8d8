import React from "react";
import { Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import adminProfile from "./../assets/userIcon.webp";
import { BASE_URL } from "../services/api";
import { Link } from "react-router-dom";

const adminemail = localStorage.getItem("email");
const adminImage = localStorage.getItem("profileImage");

const TopBar = ({ onSearch, searchPlaceholder, onSearchChange, isSearchEnable }) => (
  <div className="flex flex-col sm:flex-row gap-4 justify-between items-center w-full px-5 pt-16 md:pt-4">
    {!isSearchEnable ? (
      <div
        className="flex items-center bg-secondary py-1 w-[50%] rounded-2xl shadow-lg ml-20"
        style={{}}
      >
        <SearchOutlined className="text-black/50 ml-6 mr-2" />
        <Input
          placeholder={searchPlaceholder}
          onChange={onSearch ? onSearch : onSearchChange}
          className="bg-secondary text-black/50 placeholder-black/50 border-0 mr-4 outline-none text-lg focus:ring-0 mt-2"
          style={{
            // backgroundColor: "#2C2E2D",
            paddingLeft: "0.5rem",
            // width: "219px",
            // height: "32px",
          }}
        />
      </div>
    ) : (
      <div className="flex items-center py-1 w-[50%]  rounded-2xl shadow-lg ml-5"></div>
    )}

    <Link to={"/profile"}>
      <div className="flex items-center">
        <div className="mr-4 text-right ">
          <div className="text-black font-medium">{adminemail}</div>
          <div className="text-black/50">Admin</div>
        </div>
        <div className="w-12 h-12 border-2 border-[#0096c7] rounded-full overflow-hidden">
          <img
            src={adminImage ? adminImage : adminProfile}
            alt="Admin"
            className="w-full h-full object-cover"
          />
        </div>
      </div>
    </Link>
  </div>
);

export default TopBar;
