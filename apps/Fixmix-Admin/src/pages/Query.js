import React, { useEffect, useState } from 'react';
import TopBar from '../components/TopBar';
import DashboardMetrics from '../components/DashboardMetrics';
import noDataImage from "./../assets/icons/nodataimage.svg";
import { Button } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { fetchQueries, setSearchText } from '../slices/querySlice';

const Query = () => {
    const dispatch = useDispatch();
    const {
        query,
        isLoading,
        error,
        totalQueries,
        totalPages,
        currentPage: currentPageFromStore,
        limit: limitFromStore,
        searchText: searchTextFromStore, // Get searchText from store
    } = useSelector((state) => state.query);

    // State variables
    const [filter, setFilter] = useState("all");
    const [limit, setLimit] = useState(10); // Default limit is 10
    const [dateFilter, setDateFilter] = useState("all");
    const [currentPage, setCurrentPage] = useState(1);
    const [searchText, setSearchTextLocal] = useState(searchTextFromStore || "");
    const [sortConfig, setSortConfig] = useState({
        key: "createdAt",
        direction: "asc",
    });

    // Effect to fetch data when any parameter changes
    useEffect(() => {
        const params = {
            page: currentPage,
            limit,
            status: filter !== "all" ? filter : undefined,
            search: searchText,
            sortBy: sortConfig.key,
            order: sortConfig.direction,
            ...getDateRangeParams(dateFilter)
        };
        dispatch(fetchQueries(params));
    }, [currentPage, filter, limit, dateFilter, searchText, sortConfig, dispatch]);

    // Shimmer loading effect
    const renderShimmer = () => {
        return Array.from({ length: 10 }).map((_, index) => (
            <tr key={index} className="shimmer-row">
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer shimmer-circle"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
            </tr>
        ));
    };

    // Add this CSS for shimmer effect
    const shimmerStyles = `
        .shimmer-row {
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
            background-size: 800px 100%;
        }
        
        .shimmer {
            height: 20px;
            background: #f0f0f0;
            border-radius: 4px;
            margin: 4px 0;
        }
        
        .shimmer-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }
        
        @keyframes shimmer {
            0% { background-position: -400px 0; }
            100% { background-position: 400px 0; }
        }
    `;

    // Add styles to document head
    const styleSheet = document.createElement('style');
    styleSheet.type = 'text/css';
    styleSheet.innerText = shimmerStyles;
    document.head.appendChild(styleSheet);

    // Function to handle filter change
    const handleFilterChange = (newFilter) => {
        setFilter(newFilter); // Update the filter
        setCurrentPage(1); // Reset page to 1 whenever filter changes
    };

    const getDateRangeParams = (range) => {
        const now = moment();
        switch (range) {
            case 'today':
                return {
                    startDate: now.startOf('day').toISOString(),
                    endDate: now.endOf('day').toISOString()
                };
            case 'week':
                return {
                    startDate: now.startOf('week').toISOString(),
                    endDate: now.endOf('week').toISOString()
                };
            case 'month':
                return {
                    startDate: now.startOf('month').toISOString(),
                    endDate: now.endOf('month').toISOString()
                };
            case 'year':
                return {
                    startDate: now.startOf('year').toISOString(),
                    endDate: now.endOf('year').toISOString()
                };
            default:
                return {};
        }
    };

    const sortUsers = (key) => {
        const direction = sortConfig.key === key && sortConfig.direction === "asc" ? "desc" : "asc";
        setSortConfig({ key, direction });
    };

    const statusCounts = query?.reduce((acc, user) => {
        acc[user.status] = (acc[user.status] || 0) + 1;
        return acc;
    }, {});

    const completedCount = statusCounts?.completed || 0;
    const acceptedCount = statusCounts?.accepted || 0;
    const closedCount = statusCounts?.closed || 0;
    const openCount = statusCounts?.open || 0;

    const handleDownloadCSV = () => {
        const headers = ["No.", "Full Name", "Mobile", "Date", "Query Title", "Status"];
        const csvRows = [headers.join(",")];

        query.forEach((user, index) => {
            const row = [
                index + 1,
                user.userId.name || "N/A",
                user.userId.phone || "N/A",
                moment(user.createdAt).format("DD-MM-YY"),
                user.title || "N/A",
                user.status || "N/A"
            ];
            csvRows.push(row.join(","));
        });

        const csvData = csvRows.join("\n");
        const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });
        const url = URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "queries_report.csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handlePreviousPage = () => {
        if (currentPage > 1) setCurrentPage(currentPage - 1);
    };

    const handleNextPage = () => {
        if (currentPage < totalPages) setCurrentPage(currentPage + 1);
    };

    // Function to handle search input change
    const handleSearchChange = (e) => {
        const value = e.target.value;
        setCurrentPage(1); // Reset page to 1 whenever filter changes
        setSearchTextLocal(value);
        dispatch(setSearchText(value)); // Update Redux store
    };

    return (
        <>
            <TopBar
                searchPlaceholder="Search Query Here"
                isSearchEnable={false}
                onSearch={handleSearchChange}
            />
            <div className="p-6">
                <div className="users-container">
                    <div className="metrics-section">
                        <DashboardMetrics title="Total Queries" count={totalQueries} />
                        <DashboardMetrics title="Total Completed Queries" count={completedCount} />
                        <DashboardMetrics title="Total Open Queries" count={openCount} />
                        <DashboardMetrics title="Total Close Queries" count={closedCount} />
                    </div>

                    <div className="">
                        <div className="flex justify-between gap-4">
                            <div className='flex items-center justify-center gap-4'>
                                <div className="users-dropdown">
                                    <button className="users-dropdown-button shadow-sm clickable">
                                        {filter === "all" ? "Query Status" : filter}
                                        <div className="users-dropdown-content">
                                            <a href="#" className={filter === "all" ? "active" : ""} onClick={() => handleFilterChange("all")}>
                                                All Queries
                                            </a>
                                            <a href="#" className={filter === "open" ? "active" : ""} onClick={() => handleFilterChange("open")}>
                                                Open Queries
                                            </a>
                                            <a href="#" className={filter === "closed" ? "active" : ""} onClick={() => handleFilterChange("closed")}>
                                                Closed Queries
                                            </a>
                                            <a href="#" className={filter === "accepted" ? "active" : ""} onClick={() => handleFilterChange("accepted")}>
                                                Accepted Queries
                                            </a>
                                            <a href="#" className={filter === "completed" ? "active" : ""} onClick={() => handleFilterChange("completed")}>
                                                Completed Queries
                                            </a>
                                        </div>
                                    </button>
                                </div>
                                <div className="users-dropdown">
                                    <button className="users-dropdown-button">
                                        {dateFilter === "all" ? "Date Filter" :
                                            dateFilter === "today" ? "Today" :
                                                dateFilter === "week" ? "This Week" :
                                                    dateFilter === "month" ? "This Month" : "This Year"}
                                        <div className="users-dropdown-content">
                                            <a href="#" className={dateFilter === "all" ? "active" : ""} onClick={() => setDateFilter("all")}>
                                                All Dates
                                            </a>
                                            <a href="#" className={dateFilter === "today" ? "active" : ""} onClick={() => setDateFilter("today")}>
                                                Today
                                            </a>
                                            <a href="#" className={dateFilter === "week" ? "active" : ""} onClick={() => setDateFilter("week")}>
                                                This Week
                                            </a>
                                            <a href="#" className={dateFilter === "month" ? "active" : ""} onClick={() => setDateFilter("month")}>
                                                This Month
                                            </a>
                                            <a href="#" className={dateFilter === "year" ? "active" : ""} onClick={() => setDateFilter("year")}>
                                                This Year
                                            </a>
                                        </div>
                                    </button>
                                </div>
                            </div>
                            <div className='flex text-black'>
                                <label htmlFor="limit" className='text-black w-40 flex items-center justify-center'>Items per page: </label>
                                <select
                                    id="limit"
                                    className='w-12'
                                    value={limit}
                                    onChange={(e) => setLimit(Number(e.target.value))}
                                >
                                    <option value={10}>10</option>
                                    <option value={20}>20</option>
                                    <option value={50}>50</option>
                                </select>

                                {/* Your existing UI components here */}
                            </div>
                        </div>
                    </div>

                    {isLoading ? (
                        <div className="user-table-container">
                            <table className="user-table">
                                <thead>
                                    {/* Table headers */}
                                    <tr>
                                        <th className="header-cell">No.</th>
                                        <th className="header-cell">Photo</th>
                                        <th className="header-cell clickable" onClick={() => sortUsers("name")}>
                                            <div className="clickable-header">
                                                Full Name {sortConfig.key === "name" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        <th className="header-cell clickable" onClick={() => sortUsers("phone")}>
                                            <div className="clickable-header">
                                                Mobile {sortConfig.key === "phone" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        <th className="header-cell clickable" onClick={() => sortUsers("createdAt")}>
                                            <div className="clickable-header">
                                                Date {sortConfig.key === "createdAt" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        <th className="header-cell">Query</th>
                                        <th className="header-cell">Add on services</th>
                                        <th className="header-cell">Status</th>
                                        <th className="header-cell">More</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {renderShimmer()}
                                </tbody>
                            </table>
                        </div>
                    ) : error ? (
                        <div className="error-message">{error}</div>
                    ) : query.length > 0 ? (
                        <div className="user-table-container">
                            <table className="user-table">
                                <thead>
                                    <tr>
                                        <th className="header-cell">No.</th>
                                        <th className="header-cell">Photo</th>
                                        <th className="header-cell clickable" onClick={() => sortUsers("name")}>
                                            <div className="clickable-header">
                                                Full Name {sortConfig.key === "name" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        <th className="header-cell clickable" onClick={() => sortUsers("phone")}>
                                            <div className="clickable-header">
                                                Mobile {sortConfig.key === "phone" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        <th className="header-cell clickable" onClick={() => sortUsers("createdAt")}>
                                            <div className="clickable-header">
                                                Date {sortConfig.key === "createdAt" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        <th className="header-cell">Query</th>
                                        <th className="header-cell">Add on services</th>
                                        <th className="header-cell">Status</th>
                                        <th className="header-cell">More</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {query.map((user, index) => (
                                        <tr key={user._id}>
                                            <td className="body-cell clickable">{(currentPage - 1) * limit + index + 1}</td>
                                            <td className="body-cell">
                                                <img src={user.userId?.profilePhoto} alt="User Profile" className="w-10 h-10 rounded-full" />
                                            </td>
                                            <td className="body-cell">{user.userId?.name}</td>
                                            <td className="body-cell">{user.userId?.phone}</td>
                                            <td className="body-cell">{moment(user.createdAt).format("DD-MM-YY")}</td>
                                            <td className="body-cell">
                                                <div className='font-semibold'>{user.title}</div>
                                                {user.description}
                                            </td>
                                            <td className="body-cell">{user?.addOnServices.length}</td>
                                            <td className="body-cell">{user?.status}</td>
                                            <td className="body-cell">
                                                <Link to={`/queries/${user._id}`} className="more-link clickable">
                                                    More..
                                                </Link>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>

                            <div className="pagination-container mt-4 mx-auto justify-between px-1 text-black">
                                <button onClick={handlePreviousPage} disabled={currentPage === 1}>
                                    &lt; Previous
                                </button>
                                <span className='text-black'>Page {currentPage} of {totalPages}</span>
                                <button onClick={handleNextPage} disabled={currentPage === totalPages}>
                                    Next &gt;
                                </button>
                            </div>
                            {/* <div className="download-report">
                                <button className="download-link" onClick={handleDownloadCSV}>
                                    Download Report
                                </button>
                            </div> */}
                        </div>
                    ) : (
                        <div className="no-data-section">
                            <img src={noDataImage} alt="No data" className="no-data-image" />
                            <div className="no-data-title">No Query to show right now!</div>
                            <div className="no-data-description">
                                We don't have any query info to show here. Please add query first
                                and then check their details here.
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

export default Query;