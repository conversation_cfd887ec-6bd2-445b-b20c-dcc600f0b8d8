import React, { useEffect, useState } from 'react'
import TopBar from '../components/TopBar'
import UserModel from '../components/CreateUserModel'
import DashboardMetrics from '../components/DashboardMetrics';
import noDataImage from "./../assets/icons/nodataimage.svg";
import { Button, Switch } from 'antd';
import Swal from 'sweetalert2';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { fetchQueries, setUsers } from '../slices/querySlice';
import { getAllQueries } from '../services/api';
const Query = () => {
    // const [users, setUsers] = useState(dummyUsers);
    const [filter, setFilter] = useState("all");
    const [dateFilter, setDateFilter] = useState("all");
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const dispatch = useDispatch();
    const { users, isLoading, error } = useSelector((state) => state.query);
    const [sortConfig, setSortConfig] = useState({
        key: "createdAt",
        direction: "asc",
    });
    const itemsPerPage = 10;
    const [searchText, setSearchText] = useState('');
    const convertToCSV = (data) => {
        const headers = [
            "No.",
            "Full Name",
            "Email ID",
            "Date",
            "User Type",
            "Status",
        ];

        const csvRows = [];
        csvRows.push(headers.join(","));

        data.forEach((user, index) => {
            const row = [
                index + 1, // Serial No.
                user.name || "N/A", // Full Name
                user.email || "N/A", // Email ID
                moment(user.createdAt).format("DD-MM-YY"), // Date
                user.password ? "Email" : "Google User", // User Type
                user.status, // Status (active/deactive)
            ];
            csvRows.push(row.join(","));
        });

        return csvRows.join("\n");
    };

    useEffect(() => {
        dispatch(fetchQueries());
    }, [dispatch]);

    // Sorting functionality
    const sortUsers = (key) => {
        let direction = "asc";
        if (sortConfig.key === key && sortConfig.direction === "asc") {
            direction = "desc";
        }
        const sortedUsers = [...users].sort((a, b) => {
            if (key === "name" || key === "email" || key === "createdAt") {
                return direction === "asc"
                    ? a[key].localeCompare(b[key])
                    : b[key].localeCompare(a[key]);
            }
            return direction === "asc" ? a[key] - b[key] : b[key] - a[key];
        });
        // setUsers(sortedUsers);
        setSortConfig({ key, direction });
    };
    const toggleModal = () => {
        setIsModalVisible(!isModalVisible);
    };

    const statusCounts = users?.reduce((acc, user) => {
        acc[user.status] = (acc[user.status] || 0) + 1;
        return acc;
    }, {});

    // Extracting counts for specific statuses
    const completedCount = statusCounts?.completed || 0;
    const acceptedCount = statusCounts?.accepted || 0;
    const closedCount = statusCounts?.closed || 0;
    const openCount = statusCounts?.open || 0;


    const handleDownloadCSV = () => {
        const csvData = convertToCSV(users); // Use the `users` state that contains the filtered data
        const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });
        const url = URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", `users_report.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // Pagination logic
    const totalPages = Math.ceil(users.length / itemsPerPage);
    const handlePreviousPage = () => {
        if (currentPage > 1) setCurrentPage(currentPage - 1);
    };

    const handleNextPage = () => {
        if (currentPage < totalPages) setCurrentPage(currentPage + 1);
    };

    const paginatedUsers = users.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

    const handleSearchChange = (event) => {
        setSearchText(event.target.value);
    };

    return (
        <>
            <TopBar searchPlaceholder="Search Users Here" isSearchEnable={true} />
            <div className="p-6 ">
                <div className="users-container">
                    <div className="metrics-section">
                        <DashboardMetrics title="Total Queries" count={users.length} />
                        <DashboardMetrics title="Total Completed Queries" count={completedCount} />
                        <DashboardMetrics title="Total Open Queries" count={openCount} />
                        <DashboardMetrics title="Total Close Queries" count={closedCount} />
                    </div>
                    <div className="search-create-section">
                        {/* Dropdown for Activation Status */}
                        <div className="dropdown-buttons">
                            <div className="users-dropdown">
                                <button className="users-dropdown-button shadow-sm clickable">
                                    {filter === "all"
                                        ? "Query Status"
                                        : filter === "active"
                                            ? "Activated Users"
                                            : "Deactivated Users"}
                                    <div className="users-dropdown-content">
                                        <a
                                            href="#"
                                            className={filter === "all" ? "active" : ""}
                                            onClick={() => setFilter("all")}
                                        >
                                            All Users
                                        </a>
                                        <a
                                            href="#"
                                            className={filter === "active" ? "active" : ""}
                                            onClick={() => setFilter("active")}
                                        >
                                            Activated Users
                                        </a>
                                        <a
                                            href="#"
                                            className={filter === "deactive" ? "active" : ""}
                                            onClick={() => setFilter("deactive")}
                                        >
                                            Deactivated Users
                                        </a>
                                    </div>
                                </button>
                            </div>
                            <div className="users-dropdown">
                                <button className="users-dropdown-button">
                                    {dateFilter === "all"
                                        ? "Date Filter"
                                        : dateFilter === "today"
                                            ? "Today"
                                            : dateFilter === "week"
                                                ? "This Week"
                                                : dateFilter === "month"
                                                    ? "This Month"
                                                    : "This Year"}
                                    <div className="users-dropdown-content">
                                        <a
                                            href="#"
                                            className={dateFilter === "all" ? "active" : ""}
                                            onClick={() => setDateFilter("all")}
                                        >
                                            All Dates
                                        </a>
                                        <a
                                            href="#"
                                            className={dateFilter === "today" ? "active" : ""}
                                            onClick={() => setDateFilter("today")}
                                        >
                                            Today
                                        </a>
                                        <a
                                            href="#"
                                            className={dateFilter === "week" ? "active" : ""}
                                            onClick={() => setDateFilter("week")}
                                        >
                                            This Week
                                        </a>
                                        <a
                                            href="#"
                                            className={dateFilter === "month" ? "active" : ""}
                                            onClick={() => setDateFilter("month")}
                                        >
                                            This Month
                                        </a>
                                        <a
                                            href="#"
                                            className={dateFilter === "year" ? "active" : ""}
                                            onClick={() => setDateFilter("year")}
                                        >
                                            This Year
                                        </a>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    {users.length > 0 ? (
                        <div className="user-table-container">
                            <table className="user-table">
                                <thead>
                                    <tr>
                                        <th className="header-cell">No.</th>
                                        <th className="header-cell">Photo</th>
                                        <th
                                            className="header-cell clickable"
                                            onClick={() => sortUsers("name")}
                                        >
                                            <div className="clickable-header">
                                                Full Name{" "}
                                                {sortConfig.key === "name" &&
                                                    (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        <th
                                            className="header-cell clickable"
                                            onClick={() => sortUsers("email")}
                                        >
                                            <div className="clickable-header">
                                                Mobile{" "}
                                                {sortConfig.key === "email" &&
                                                    (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        <th
                                            className="header-cell clickable"
                                            onClick={() => sortUsers("createdAt")}
                                        >
                                            <div className="clickable-header">
                                                Date{" "}
                                                {sortConfig.key === "createdAt"
                                                    ? sortConfig.direction === "asc"
                                                        ? "↑"
                                                        : "↓"
                                                    : sortConfig.key === null &&
                                                        sortConfig.direction === "asc"
                                                        ? "↑"
                                                        : ""}
                                            </div>
                                        </th>
                                        <th className="header-cell">Query</th>
                                        {/* <th className="header-cell">Activate/Deactivate</th> */}
                                        <th className="header-cell">More</th>
                                        {/* <th className="header-cell">Action</th> */}
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedUsers.map((user, index) => (
                                        <tr onClick={() => console.log(user)} key={user._id}>
                                            <td className="body-cell clickable">
                                                {(currentPage - 1) * itemsPerPage + index + 1}
                                            </td>
                                            <td className="body-cell">
                                                {user.userId.profilePhoto === "" ?
                                                    <>  {user.userId.profilePhoto} </> :
                                                    <>
                                                        <img src={user.userId.profilePhoto} alt="User Profile" className="w-10 h-10 rounded-full" />
                                                    </>
                                                }
                                            </td>
                                            <td className="body-cell">{user?.userId?.name}</td>
                                            <td className="body-cell">{user.userId.phone}</td>
                                            <td className="body-cell">
                                                {moment(user.createdAt).format("DD-MM-YY")}
                                            </td>
                                            <td className="body-cell">
                                                <div className='font-semibold'>
                                                    {user.title}
                                                </div>
                                                {user.description}
                                            </td>
                                            {/* <td className="body-cell">
                                                <Switch
                                                    checked={user.status === "active"}
                                                    onChange={() => handleSwitchChange(index)}
                                                    style={{
                                                        backgroundColor:
                                                            user.status === "active" ? "#0096c7" : "#798593",
                                                    }}
                                                />
                                            </td> */}
                                            <td className="body-cell">
                                                <Link
                                                    to={`/queries/${user._id}`}
                                                    className="more-link clickable"
                                                >
                                                    More..
                                                </Link>
                                            </td>
                                            {/* <td className="body-cell">
                                                <Link
                                                    onClick={() => handleDeleteUser(user._id)}
                                                    className="delete-link clickable"
                                                >
                                                    Delete..
                                                </Link>
                                            </td> */}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>

                            <div className="pagination-container mt-4 mx-auto justify-between px-1">
                                <button onClick={handlePreviousPage} disabled={currentPage === 1}>
                                    &lt; Previous
                                </button>
                                &nbsp;
                                <span className="">
                                    Page {currentPage} of {totalPages}
                                </span>
                                &nbsp;
                                <button
                                    onClick={handleNextPage}
                                    disabled={currentPage === totalPages}
                                >
                                    Next &gt;
                                </button>
                            </div>
                            {/* <div className="download-report">
                                <button className="download-link" onClick={handleDownloadCSV}>
                                    Download Report
                                </button>
                            </div> */}
                        </div>
                    ) : (
                        <div className="no-data-section">
                            <img src={noDataImage} alt="No data" className="no-data-image" />
                            <div className="no-data-title">No Query to show right now!</div>
                            <div className="no-data-description">
                                We don't have any query info to show here. Please add query first
                                and then check their details here.
                            </div>
                        </div>
                    )}

                    {/* {isModalVisible && (
                        <div className="modal-overlay" onClick={toggleModal}>
                            <div className="modal-container" onClick={(e) => e.stopPropagation()}>
                                <UserModel
                                    closeModal={toggleModal}
                                    updateUserData={updateUserData}
                                />
                            </div>
                        </div>
                    )} */}
                </div>
            </div>
        </>
    )
}

export default Query
