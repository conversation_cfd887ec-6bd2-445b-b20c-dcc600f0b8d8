/* Common styles for buttons */
.btn1,
.btn {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 650;
  border: none;
  border-radius: 20px;
  padding: 1.5rem 10.2rem;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.05);
  margin: 5px;
  flex: 1 1 auto;
  transition: background 0.3s ease-in-out;
}

.left-align-button {
  inset-area: y-start;
}

/* CSS to handle the truncation of message text */
.truncate-message1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* Limit to 2 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 3em;
  /* Adjust based on your font size */
  line-height: 1em;
  /* Adjust based on your font size */
}

/* button.btn1.de-btn {
  border-block: revert-layer;
}
button.btn.de-btn.ml-4 {
  border-block: revert-layer;
} */
.active-btn {
  background: linear-gradient(90deg, #f6d724 0%, #2c9547 100%);
}

.buttons-container {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.de-btn {
  border: 2px solid rgb(105, 105, 105);
  color: rgb(158, 157, 157);
}

/* Override background for hover effect */
.btn.bg-yellow-400:hover {
  background-color: #f59e0b;
}

.notifications-container {
  /* display: flex;
  flex-direction: column;
  align-items: center; */
  padding: 2rem;
  /* width: 100%;
  box-sizing: border-box; */
}

.search-buttons {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.no-notifications img {
  max-width: 100%;
  height: auto;
  margin-bottom: 20px;
}

.no-notifications h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.no-notifications p {
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 20px;
}

.notification-list {
  width: 100%;
  max-width: 800px;
}

.notification-item {
  background: #1f1f1f;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.notification-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 20px;
  flex: 0 0 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-content {
  flex: 1;
}

.notification-content h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.notification-content p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  font-size: 0.9rem;
}

/* Popup styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.popup-content {
  background: #1f1f1f;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 850px;
  height: 80%;
  max-height: 650px;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.popup-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 10px;
  align-self: flex-start;
}

.popup-subtitle {
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 20px;
  align-self: flex-start;
}

.popup-textarea {
  width: 100%;
  height: 50%;
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 10px;
  background: none;
  color: #ffffff;
  margin-bottom: 20px;
}

.close-btn {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 24px;
  cursor: pointer;
  position: absolute;
  top: 20px;
  right: 20px;
}

.popup-send-btn-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.popup-send-btn {
  background: linear-gradient(90deg, #0096c7 0%, #4bd1fd 100%);
  color: #ffffff;
  font-size: 1rem;
  font-weight: 500;
  border: none;
  border-radius: 50px;
  padding: 15px 50px;
  cursor: pointer;
}

.fab {
  background: linear-gradient(90deg, #0096c7 0%, #4bd1fd 100%);
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive styles */
@media (max-width: 600px) {

  .btn1,
  .btn {
    font-size: 0.9rem;
    padding: 0.5rem 1.5rem;
  }

  .notification-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .notification-item img {
    margin-bottom: 10px;
  }
}

@media (max-width: 1024px) {
  .popup-content {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
  }
}

@media (max-width: 768px) {
  .popup-content {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
  }

  .popup-textarea {
    height: 30%;
  }
}

/* Additional Responsive Styles */
@media (max-width: 768px) {

  .btn1,
  .btn {
    padding: 0.75rem 3rem;
  }

  .no-notifications img {
    width: 80%;
  }
}

@media (max-width: 480px) {

  .btn1,
  .btn {
    padding: 0.5rem 2rem;
    font-size: 0.8rem;
  }

  .popup-textarea {
    height: 25%;
  }

  .popup-title,
  .popup-subtitle {
    align-self: center;
  }
}

@media (max-width: 360px) {

  .btn1,
  .btn {
    padding: 0.4rem 1.5rem;
    font-size: 0.7rem;
  }
}