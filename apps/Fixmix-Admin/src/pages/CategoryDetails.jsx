import React, { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import userIcon from "../assets/userIcon.webp";
import { createNewSubCategory, fetchCategoryById } from '../slices/categorySlice';
import { Button, Input, Modal } from 'antd';
import { createSubcategory, updateImageCategory, updateImageSubCategory, uploadAdminImage } from '../services/api';
import Swal from 'sweetalert2';

const CategoryDetails = () => {
    const { categoryId } = useParams();
    const dispatch = useDispatch();
    const [itemsPerPage] = useState(10);
    const [searchText, setSearchText] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [categoryImages, setCategoryImages] = useState({});
    const fileInputRef = useRef(null); // Single ref for file input
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [newCategory, setNewCategory] = useState({ name: "", imageUrl: "", categoryId: categoryId });
    const { selectedUser: categories, loading, error } = useSelector((state) => state.categories);
    useEffect(() => {
        if (categoryId) {
            dispatch(fetchCategoryById(categoryId)); // Fetch user when component mounts
        }
    }, [dispatch, categoryId]);

    const handleImageUpload = async (event, categoryId) => {
        const file = event.target.files[0];
        if (!file) return;

        const imageUrl = await getImageUrl(file);
        if (!imageUrl) return;

        setCategoryImages((prev) => ({ ...prev, [categoryId]: imageUrl }));

        if (categoryId) {
            await updateImageCategory({ imageUrl, id: categoryId });
            window.location.reload();
        } else {
            setNewCategory((prev) => ({ ...prev, imageUrl }));
        }
    };

    const handleCreateCategory = async () => {
        if (!newCategory.name || !newCategory.imageUrl) {
            Swal.fire("Error", "Please provide a category name and image.", "error");
            return;
        }
        dispatch(createNewSubCategory(newCategory));
        setIsModalVisible(false);
        window.location.reload();
        setNewCategory({ name: "", imageUrl: "" });
    };

    const getImageUrl = async (file) => {
        try {
            const formData = new FormData();
            formData.append("image", file);
            const response = await uploadAdminImage(formData);
            return response.body.imageUrl;
        } catch (error) {
            console.error("Error uploading image:", error);
            return "";
        }
    };

    const handleAvatarClick = (event) => {
        event.stopPropagation(); // Prevent bubbling issues

        Swal.fire({
            title: "Are you sure?",
            text: "Do you want to change this category's image?",
            icon: "question",
            showCancelButton: true,
            confirmButtonText: "Yes, proceed!",
            cancelButtonText: "No, cancel!",
        }).then((result) => {
            if (result.isConfirmed) {
                // Open file upload dialog ONLY when the user clicks "Yes"
                if (fileInputRef.current) {
                    fileInputRef.current.click();
                }
            }
        });
    };
    if (loading) return <div className="text-white">Loading user details...</div>;
    if (error) return <div className="text-white">Error: {error}</div>;
    if (!categories) return <div className="text-white">User not found</div>;

    // Filter categories based on search text
    const filteredData = categories.filter((cat) =>
        cat.name.toLowerCase().includes(searchText.toLowerCase())
    );

    // Pagination logic
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = filteredData.slice(indexOfFirstItem, indexOfLastItem);

    // Pagination handlers
    const handlePreviousPage = () => {
        if (currentPage > 1) setCurrentPage(currentPage - 1);
    };

    const handleNextPage = () => {
        if (currentPage < Math.ceil(filteredData.length / itemsPerPage)) {
            setCurrentPage(currentPage + 1);
        }
    };

    return (
        <div className="user-detail-container mx-auto max-w-6xl pt-20">
            <div className="breadcrumb text-light-gray text-sm mb-4 flex items-center">
                <span
                    className="cursor-pointer mr-2"
                    onClick={() => window.history.back()}
                >
                    &larr;
                </span>
                <span className="ml-2 text-black" onClick={() => console.log(categories)}>Dashboard / Categories / SubCategories</span>
            </div>
            <div className="breadcrumb-separator border-t border-gray-700 mb-8"></div>
            <Button type="primary" onClick={() => setIsModalVisible(true)}>
                Create Subcategory
            </Button>
            <div className="category-table-container">
                <table className="category-table">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Sub Category</th>
                        </tr>
                    </thead>
                    <tbody>
                        {currentItems.map((cat) => (
                            <tr onClick={() => console.log(cat)} key={cat.id}>
                                <td onClick={handleAvatarClick}>
                                    <div
                                        className="circle-image"
                                        style={{
                                            backgroundImage: `url(${cat.imageUrl})`,
                                            width: "50px",
                                            height: "50px",
                                            borderRadius: "50%",
                                            backgroundSize: "cover",
                                            cursor: "pointer",
                                        }}
                                    />
                                    {/* Hidden File Input */}
                                    <input
                                        type="file"
                                        ref={fileInputRef}
                                        style={{ display: "none" }}
                                        accept="image/*"
                                        onChange={(event) => handleImageUpload(event, cat._id)}
                                    />
                                </td>
                                <td>{cat.name}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {/* Pagination */}
            <div className="pagination-container">
                <Button onClick={handlePreviousPage} disabled={currentPage === 1}>
                    Previous
                </Button>
                <span>
                    Page {currentPage} of {Math.ceil(filteredData.length / itemsPerPage)}
                </span>
                <Button
                    onClick={handleNextPage}
                    disabled={
                        currentPage === Math.ceil(filteredData.length / itemsPerPage)
                    }
                >
                    Next
                </Button>
            </div>
            <div className="user-metrics mt-10 grid grid-cols-3 gap-6">
                {/* <UserMetrics title="Total Orders" count={user.totalOrders} />
        <UserMetrics title="Total wishlist" count={user.wishlist.length} />
        <UserMetrics title="Total Spending" count={"€ " + user.totalSpending} /> */}
            </div>
            <Modal visible={isModalVisible} onCancel={() => setIsModalVisible(false)} onOk={handleCreateCategory}>
                <h2 className="text-black font-semibold text-lg py-4">Create Subcategory</h2>
                <Input placeholder="Subcategory Name" value={newCategory.name} className="text-black" onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })} />
                {newCategory.imageUrl && <img src={newCategory.imageUrl} alt="Uploaded Preview" className="image-preview w-20 h-20" />}
                <input type="file" ref={fileInputRef} style={{ display: "none" }} accept="image/*" onChange={handleImageUpload} />
                <Button onClick={() => fileInputRef.current.click()} className="text-black">Upload Image</Button>
            </Modal>
        </div>
    )
}

export default CategoryDetails
