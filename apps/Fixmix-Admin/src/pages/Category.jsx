import React, { useEffect, useRef, useState } from "react";
import { Button, Input, Spin, Modal } from "antd";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { createNewCategory, fetchCategories } from "../slices/categorySlice";
import TopBar from "./../components/TopBar";
import { ShimmerTable } from "shimmer-effects-react";
import "./Category.css";
import { updateImage, updateImageCategory, uploadAdminImage } from "../services/api";
import Swal from "sweetalert2";

const Category = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [searchText, setSearchText] = useState("");
  const fileInputRef = useRef(null); // Single ref for file input
  const [categoryImages, setCategoryImages] = useState({});
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [newCategory, setNewCategory] = useState({ name: "", imageUrl: "" });

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { data: categories, loading, error } = useSelector((state) => state.categories);

  // Fetch categories on component mount
  useEffect(() => {
    dispatch(fetchCategories());
  }, [dispatch]);

  // Filter categories based on search text
  const filteredData = (categories || []).filter((cat) =>
    (cat?.name || "").toLowerCase().includes(searchText.toLowerCase())
  );

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredData.slice(indexOfFirstItem, indexOfLastItem);

  // Pagination handlers
  const handlePreviousPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const handleNextPage = () => {
    if (currentPage < Math.ceil(filteredData.length / itemsPerPage)) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleAvatarClick = (event, categoryId) => {
    event.stopPropagation(); // Prevent event bubbling

    Swal.fire({
      title: "Are you sure?",
      text: "Do you want to change this category's image?",
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes, proceed!",
      cancelButtonText: "No, cancel!",
    }).then((result) => {
      if (result.isConfirmed) {
        // Open the correct file input for the specific category
        document.getElementById(`fileInput-${categoryId}`).click();
      }
    });
  };

  const getImageUrl = async (file) => {
    try {
      const formData = new FormData();
      formData.append("image", file);
      const response = await uploadAdminImage(formData);
      return response.body.imageUrl;
    } catch (error) {
      console.error("Error uploading image:", error);
      return "";
    }
  };

  const handleImageUpload = async (event, categoryId) => {
    const file = event.target.files[0];
    if (!file) return;

    const imageUrl = await getImageUrl(file);
    if (!imageUrl) return;

    setCategoryImages((prev) => ({ ...prev, [categoryId]: imageUrl }));

    if (categoryId) {
      await updateImageCategory({ imageUrl, id: categoryId });
      window.location.reload();
    } else {
      setNewCategory((prev) => ({ ...prev, imageUrl }));
    }
  };

  const handleCreateCategory = async () => {
    if (!newCategory.name || !newCategory.imageUrl) {
      Swal.fire("Error", "Please provide a category name and image.", "error");
      return;
    }
    dispatch(createNewCategory(newCategory));
    setIsModalVisible(false);
    window.location.reload();
    setNewCategory({ name: "", imageUrl: "" });
  };

  // Handle search input change
  const handleSearchChange = (event) => {
    setSearchText(event.target.value);
    setCurrentPage(1); // Reset to the first page when searching
  };

  // Navigate to More details (e.g., SubCategory page)
  const handleMoreClick = (category) => {
    navigate(`/category/${category._id}`, {
      // state: { subcategories: category.subcategories },
    });
  };

  // Show loading shimmer if data is being fetched
  if (loading) {
    return (
      <ShimmerTable
        mode="light"
        row={7}
        col={6}
        border={0}
        borderColor={"#cbd5e1"}
        rounded={0.25}
        rowGap={16}
        colPadding={[10, 5, 10, 5]}
      />
    );
  }

  // Show error message if there's an error
  if (error) {
    return <div className="error-message">Error: {error}</div>;
  }

  return (
    <>
      <TopBar
        searchPlaceholder="Search Categories Here"
        onSearchChange={handleSearchChange}
      />
      <div className="p-6">
        <div className="category-container">
          <div className="flex items-center justify-between">
            <h1 className="text-black font-semibold text-lg">Categories</h1>
            <Button type="primary" onClick={() => setIsModalVisible(true)}>
              Create Category
            </Button>
          </div>

          <div className="category-table-container">
            <table className="category-table">
              <thead>
                <tr>
                  <th>Image</th>
                  <th>Category</th>
                  <th>Sub-Category Count</th>
                  {/* <th>Created Date</th> */}
                  <th>More</th>
                </tr>
              </thead>
              <tbody>
                {currentItems.map((cat) => (
                  <tr key={cat._id}>
                    <td onClick={(event) => handleAvatarClick(event, cat._id)}>
                      <div
                        className="circle-image"
                        style={{
                          backgroundImage: `url(${categoryImages[cat._id] || cat.imageUrl})`,
                          width: "50px",
                          height: "50px",
                          borderRadius: "50%",
                          backgroundSize: "cover",
                          cursor: "pointer",
                        }}
                      />
                      {/* File input with unique ID per category */}
                      <input
                        type="file"
                        id={`fileInput-${cat._id}`}
                        style={{ display: "none" }}
                        accept="image/*"
                        onChange={(event) => handleImageUpload(event, cat._id)}
                      />
                    </td>
                    <td>{cat.name}</td>
                    <td>{cat.subCategoryCount}</td>
                    <td>
                      <button className="more-button" onClick={() => handleMoreClick(cat)}>
                        More
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="pagination-container">
            <Button onClick={handlePreviousPage} disabled={currentPage === 1}>
              Previous
            </Button>
            <span className="text-black">
              Page {currentPage} of {Math.ceil(filteredData.length / itemsPerPage)}
            </span>
            <Button
              onClick={handleNextPage}
              disabled={
                currentPage === Math.ceil(filteredData.length / itemsPerPage)
              }
            >
              Next
            </Button>
          </div>
          <Modal visible={isModalVisible} onCancel={() => setIsModalVisible(false)} onOk={handleCreateCategory}>
            <h2 className="text-black font-semibold text-lg py-4">Create Category</h2>
            <Input placeholder="Category Name" value={newCategory.name} className="text-black" onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })} />
            {newCategory.imageUrl && <img src={newCategory.imageUrl} alt="Uploaded Preview" className="image-preview w-20 h-20" />}
            <input type="file" ref={fileInputRef} style={{ display: "none" }} accept="image/*" onChange={handleImageUpload} />
            <Button onClick={() => fileInputRef.current.click()} className="text-black">Upload Image</Button>
          </Modal>
        </div>
      </div>
    </>
  );
};

export default Category;