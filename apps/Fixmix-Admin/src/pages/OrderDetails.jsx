import React, { useEffect } from "react";
import { Layout } from "antd";
import Sidebar from "../components/Sidebar";
import TopBar from "../components/TopBar";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { fetchOrderById } from "../slices/orderSlice";
import moment from "moment";

const { Content } = Layout;

const OrderDetails = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Get order details and loading status
  const { selectedOrder, isLoading, error } = useSelector((state) => state.orders);

  useEffect(() => {
    if (orderId) {
      dispatch(fetchOrderById(orderId));
    }
  }, [dispatch, orderId]);

  if (isLoading) return <p>Loading order details...</p>;
  if (error) return <p style={{ color: "red" }}>Error: {error}</p>;

  return (
    <>
      <TopBar />
      <div className="breadcrumb text-black text-sm mb-4 flex items-center p-8">
        <span className="cursor-pointer mr-2" onClick={() => navigate(-1)}>
          &larr; <span className="text-black">Order Details</span>
        </span>
      </div>

      {selectedOrder ? (
        <>
          <div className="flex items-center justify-start gap-2 px-8">
            <div className="block text-black text-sm lg:text-lg font-semibold">Transaction ID:</div>
            <div className="block text-black text-sm lg:text-lg">
              {selectedOrder?.transactionId || "N/A"}
            </div>
          </div>
          <div className="user-detail-grid  p-8  gap-6">
            <div className=" grid grid-cols-2 gap-4 w-full">
              <div className="col-span-1 ">
                <div className="block text-black text-sm lg:text-lg font-semibold">User</div>
                <div className="flex items-center gap-4">
                  <div><img src={selectedOrder?.userId?.profilePhoto} alt="User Photo" className="w-16 lg:w-24 h-16 lg:h-24 rounded-full" /></div>
                  <div className="block text-black text-sm lg:text-lg">
                    <p className="text-black text-base lg:text-lg">{selectedOrder?.userId?.name || "N/A"}</p>
                    <p className="text-black text-base lg:text-lg">{selectedOrder?.userId?.phone || "N/A"}</p>
                  </div>
                </div>
              </div>
              <div className="col-span-1 ">
                <div className="block text-black text-sm lg:text-lg font-semibold">Worker</div>
                <div className="flex items-center gap-4">
                  <div><img src={selectedOrder?.vendorId?.profilePhoto} alt="User Photo" className="w-16 lg:w-24 h-16 lg:h-24 rounded-full" /></div>
                  <div className="block text-black text-sm lg:text-lg">
                    <p className="text-black text-base lg:text-lg">{selectedOrder?.vendorId?.name || "N/A"}</p>
                    <p className="text-black text-base lg:text-lg">{selectedOrder?.vendorId?.phone || "N/A"}</p>
                  </div>
                </div>
              </div>
              <div className="col-span-1 ">
                <div className="block text-black text-sm lg:text-lg font-semibold">Query</div>
                <div className="flex items-center gap-4">
                  <div><img src={selectedOrder?.queryId?.photoUrl} alt="User Photo" className="w-24 lg:w-32 h-16 lg:h-24 rounded-md" /></div>
                  <div className="block text-black text-sm lg:text-lg">
                    <p className="text-black text-base lg:text-lg">{selectedOrder?.queryId?.title || "N/A"}</p>
                    <p className="text-black text-base lg:text-lg">{selectedOrder?.queryId?.description || "N/A"}</p>
                  </div>
                </div>
              </div>
              <div className="col-span-1 ">
                <div className="block text-black text-sm lg:text-lg font-semibold">Offer</div>
                <div className="flex items-center gap-4">
                  <div className="block text-black text-sm lg:text-lg">
                    <p className="text-black text-base lg:text-lg"><span className="font-semibold">Price :</span>₹{selectedOrder?.offerId?.price || "N/A"}</p>
                    <p className="text-black text-base lg:text-lg"><span className="font-semibold">Description :</span>{selectedOrder?.offerId?.description || "N/A"}</p>
                  </div>
                </div>
              </div>
              <div className="col-span-2">
                <div className="block text-black text-sm lg:text-lg font-semibold">Services</div>
                <div className="grid grid-cols-3">
                  {selectedOrder?.services?.map((item, index) => (
                    <div className="col-span-1" key={item._id}>
                      <div className="block text-black text-sm lg:text-lg">
                        <p className="text-black text-base lg:text-lg">
                          <span className="font-semibold">Name :</span> {item.name || "N/A"}
                        </p>
                        <p className="text-black text-base lg:text-lg">
                          <span className="font-semibold">Price :</span> ₹{item.price || "N/A"}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex items-center justify-start gap-2">
                <div className="block text-black text-sm lg:text-lg font-semibold">Status:</div>
                <div className="block text-black text-sm lg:text-lg">
                  {selectedOrder?.status || "N/A"}
                </div>
              </div>
              <div className="flex items-center justify-start gap-2">
                <div className="block text-black text-sm lg:text-lg font-semibold">Total Amount:</div>
                <div className="block text-black text-sm lg:text-lg">
                  {selectedOrder?.totalAmount || "N/A"}
                </div>
              </div>
              <div className="flex items-center justify-start gap-2">
                <div className="block text-black text-sm lg:text-lg font-semibold">Order Date:</div>
                <div className="block text-black text-sm lg:text-lg">
                  {selectedOrder?.createdAt ? moment(selectedOrder.createdAt).format("DD-MM-YYYY") : "N/A"}
                </div>
              </div>
            </div>
          </div>
        </>
      ) : (
        <p>No order found.</p>
      )}
    </>
  );
};

export default OrderDetails;
