import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { <PERSON><PERSON>, Switch, Spin } from "antd";
import noDataImage from "./../assets/icons/nodataimage.svg";
import DashboardMetrics from "../components/DashboardMetrics";
import UserModel from "./../components/CreateUserModel";
import "./Users.css";
import Swal from "sweetalert2";
import moment from "moment";
import TopBar from "./../components/TopBar";
import { ShimmerContentBlock, ShimmerDiv, ShimmerTable } from "shimmer-effects-react";
import { fetchUsers, setSearchText, toggleUserStatus } from "../slices/userSlice";
import { getAllUser } from "../services/api";

const Users = () => {
  const dispatch = useDispatch();

  // Get state from Redux store
  const {
    data: users,
    totalPages,
    currentPage: currentPageFromAPI,
    loading,
    error,
    limit: limitFromStore,
    searchText: searchTextFromStore,
  } = useSelector((state) => state.users);
  const totalUsers = useSelector((state) => state.users.totalUsers);

  // Local state for filters and pagination
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [searchText, setSearchTextLocal] = useState(searchTextFromStore || "");
  const [filter, setFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");
  const [limit, setLimit] = useState(10); // Default limit is 10
  const [sortConfig, setSortConfig] = useState({ key: "createdAt", direction: "asc" });
  const [currentPage, setCurrentPage] = useState(1);

  // Effect to fetch vendors when filters, search, or pagination changes
  useEffect(() => {
    const params = {
      isActive: filter === "all" ? undefined : filter === "active",
      page: currentPage,
      limit,
      search: searchText,
      sortBy: sortConfig.key,
      order: sortConfig.direction,
      ...getDateRangeParams(dateFilter), // Add date range params if applicable
    };
    dispatch(fetchUsers(params));
  }, [searchText, filter, limit, dateFilter, sortConfig, currentPage, dispatch]);

  // Helper function to calculate date range params
  const getDateRangeParams = (range) => {
    const now = moment();
    switch (range) {
      case "today":
        return {
          startDate: now.startOf("day").toISOString(),
          endDate: now.endOf("day").toISOString(),
        };
      case "week":
        return {
          startDate: now.startOf("week").toISOString(),
          endDate: now.endOf("week").toISOString(),
        };
      case "month":
        return {
          startDate: now.startOf("month").toISOString(),
          endDate: now.endOf("month").toISOString(),
        };
      case "year":
        return {
          startDate: now.startOf("year").toISOString(),
          endDate: now.endOf("year").toISOString(),
        };
      default:
        return {};
    }
  };

  // Handle CSV download
  const handleDownloadCSV = async () => {
    const params = {
      limit: totalUsers, // Fetch all records for CSV
      ...(filter !== "all" && { isActive: filter === "active" }),
      ...(dateFilter !== "all" && {
        startDate: moment().startOf(dateFilter).toISOString(),
        endDate: moment().endOf(dateFilter).toISOString(),
      }),
    };

    try {
      const { data } = await getAllUser(params);
      const csvData = convertToCSV(data.users);
      const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `users_report.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      Swal.fire("Error", "Failed to download CSV", "error");
    }
  };

  // Convert data to CSV format
  const convertToCSV = (data) => {
    const headers = ["No.", "Full Name", "Email ID", "Date", "User Type", "Status"];
    const csvRows = [headers.join(",")];

    data.forEach((user, index) => {
      const row = [
        index + 1,
        user.name || "N/A",
        user.email || "N/A",
        moment(user.createdAt).format("DD-MM-YY"),
        user.password ? "Email" : "Google User",
        user.status,
      ];
      csvRows.push(row.join(","));
    });

    return csvRows.join("\n");
  };

  // Handle user status toggle
  const handleSwitchChange = ({ id, isActive }) => {
    dispatch(toggleUserStatus({ id, isActive }));
  };

  // Handle sorting
  const handleSort = (key) => {
    const direction = sortConfig.key === key && sortConfig.direction === "asc" ? "desc" : "asc";
    setSortConfig({ key, direction });
  };

  // Pagination handlers
  const handlePreviousPage = () => setCurrentPage((prev) => Math.max(prev - 1, 1));
  const handleNextPage = () => setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setCurrentPage(1); // Reset page to 1 whenever filter changes
    setSearchTextLocal(value);
    dispatch(setSearchText(value)); // Update Redux store
  };

  // Show loading animation or error message if needed
  const renderShimmer = () => {
    return Array.from({ length: 10 }).map((_, index) => (
      <tr key={index} className="shimmer-row">
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer shimmer-circle"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
      </tr>
    ));
  };
  const shimmerStyles = `
        .shimmer-row {
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
            background-size: 800px 100%;
        }
        
        .shimmer {
            height: 20px;
            background: #f0f0f0;
            border-radius: 4px;
            margin: 4px 0;
        }
        
        .shimmer-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }
        
        @keyframes shimmer {
            0% { background-position: -400px 0; }
            100% { background-position: 400px 0; }
        }
    `;

  return (
    <>
      <TopBar
        searchPlaceholder="Search Vendor Here"
        isSearchEnable={false}
        onSearch={handleSearchChange}
      />
      <div className="p-6">
        <div className="users-container">
          <div className="metrics-section">
            <DashboardMetrics title="Total Users" count={totalUsers} />
            <DashboardMetrics title="Total Orders" count={0} />
            <DashboardMetrics title="Total Spending" count={`€ 0`} />
            <DashboardMetrics title="Total Pending Orders" count={0} />
          </div>

          <div className="search-create-section">
            <div className="dropdown-buttons">
              <div className="users-dropdown">
                <button className="users-dropdown-button shadow-sm clickable">
                  {filter === "all" ? "User Status" : filter === "active" ? "Activated Users" : "Deactivated Users"}
                  <div className="users-dropdown-content">
                    <a href="#" className={filter === "all" ? "active" : ""} onClick={() => setFilter("all")}>
                      All Users
                    </a>
                    <a href="#" className={filter === "active" ? "active" : ""} onClick={() => setFilter("active")}>
                      Activated Users
                    </a>
                    <a href="#" className={filter === "deactive" ? "active" : ""} onClick={() => setFilter("deactive")}>
                      Deactivated Users
                    </a>
                  </div>
                </button>
              </div>
              <div className="users-dropdown">
                <button className="users-dropdown-button">
                  {dateFilter === "all" ? "Date Filter" : dateFilter === "today" ? "Today" : dateFilter === "week" ? "This Week" : dateFilter === "month" ? "This Month" : "This Year"}
                  <div className="users-dropdown-content">
                    <a href="#" className={dateFilter === "all" ? "active" : ""} onClick={() => setDateFilter("all")}>
                      All Dates
                    </a>
                    <a href="#" className={dateFilter === "today" ? "active" : ""} onClick={() => setDateFilter("today")}>
                      Today
                    </a>
                    <a href="#" className={dateFilter === "week" ? "active" : ""} onClick={() => setDateFilter("week")}>
                      This Week
                    </a>
                    <a href="#" className={dateFilter === "month" ? "active" : ""} onClick={() => setDateFilter("month")}>
                      This Month
                    </a>
                    <a href="#" className={dateFilter === "year" ? "active" : ""} onClick={() => setDateFilter("year")}>
                      This Year
                    </a>
                  </div>
                </button>
              </div>
              <div className='flex text-black'>
                <label htmlFor="limit" className='text-black w-40 flex items-center justify-center'>Items per page: </label>
                <select
                  id="limit"
                  className='w-12'
                  value={limit}
                  onChange={(e) => setLimit(Number(e.target.value))}
                >
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                </select>

                {/* Your existing UI components here */}
              </div>
            </div>
            <Button type="primary" className="create-button clickable" onClick={() => setIsModalVisible(true)}>
              Create New User
            </Button>
          </div>
          {loading ? (
            <div className="user-table-container">
              <table className="user-table">

                <tbody>
                  {renderShimmer()}
                </tbody>
              </table>
            </div>
          ) : error ? (
            <div className="error-message">{error}</div>
          ) : users.length > 0 ? (
            <div className="user-table-container">
              <table className="user-table">
                <thead>
                  <tr>
                    <th className="header-cell">No.</th>
                    <th className="header-cell clickable">Photo</th>
                    <th className="header-cell clickable" onClick={() => handleSort("name")}>
                      Full Name {sortConfig.key === "name" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                    </th>
                    <th className="header-cell clickable" onClick={() => handleSort("phone")}>
                      Mobile No. {sortConfig.key === "phone" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                    </th>
                    <th className="header-cell clickable" onClick={() => handleSort("wallet")}>
                      Wallet {sortConfig.key === "wallet" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                    </th>
                    <th className="header-cell clickable" onClick={() => handleSort("createdAt")}>
                      Joining Date {sortConfig.key === "createdAt" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                    </th>
                    <th className="header-cell">User Type</th>
                    <th className="header-cell">Activate/Deactivate</th>
                    <th className="header-cell">More</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user, index) => (
                    <tr key={user._id}>
                      <td className="body-cell">{(currentPage - 1) * limit + index + 1}</td>
                      <td className="body-cell">
                        {user?.profilePhoto ? (
                          <img src={user.profilePhoto} alt="User Profile" className="w-10 h-10 rounded-full" />
                        ) : (
                          <div className="w-10 h-10 rounded-full bg-gray-200"></div>
                        )}
                      </td>
                      <td className="body-cell">{user.name}</td>
                      <td className="body-cell">{user.phone}</td>
                      <td className="body-cell">{user.wallet}</td>
                      <td className="body-cell">{moment(user.createdAt).format("DD-MM-YY")}</td>
                      <td className="body-cell">{user.accountType}</td>
                      <td className="body-cell">
                        <Switch
                          checked={user.isActive}
                          onChange={() => handleSwitchChange({ id: user._id, isActive: user.isActive })}
                          style={{ backgroundColor: user.isActive ? "#0096c7" : "#798593" }}
                        />
                      </td>
                      <td className="body-cell">
                        <Link to={`/users/${user._id}`} className="more-link clickable">
                          More..
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              <div className="pagination-container mt-4 mx-auto justify-between px-1">
                <button onClick={handlePreviousPage} disabled={currentPage === 1}>&lt; Previous</button>
                <span className="text-black">Page {currentPage} of {totalPages}</span>
                <button onClick={handleNextPage} disabled={currentPage === totalPages}>Next &gt;</button>
              </div>
              {/* <div className="download-report">
                <button className="download-link" onClick={handleDownloadCSV}>Download Report</button>
              </div> */}
            </div>
          ) : (
            <div className="no-data-section">
              <img src={noDataImage} alt="No data" className="no-data-image" />
              <div className="no-data-title">No Users to show right now!</div>
              <div className="no-data-description">
                We don't have any user's info to show here. Please add users first and then check their details here.
              </div>
            </div>
          )}

          {isModalVisible && (
            <div className="modal-overlay" onClick={() => setIsModalVisible(false)}>
              <div className="modal-container" onClick={(e) => e.stopPropagation()}>
                <UserModel closeModal={() => setIsModalVisible(false)} updateUserData={() => dispatch(fetchUsers())} />
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Users;