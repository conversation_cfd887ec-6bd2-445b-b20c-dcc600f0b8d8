import React, { useEffect, useState } from "react";
import Swal from "sweetalert2";
import "./VendorsPayout.css";
import noDataImage from "./../assets/icons/empty_order.svg";
import TopBar from "./../components/TopBar";
import { ShimmerTable } from "shimmer-effects-react";
import { useDispatch, useSelector } from "react-redux";
import { fetchTransactions } from "../slices/transactionsSlice";

const VendorsPayout = () => {
  const dispatch = useDispatch();
  const { data: transactions, status, error } = useSelector((state) => state.transactions);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchText, setSearchText] = useState("");
  const [sortConfig, setSortConfig] = useState({
    key: "date",
    direction: "desc",
  });
  const rowsPerPage = 10;

  useEffect(() => {
    dispatch(fetchTransactions());
  }, [dispatch]);

  const convertToCSV = (data) => {
    const headers = [
      "Serial No.",
      "Sender Name",
      "Receiver Name",
      "Amount",
      "Date",
      "Transaction Type"
    ];

    const csvRows = [];
    csvRows.push(headers.join(","));

    data.forEach((transaction, index) => {
      const row = [
        index + 1,
        transaction.sender?.name || "N/A",
        transaction.receiver?.name || "N/A",
        transaction.amount?.toFixed(2) || "0.00",
        formatDate(transaction.date),
        transaction.transactionType || "N/A"
      ];
      csvRows.push(row.join(","));
    });

    return csvRows.join("\n");
  };

  const handleDownloadCSV = () => {
    if (!transactions?.body?.length) return;

    const csvData = convertToCSV(filteredData);
    const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "transactions_report.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const handleSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  const sortedData = [...(transactions?.body || [])].sort((a, b) => {
    if (sortConfig.key === "date") {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return sortConfig.direction === "asc" ? dateA - dateB : dateB - dateA;
    }

    const valueA = a[sortConfig.key]?.toString().toLowerCase() || "";
    const valueB = b[sortConfig.key]?.toString().toLowerCase() || "";

    if (sortConfig.key.includes(".")) {
      const keys = sortConfig.key.split('.');
      const nestedValueA = a[keys[0]]?.[keys[1]]?.toString().toLowerCase() || "";
      const nestedValueB = b[keys[0]]?.[keys[1]]?.toString().toLowerCase() || "";
      return sortConfig.direction === "asc"
        ? nestedValueA.localeCompare(nestedValueB)
        : nestedValueB.localeCompare(nestedValueA);
    }

    return sortConfig.direction === "asc"
      ? valueA.localeCompare(valueB)
      : valueB.localeCompare(valueA);
  });

  const filteredData = sortedData.filter(transaction => {
    const searchLower = searchText.toLowerCase();
    return (
      transaction.sender?.name?.toLowerCase().includes(searchLower) ||
      transaction.receiver?.name?.toLowerCase().includes(searchLower) ||
      transaction.amount?.toString().includes(searchLower) ||
      transaction.transactionType?.toLowerCase().includes(searchLower)
    );
  });

  const indexOfLastRow = currentPage * rowsPerPage;
  const indexOfFirstRow = indexOfLastRow - rowsPerPage;
  const currentRows = filteredData.slice(indexOfFirstRow, indexOfLastRow);
  const totalPages = Math.ceil(filteredData.length / rowsPerPage);

  const handleNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const handleSearchChange = (event) => {
    setSearchText(event.target.value);
    setCurrentPage(1);
  };

  const renderTransactionsTable = () => (
    <table className="vendors-payout-table rounded-lg bg-secondary">
      <thead className="text-black">
        <tr>
          <th>Serial No.</th>
          <th onClick={() => handleSort("sender.name")} className="clickable">
            Sender {sortConfig.key === "sender.name" && (sortConfig.direction === "asc" ? "↑" : "↓")}
          </th>
          <th onClick={() => handleSort("receiver.name")} className="clickable">
            Receiver {sortConfig.key === "receiver.name" && (sortConfig.direction === "asc" ? "↑" : "↓")}
          </th>
          <th onClick={() => handleSort("amount")} className="clickable">
            Amount (€) {sortConfig.key === "amount" && (sortConfig.direction === "asc" ? "↑" : "↓")}
          </th>
          <th onClick={() => handleSort("date")} className="clickable">
            Date {sortConfig.key === "date" && (sortConfig.direction === "asc" ? "↑" : "↓")}
          </th>
          <th onClick={() => handleSort("transactionType")} className="clickable">
            Type {sortConfig.key === "transactionType" && (sortConfig.direction === "asc" ? "↑" : "↓")}
          </th>
        </tr>
      </thead>
      <tbody>
        {currentRows.map((transaction, index) => (
          <tr key={transaction._id}>
            <td>{indexOfFirstRow + index + 1}</td>
            <td>{transaction.sender?.name || "N/A"}</td>
            <td>{transaction.receiver?.name || "N/A"}</td>
            <td>{transaction.amount?.toFixed(2)}</td>
            <td>{formatDate(transaction.date)}</td>
            <td>{transaction.transactionType || "N/A"}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );

  if (status === "loading") return <ShimmerTable row={10} />;
  if (status === "failed") return (
    <div className="error-message">
      <h3>Error loading transactions</h3>
      <p>{error?.message || "Please try again later"}</p>
    </div>
  );

  return (
    <>
      <TopBar
        searchPlaceholder="Search transactions..."
        onSearchChange={handleSearchChange}
      />
      <div className="p-6">
        <div className="vendors-payout-container">
          <div className="orders-content">
            {transactions?.body?.length ? (
              renderTransactionsTable()
            ) : (
              <div className="empty-orders">
                <img src={noDataImage} className="empty-orders-icon" alt="No Data" />
                <h3 className="no-orders-title">No transactions found</h3>
                <p className="no-orders-text">
                  There are currently no transactions to display.
                </p>
              </div>
            )}
          </div>

          {transactions?.body?.length > 0 && (
            <>
              <div className="pagination-container mt-4 mx-auto justify-between px-1">
                <button onClick={handlePreviousPage} disabled={currentPage === 1}>
                  &lt; Previous
                </button>
                <span className="text-black">
                  Page {currentPage} of {totalPages}
                </span>
                <button onClick={handleNextPage} disabled={currentPage === totalPages}>
                  Next &gt;
                </button>
              </div>

              {/* <div className="download-report">
                <button className="download-link" onClick={handleDownloadCSV}>
                  Download Report
                </button>
              </div> */}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default VendorsPayout;