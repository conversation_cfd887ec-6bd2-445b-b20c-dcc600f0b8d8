import React, { useEffect, useState } from "react";
import "./notification.css";
import noDataImage from "./../assets/icons/nodataimage.svg";
import Swal from "sweetalert2";
import { ShimmerTable } from "shimmer-effects-react";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchUserNotifications,
  fetchVendorNotifications,
  sendUserNotification,
  sendVendorNotification,
} from "../slices/notificationsSlice";

const Notifications = () => {
  // Redux state
  const { users = [], vendors = [], status, error } = useSelector(
    (state) => state.notifications
  );
  const dispatch = useDispatch();

  // Local state
  const [title, setTitle] = useState("");
  const [body, setBody] = useState("");
  const [view, setView] = useState("userNotifications");
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const [currentPageUser, setCurrentPageUser] = useState(1);
  const [currentPageVendor, setCurrentPageVendor] = useState(1);
  const [itemsPerPage] = useState(5);

  // Fetch notifications on mount
  useEffect(() => {
    dispatch(fetchUserNotifications());
    dispatch(fetchVendorNotifications());
  }, [dispatch]);

  // Handle API errors
  useEffect(() => {
    if (error) {
      // Swal.fire({
      //   icon: "error",
      //   title: "Error",
      //   text: error,
      // });
    }
  }, [error]);

  // Toggle the popup for creating notifications
  const togglePopup = () => setIsPopupVisible(!isPopupVisible);

  // Handle sending notifications
  const handleSendNotification = () => {
    if (!title.trim() || !body.trim()) {
      Swal.fire("Error", "Title and body cannot be empty", "error");
      return;
    }

    const action =
      view === "userNotifications" ? sendUserNotification : sendVendorNotification;

    dispatch(action({ title, body }))
      .unwrap()
      .then(() => {
        Swal.fire("Success", "Notification sent successfully", "success");
        togglePopup();
        setTitle("");
        setBody("");
        // Refresh notifications
        dispatch(fetchUserNotifications());
        dispatch(fetchVendorNotifications());
      })
      .catch((error) => {
        Swal.fire("Error", error.message || "Failed to send notification", "error");
      });
  };

  // Paginate the notifications
  const paginate = (items, pageNumber) => {
    const startIndex = (pageNumber - 1) * itemsPerPage;
    return items.slice(startIndex, startIndex + itemsPerPage);
  };

  // Determine which notifications to display
  const currentNotifications = view === "userNotifications" ? users : vendors;
  const totalPages = Math.ceil(currentNotifications.length / itemsPerPage);
  const currentPage = view === "userNotifications" ? currentPageUser : currentPageVendor;

  // Handle pagination navigation
  const handlePreviousPage = () => {
    if (view === "userNotifications") {
      setCurrentPageUser((prev) => Math.max(prev - 1, 1));
    } else {
      setCurrentPageVendor((prev) => Math.max(prev - 1, 1));
    }
  };

  const handleNextPage = () => {
    if (view === "userNotifications") {
      setCurrentPageUser((prev) => Math.min(prev + 1, totalPages));
    } else {
      setCurrentPageVendor((prev) => Math.min(prev + 1, totalPages));
    }
  };

  return (
    <div className="notifications-container">
      <div className="buttons-container">
        <button
          className={`btn1 ${view === "userNotifications" ? "active-btn" : "de-btn"}`}
          onClick={() => setView("userNotifications")}
        >
          User Notifications
        </button>
        <button
          className={`btn ${view === "sendToVendor" ? "active-btn" : "de-btn"} ml-4`}
          onClick={() => setView("sendToVendor")}
        >
          Vendor Notifications
        </button>
      </div>

      {status === "loading" ? (
        <ShimmerTable rows={5} columns={1} />
      ) : currentNotifications.length === 0 ? (
        <div className="no-notifications text-center">
          <img src={noDataImage} alt="No data" className="no-data-image" />
          <h3 className="text-lg font-semibold">No Notifications to show!</h3>
          <p className="text-light-gray">Notifications will appear here once created</p>
          <button
            className="btn bg-yellow-400 mt-4 active-btn"
            onClick={togglePopup}
          >
            CREATE NOW
          </button>
        </div>
      ) : (
        <div className="notifications-list">
          {paginate(currentNotifications, currentPage).map((notification) => (
            <div
              key={notification._id}
              className="flex items-center p-4 mb-4 bg-secondary rounded-lg relative cursor-pointer"
              onClick={() =>
                Swal.fire({
                  title: notification.title,
                  text: notification.body,
                  confirmButtonText: "Close",
                })
              }
            >
              <div className="user-notification-content">
                <h4 className="text-xl font-semibold text-black">
                  {notification?.title}
                </h4>
                <p className="text-black/50 truncate-message1">
                  {notification?.body}
                </p>
              </div>
              <span className="absolute bottom-2 right-2 notification-date w-16 h-12 flex items-center justify-center text-black/50 text-sm">
                {new Date(notification?.createdAt).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                })}
              </span>
            </div>
          ))}

          <div className="pagination-container mt-4 mx-auto justify-between px-1">
            <button onClick={handlePreviousPage} disabled={currentPage === 1}>
              &lt; Previous
            </button>
            <span className="text-black">
              Page {currentPage} of {totalPages}
            </span>
            <button onClick={handleNextPage} disabled={currentPage === totalPages}>
              Next &gt;
            </button>
          </div>
        </div>
      )}

      <button className="fab bg-yellow-400 text-white" onClick={togglePopup}>
        ✎
      </button>

      {isPopupVisible && (
        <div className="popup-overlay" onClick={togglePopup}>
          <div className="popup-content" onClick={(e) => e.stopPropagation()}>
            <button className="close-btn pl-4 left-align-button text-black" onClick={togglePopup}>
              &larr;
            </button>
            <h2 className="pl-10 popup-title text-black">Create Notification!</h2>
            <p className="pl-10 popup-subtitle text-black">
              Write what you want to say to your{" "}
              {view === "userNotifications" ? "users" : "vendors"}!
            </p>
            <input
              type="text"
              className="pl-3 popup-input text-black"
              placeholder="Title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
            <textarea
              className="popup-textarea text-black"
              placeholder="Write here..."
              value={body}
              onChange={(e) => setBody(e.target.value)}
            ></textarea>
            <div className="popup-send-btn-container">
              <button className="popup-send-btn" onClick={handleSendNotification}>
                Send
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Notifications;