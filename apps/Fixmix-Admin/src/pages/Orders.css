/* Gradient background for the active button */
.active-btn {
  background: linear-gradient(90deg, #0096c7 0%, #4bd1fd 100%);
}

.de-btn {
  border: 2px solid rgb(105, 105, 105);
  /* White border */
  color: #000 !important;
  border-radius: 10px;
}

/* Override background for hover effect */
.btn.bg-yellow-400:hover {
  background-color: #f59e0b;
}

@media (max-width: 600px) {

  .btn1,
  .btn {
    font-size: 0.9rem;
    padding: 0.5rem 1.5rem;
  }
}

body {
  font-family: "Poppins", sans-serif;
  background-color: #2c2e2d;
  color: white;
}

.orders-container {
  padding: 2rem;
}

.buttons-container {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.dropdown-buttons-container {
  display: flex;
  justify-content: flex-start;
  /* Align to the left */
  gap: 1rem;
  margin-bottom: 1rem;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-button {
  color: #373938;
  background-color: #faf9f9;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.clickable-header {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: #faf9f9;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.dropdown-content a {
  color: #000;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}

.dropdown-content a:hover {
  background-color: #0096c7;
  color: white;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.progress-orders-button,
.completed-orders-button {
  background-color: #373938;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.progress-orders-button.active,
.completed-orders-button.active {
  background-color: #34a853;
}

.orders-content {
  margin-top: 1rem;
}

.orders-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 10px;
}

.orders-table th,
.orders-table td {
  padding: 0.75rem;
  text-align: left;
}

.orders-table th {
  color: #000;
  background: #faf9f9;
}

.orders-table tr:nth-child(even) {
  background: #faf9f9;
  color: #000;
}

.orders-table tr:nth-child(odd) {
  background-color: #faf9f9;
  color: #000;
}

.orders-table tr:hover {
  background-color: #faf9f9;
  color: #000;
}

.empty-orders {
  text-align: center;
  margin-top: 5rem;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  /* Center the empty icon */
}

.empty-orders-icon {
  width: 250px;
  margin-bottom: 1rem;
}

.no-orders-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.no-orders-text {
  font-size: 1rem;
  color: #9d9d9d;
}

.download-report {
  text-align: right;
  margin-top: 1rem;
}

.download-link {
  color: #0096c7;
  text-decoration: none;
  font-weight: bold;
}

/* Common styles for buttons */
.btn1,
.btn {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 650;
  border: none;
  border-radius: 20px;
  padding: 0.75rem 1.5rem;
  /* Adjusted button padding for smaller width */
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.05);
  margin: 5px;
  flex: 1 1 auto;
  transition: background 0.3s ease-in-out;
}