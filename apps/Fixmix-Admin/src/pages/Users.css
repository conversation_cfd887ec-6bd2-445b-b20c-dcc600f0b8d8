.search-create-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.users-container {
  padding: 1rem;
}

.dropdown-buttons {
  display: flex;
  gap: 1rem;
}

.dropdown-button {
  background: #2c2e2d;
  color: #ffffff;
  font-size: 16px;
  font-weight: 300;
  border: none;
  border-radius: 15px;
  padding: 0.9rem 1.3rem;
}

.create-button {
  background: linear-gradient(90deg, #0096c7 0%, #6ccaf0 100%);
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  border: none;
  border-radius: 10px;
  padding: 1.4rem 1.1rem;
  box-shadow: 0px 4px 201px rgba(0, 0, 0, 0.05);
}

.metrics-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.metric-card {
  background-color: #2c2e2d;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.metric-title {
  color: rgba(255, 255, 255, 0.5);
}

.metric-value {
  color: #f6d724;
  font-size: 2rem;
  font-weight: bold;
}

.no-data-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #faf9f9;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  height: 50vh;
}

.no-data-image {
  margin-bottom: 1rem;
}

.no-data-title {
  color: #000;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.no-data-description {
  color: rgba(146, 144, 144, 0.5);
  text-align: center;
}

/* Table Styles */
.user-table-container {
  margin-top: 20px;
}

/* Users.css */
.clickable {
  cursor: pointer;
}

.clickable-header {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.clickable-header span {
  margin-left: 5px;
}

.user-table {
  width: 100%;
  border-collapse: collapse;
  background: #faf9f9;
  color: #000;
  box-shadow: 2px 2px 8.3px -5px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
  overflow: hidden;
}

.user-table th,
.user-table td {
  padding: 15px;
  text-align: left;
  /* color: #ffffff; */
}

.user-table th {
  background: #faf9f9;
  border-bottom: none;
  border-top: 1px solid #444;
  color: #000;
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  font-size: 16px;
}

.user-table thead th {
  background: #faf9f9;
  border-bottom: none;
  border-top: none;
  border-radius: 10px 10px 0 0;
}

.user-table tbody tr:nth-child(odd) {
  color: #000;
}

.user-table tbody tr:nth-child(even) {
  color: #000;
}

.more-link {
  color: #0096c7;
  cursor: pointer;
  text-decoration: underline;
}

.delete-link {
  color: #f62424;
  cursor: pointer;
  text-decoration: underline;
}

/* Custom table layout as per provided CSS */
.table-container {
  position: relative;
}

.table-header,
.table-row {
  width: 100%;
  height: 65px;
  background: #2c2e2d;
  box-shadow: 2px 2px 8.3px -5px rgba(0, 0, 0, 0.25);
  border-radius: 10px 10px 0 0;
}

.table-cell {
  padding: 10px;
}

.header-cell {
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #000;
}

.body-cell {
  font-family: "Poppins", sans-serif;
  font-weight: 300;
  font-size: 14px;
  color: #000;
}

/* Pagination Container */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
  padding: 10px;
}

/* Button Styles */
.pagination-container button {
  background-color: #333;
  border: none;
  color: #fff;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.pagination-container button:disabled {
  background-color: #555;
  color: #fff;
  cursor: not-allowed;
}

.pagination-container button:hover:not(:disabled) {
  background-color: #555;
}

/* Current Page Display */
.pagination-container span {
  font-size: 14px;
}

.download-report {
  text-align: right;
  margin-top: 1rem;
}

.download-link {
  color: #fff;
  background: linear-gradient(90deg, #0096c7 0%, #4bd1fd 100%);
  text-decoration: none;
  font-weight: bold;
  padding: 10px;
  border-radius: 10px;
}