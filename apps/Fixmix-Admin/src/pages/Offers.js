import React, { useEffect, useState } from 'react'
import TopBar from '../components/TopBar'
import UserModel from '../components/CreateUserModel'
import DashboardMetrics from '../components/DashboardMetrics';
import noDataImage from "./../assets/icons/nodataimage.svg";
import { Button, Switch } from 'antd';
import Swal from 'sweetalert2';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { Link } from 'react-router-dom';
import { fetchOffers, setSearchText } from '../slices/offerSlice';
const Offers = () => {
    const dispatch = useDispatch();
    const {
        offer,
        isLoading,
        error,
        totalOffers,
        totalPages,
        currentPage: currentPageFromStore,
        limit: limitFromStore,
        searchText: searchTextFromStore, // Get searchText from store
    } = useSelector((state) => state.offer);

    const [filter, setFilter] = useState("all");
    const [limit, setLimit] = useState(10); // Default limit is 10
    const [dateFilter, setDateFilter] = useState("all");
    const [currentPage, setCurrentPage] = useState(1);
    const [searchText, setSearchTextLocal] = useState(searchTextFromStore || "");
    const [sortConfig, setSortConfig] = useState({
        key: "createdAt",
        direction: "asc",
    });
    // Effect to fetch data when any parameter changes
    useEffect(() => {
        const params = {
            page: currentPage,
            limit,
            status: filter !== "all" ? filter : undefined,
            search: searchText,
            sortBy: sortConfig.key,
            order: sortConfig.direction,
            ...getDateRangeParams(dateFilter)
        };
        dispatch(fetchOffers(params));
    }, [currentPage, filter, limit, dateFilter, searchText, sortConfig, dispatch]);

    // Shimmer loading effect
    const renderShimmer = () => {
        return Array.from({ length: 10 }).map((_, index) => (
            <tr key={index} className="shimmer-row">
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer shimmer-circle"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
                <td className="body-cell"><div className="shimmer"></div></td>
            </tr>
        ));
    };

    // Add this CSS for shimmer effect
    const shimmerStyles = `
           .shimmer-row {
               animation: shimmer 1.5s infinite linear;
               background: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
               background-size: 800px 100%;
           }
           
           .shimmer {
               height: 20px;
               background: #f0f0f0;
               border-radius: 4px;
               margin: 4px 0;
           }
           
           .shimmer-circle {
               width: 40px;
               height: 40px;
               border-radius: 50%;
           }
           
           @keyframes shimmer {
               0% { background-position: -400px 0; }
               100% { background-position: 400px 0; }
           }
       `;

    // Add styles to document head
    const styleSheet = document.createElement('style');
    styleSheet.type = 'text/css';
    styleSheet.innerText = shimmerStyles;
    document.head.appendChild(styleSheet);
    // Function to handle filter change
    const handleFilterChange = (newFilter) => {
        setFilter(newFilter); // Update the filter
        setCurrentPage(1); // Reset page to 1 whenever filter changes
    };

    const getDateRangeParams = (range) => {
        const now = moment();
        switch (range) {
            case 'today':
                return {
                    startDate: now.startOf('day').toISOString(),
                    endDate: now.endOf('day').toISOString()
                };
            case 'week':
                return {
                    startDate: now.startOf('week').toISOString(),
                    endDate: now.endOf('week').toISOString()
                };
            case 'month':
                return {
                    startDate: now.startOf('month').toISOString(),
                    endDate: now.endOf('month').toISOString()
                };
            case 'year':
                return {
                    startDate: now.startOf('year').toISOString(),
                    endDate: now.endOf('year').toISOString()
                };
            default:
                return {};
        }
    };
    const sortUsers = (key) => {
        const direction = sortConfig.key === key && sortConfig.direction === "asc" ? "desc" : "asc";
        setSortConfig({ key, direction });
    };

    const statusCounts = offer?.reduce((acc, user) => {
        acc[user.status] = (acc[user.status] || 0) + 1;
        return acc;
    }, {});

    const completedCount = statusCounts?.completed || 0;
    const acceptedCount = statusCounts?.accepted || 0;
    const closedCount = statusCounts?.closed || 0;
    const openCount = statusCounts?.open || 0;
    const convertToCSV = (data) => {
        const headers = [
            "No.",
            "Full Name",
            "Email ID",
            "Date",
            "User Type",
            "Status",
        ];

        const csvRows = [];
        csvRows.push(headers.join(","));

        data.forEach((user, index) => {
            const row = [
                index + 1, // Serial No.
                user.name || "N/A", // Full Name
                user.email || "N/A", // Email ID
                moment(user.createdAt).format("DD-MM-YY"), // Date
                user.password ? "Email" : "Google User", // User Type
                user.status, // Status (active/deactive)
            ];
            csvRows.push(row.join(","));
        });

        return csvRows.join("\n");
    };


    // Sorting functionality
    // const sortUsers = (key) => {
    //     let direction = "asc";
    //     if (sortConfig.key === key && sortConfig.direction === "asc") {
    //         direction = "desc";
    //     }
    //     const sortedUsers = [...offers].sort((a, b) => {
    //         if (key === "name" || key === "email" || key === "createdAt") {
    //             return direction === "asc"
    //                 ? a[key].localeCompare(b[key])
    //                 : b[key].localeCompare(a[key]);
    //         }
    //         return direction === "asc" ? a[key] - b[key] : b[key] - a[key];
    //     });
    //     // setUsers(sortedUsers);
    //     setSortConfig({ key, direction });
    // };
    // const toggleModal = () => {
    //     setIsModalVisible(!isModalVisible);
    // };

    const handleDownloadCSV = () => {
        const csvData = convertToCSV(offer); // Use the `users` state that contains the filtered data
        const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });
        const url = URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", `users_report.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // Pagination logic
    const handlePreviousPage = () => {
        if (currentPage > 1) setCurrentPage(currentPage - 1);
    };

    const handleNextPage = () => {
        if (currentPage < totalPages) setCurrentPage(currentPage + 1);
    };

    // const paginatedUsers = offers.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

    // Function to handle search input change
    const handleSearchChange = (e) => {
        const value = e.target.value;
        setCurrentPage(1); // Reset page to 1 whenever filter changes
        setSearchTextLocal(value);
        dispatch(setSearchText(value)); // Update Redux store
    };

    // const statusCounts = offers?.reduce((acc, user) => {
    //     acc[user.status] = (acc[user.status] || 0) + 1;
    //     return acc;
    // }, {});

    // Extracting counts for specific statuses
    // const completedCount = statusCounts?.completed || 0;
    // const acceptedCount = statusCounts?.accepted || 0;
    const sentCount = statusCounts?.sent || 0;
    // const openCount = statusCounts?.open || 0;


    return (
        <>
            <div onClick={() => console.log(Offers)}>Hello</div>
            <TopBar
                searchPlaceholder="Search offers Here"
                isSearchEnable={false}
                onSearch={handleSearchChange}
            />
            <div className="p-6 ">
                <div className="users-container">
                    <div className="metrics-section">
                        <DashboardMetrics title="Total Offers" count={totalOffers} />
                        <DashboardMetrics title="Total Accepted Offer" count={acceptedCount} />
                        <DashboardMetrics title="Total Sent Offer" count={sentCount} />
                        {/* <DashboardMetrics
                            title="Total Resolved"
                            count={`€ ${Math.round(totalSpending)}`}
                        /> */}
                        {/* <DashboardMetrics
                            title="Total Pending Queries"
                            count={totalProcessingOrders}
                        /> */}
                    </div>
                    <div className="search-create-section">
                        {/* Dropdown for Activation Status */}
                        <div className="dropdown-buttons">
                            <div className="users-dropdown">
                                <button className="users-dropdown-button shadow-sm clickable">
                                    {filter === "all" ? "Offer Status" : filter}
                                    <div className="users-dropdown-content">
                                        <a href="#" className={filter === "all" ? "active" : ""} onClick={() => handleFilterChange("all")}>
                                            All Offer
                                        </a>
                                        <a href="#" className={filter === "open" ? "active" : ""} onClick={() => handleFilterChange("accepted")}>
                                            Accepted Offers
                                        </a>
                                        <a href="#" className={filter === "closed" ? "active" : ""} onClick={() => handleFilterChange("sent")}>
                                            Sent Offers
                                        </a>
                                    </div>
                                </button>
                            </div>
                            <div className="users-dropdown">
                                <button className="users-dropdown-button">
                                    {dateFilter === "all" ? "Date Filter" :
                                        dateFilter === "today" ? "Today" :
                                            dateFilter === "week" ? "This Week" :
                                                dateFilter === "month" ? "This Month" : "This Year"}
                                    <div className="users-dropdown-content">
                                        <a href="#" className={dateFilter === "all" ? "active" : ""} onClick={() => setDateFilter("all")}>
                                            All Dates
                                        </a>
                                        <a href="#" className={dateFilter === "today" ? "active" : ""} onClick={() => setDateFilter("today")}>
                                            Today
                                        </a>
                                        <a href="#" className={dateFilter === "week" ? "active" : ""} onClick={() => setDateFilter("week")}>
                                            This Week
                                        </a>
                                        <a href="#" className={dateFilter === "month" ? "active" : ""} onClick={() => setDateFilter("month")}>
                                            This Month
                                        </a>
                                        <a href="#" className={dateFilter === "year" ? "active" : ""} onClick={() => setDateFilter("year")}>
                                            This Year
                                        </a>
                                    </div>
                                </button>
                            </div>
                            <div className='flex text-black'>
                                <label htmlFor="limit" className='text-black w-40 flex items-center justify-center'>Items per page: </label>
                                <select
                                    id="limit"
                                    className='w-12'
                                    value={limit}
                                    onChange={(e) => setLimit(Number(e.target.value))}
                                >
                                    <option value={10}>10</option>
                                    <option value={20}>20</option>
                                    <option value={50}>50</option>
                                </select>

                                {/* Your existing UI components here */}
                            </div>
                        </div>
                    </div>
                    <h1 onClick={() => console.log(offer)}>Hello</h1>
                    {isLoading ? (
                        <div className="user-table-container">
                            <table className="user-table">
                                <thead>
                                    {/* Table headers */}
                                    <tr>
                                        <th className="header-cell">No.</th>
                                        <th className="header-cell">Photo</th>
                                        <th className="header-cell clickable" onClick={() => sortUsers("name")}>
                                            <div className="clickable-header">
                                                Full Name {sortConfig.key === "name" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        <th className="header-cell clickable" onClick={() => sortUsers("phone")}>
                                            <div className="clickable-header">
                                                Mobile {sortConfig.key === "phone" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        <th className="header-cell clickable" onClick={() => sortUsers("createdAt")}>
                                            <div className="clickable-header">
                                                Date {sortConfig.key === "createdAt" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        <th className="header-cell">Query</th>
                                        <th className="header-cell">Add on services</th>
                                        <th className="header-cell">Status</th>
                                        <th className="header-cell">More</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {renderShimmer()}
                                </tbody>
                            </table>
                        </div>
                    ) : error ? (
                        <div className="error-message">{error}</div>
                    ) : offer?.length > 0 ? (
                        <div className="user-table-container">
                            <table className="user-table">
                                <thead>
                                    <tr>
                                        <th className="header-cell">No.</th>
                                        {/* <th className="header-cell">Photo</th> */}
                                        <th
                                            className="header-cell clickable"
                                            onClick={() => sortUsers("name")}
                                        >
                                            <div className="clickable-header">
                                                Full Name{" "}
                                                {sortConfig.key === "name" &&
                                                    (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th>
                                        {/* <th
                                            className="header-cell clickable"
                                            onClick={() => sortUsers("email")}
                                        >
                                            <div className="clickable-header">
                                                Mobile{" "}
                                                {sortConfig.key === "email" &&
                                                    (sortConfig.direction === "asc" ? "↑" : "↓")}
                                            </div>
                                        </th> */}
                                        <th
                                            className="header-cell clickable"
                                            onClick={() => sortUsers("createdAt")}
                                        >
                                            <div className="clickable-header">
                                                Date{" "}
                                                {sortConfig.key === "createdAt"
                                                    ? sortConfig.direction === "asc"
                                                        ? "↑"
                                                        : "↓"
                                                    : sortConfig.key === null &&
                                                        sortConfig.direction === "asc"
                                                        ? "↑"
                                                        : ""}
                                            </div>
                                        </th>
                                        <th className="header-cell">Price</th>
                                        <th className="header-cell">Description</th>
                                        <th className="header-cell">Status</th>
                                        {/* <th className="header-cell">Activate/Deactivate</th> */}
                                        <th className="header-cell">More</th>
                                        {/* <th className="header-cell">Action</th> */}
                                    </tr>
                                </thead>
                                <tbody>
                                    {offer.map((user, index) => (
                                        <tr onClick={() => console.log(user)} key={user._id}>
                                            <td className="body-cell clickable">
                                                {(currentPage - 1) * limit + index + 1}
                                            </td>
                                            {/* <td className="body-cell">
                                                {user.userId.profilePhoto === "" ?
                                                    <>  {user.userId.profilePhoto} </> :
                                                    <>
                                                        <img src={user.userId.profilePhoto} alt="User Profile" className="w-10 h-10 rounded-full" />
                                                    </>
                                                }
                                            </td> */}
                                            <td className="body-cell">{user?.queryId?.userId?.name}</td>
                                            {/* <td className="body-cell">{user.userId.phone}</td> */}
                                            <td className="body-cell">
                                                {moment(user.createdAt).format("DD-MM-YY")}
                                            </td>
                                            <td className="body-cell">{user.price} </td>
                                            <td className="body-cell">{user.description} </td>
                                            <td className="body-cell">{user.status} </td>
                                            {/* <td className="body-cell">
                                                <Switch
                                                    checked={user.status === "active"}
                                                    onChange={() => handleSwitchChange(index)}
                                                    style={{
                                                        backgroundColor:
                                                            user.status === "active" ? "#0096c7" : "#798593",
                                                    }}
                                                />
                                            </td> */}
                                            <td className="body-cell">
                                                <Link
                                                    to={`/offers/${user._id}`}
                                                    className="more-link clickable"
                                                >
                                                    More..
                                                </Link>
                                            </td>
                                            {/* <td className="body-cell">
                                                <Link
                                                    onClick={() => handleDeleteUser(user._id)}
                                                    className="delete-link clickable"
                                                >
                                                    Delete..
                                                </Link>
                                            </td> */}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>

                            <div className="pagination-container mt-4 mx-auto justify-between px-1">
                                <button onClick={handlePreviousPage} disabled={currentPage === 1}>
                                    &lt; Previous
                                </button>
                                &nbsp;
                                <span className="text-black">
                                    Page {currentPage} of {totalPages}
                                </span>
                                &nbsp;
                                <button
                                    onClick={handleNextPage}
                                    disabled={currentPage === totalPages}
                                >
                                    Next &gt;
                                </button>
                            </div>
                            {/* <div className="download-report">
                                <button className="download-link" onClick={handleDownloadCSV}>
                                    Download Report
                                </button>
                            </div> */}
                        </div>
                    ) : (
                        <div className="no-data-section">
                            <img src={noDataImage} alt="No data" className="no-data-image" />
                            <div className="no-data-title">No Offers to show right now!</div>
                            <div className="no-data-description">
                                We don't have any offer info to show here. Please add offers first
                                and then check their details here.
                            </div>
                        </div>
                    )}

                    {/* {isModalVisible && (
                        <div className="modal-overlay" onClick={toggleModal}>
                            <div className="modal-container" onClick={(e) => e.stopPropagation()}>
                                <UserModel
                                    closeModal={toggleModal}
                                    updateUserData={updateUserData}
                                />
                            </div>
                        </div>
                    )} */}
                </div>
            </div>
        </>
    )
}

export default Offers
