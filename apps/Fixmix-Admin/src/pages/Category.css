.filters {
  margin-top: 50px;
  margin-bottom: 20px;
}

.filter-button {
  margin-right: 10px;
  padding: 10px 20px;
  background-color: #333;
  color: #ffffff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.category-table {
  width: 100%;
  border-collapse: collapse;
}

.category-table th,
.category-table td {
  padding: 15px;
  text-align: left;
}

.category-table-container {
  border-radius: 10px;
  overflow: hidden;
}

.category-table th {
  color: #333333;
  background-color: #faf9f9;
  color: #000;
}

.category-table tr {
  background-color: #faf9f9;
  color: #000;
}

.category-table tr:nth-child(even) {
  background-color: #faf9f9;
  color: #000;
}

.category-table tr td {
  /* border-bottom: 1px solid #444; */
}

.switch {
  position: relative;
  display: inline-block;
  width: 34px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 12px;
  width: 12px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked+.slider {
  background-color: #4caf50;
}

input:checked+.slider:before {
  transform: translateX(14px);
}

.category-container {
  padding: 1rem;
  width: 100%;
  overflow-x: auto;
  /* Enable horizontal scroll if necessary */
  margin: 0 auto;
  /* Center the content */
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create-button {
  float: right;
  margin-bottom: 20px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: #000;
  color: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  position: relative;
}

.modal-close-button {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  color: #fff;
  font-size: 24px;
  cursor: pointer;
}

.modal-close-button:hover {
  color: #bbb;
}

.modal-content h2 {
  margin-bottom: 20px;
}

.modal-content input {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  border: 1px solid #555;
  background-color: #222;
  color: #fff;
}

.create-button-gradient {
  width: 100%;
  padding: 10px;
  background: linear-gradient(90deg, #f6d724 0%, #2c9547 100%);
  color: #000;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.create-button-gradient:hover {
  background: linear-gradient(90deg, #f6d724 0%, #2c9547 100%);
}

.create-button {
  float: right;
  align-self: right;
}

.more-button {
  color: #0096c7 !important;
  text-decoration: underline !important;
  padding: 0;
  height: auto;
  line-height: inherit;
  cursor: pointer;
}

.save-button {
  color: #5ff624 !important;
  text-decoration: underline !important;
  padding: 0;
  height: auto;
  line-height: inherit;
  cursor: pointer;
}

.more-button:hover {
  color: green !important;
  /* Change to a brighter yellow on hover */
}