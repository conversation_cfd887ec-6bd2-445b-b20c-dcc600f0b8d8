import { Layout, Descriptions, Avatar, Input, Button } from "antd";
import { MailOutlined, EyeOutlined } from "@ant-design/icons";
import React, { useState, useRef, useEffect } from "react";
import blankProfile from "./../assets/userIcon.webp";
import {
  changepassword,
  uploadAdminImage,
  updateImage,
  verifyToken,
  forgotPassword,
} from "../services/api";
import Swal from "sweetalert2";

const { Content } = Layout;

const UserProfile = () => {
  const [isVerified, setIsVerified] = useState(false);
  const [loading, setLoading] = useState(true);
  const [adminEmail, setAdminEmail] = useState("");
  const [avatarUrl, setAvatarUrl] = useState(blankProfile);
  const [showPasswordFields, setShowPasswordFields] = useState(false);
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const fileInputRef = useRef(null);
  useEffect(() => {
    const verifyUserToken = async () => {
      try {
        const response = await verifyToken();
        console.log("profiledata", response)
        if (response?.body) {
          setAdminEmail(response.body.admin.email);
          setIsVerified(true);
        }
      } catch (error) {
        console.error("Token verification failed:", error);
        setIsVerified(false);
      } finally {
        setLoading(false);
      }
    };

    verifyUserToken();

    // Load profile image from localStorage
    const storedImage = localStorage.getItem("profileImage");
    if (storedImage) {
      setAvatarUrl(storedImage);
    }
  }, []);

  const handleForgotPassword = async (adminEmail) => {
    try {
      const response = await forgotPassword(adminEmail); // Pass only the email
      if (response.status === 200) {
        Swal.fire({
          title: "Success!",
          text: "Password reset link has been sent to your email.",
          icon: "success",
          confirmButtonText: "Ok",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error forget password",
        text: error?.response?.data?.message || "Please try again later.",
        icon: "error",
        confirmButtonText: "Ok",
      });
    }
  }

  const handleChangePasswordClick = () => {
    setShowPasswordFields(true);
  };

  const handlePasswordChange = async () => {
    if (!oldPassword || !newPassword) {
      Swal.fire({
        title: "Error",
        text: "Both fields are required!",
        icon: "warning",
        confirmButtonText: "Ok",
      });
      return;
    }

    try {
      const response = await changepassword({ oldpass: oldPassword, newpass: newPassword });
      if (response.status === 200) {
        Swal.fire({
          title: "Success!",
          text: "Password has been changed.",
          icon: "success",
          confirmButtonText: "Ok",
        });
        setShowPasswordFields(false);
        setOldPassword("");
        setNewPassword("");
      }
    } catch (error) {
      Swal.fire({
        title: "Error changing password",
        text: error?.response?.data?.message || "Please try again later.",
        icon: "error",
        confirmButtonText: "Ok",
      });
    }
  };
  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      const fileUrl = URL.createObjectURL(file);
      setAvatarUrl(fileUrl);

      const formData = new FormData();
      formData.append("image", file);

      try {
        const response = await uploadAdminImage(formData);
        const imageUrl = response.body.imageUrl;
        localStorage.setItem("profileImage", imageUrl);
        await updateImage(imageUrl);
        window.location.reload();
      } catch (error) {
        console.error("Error uploading image:", error);
      }
    }
  };

  const handleAvatarClick = () => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you want to change profile picture?",
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes, proceed!",
      cancelButtonText: "No, cancel!",
    }).then((result) => {
      if (result.isConfirmed && fileInputRef.current) {
        fileInputRef.current.click();
      }
    });
  };

  return (
    <div className="p-6">
      <Layout style={{ minHeight: "100vh", backgroundColor: "white" }}>
        <div
          style={{
            backgroundColor: "#faf9f9",
            borderRadius: "8px",
            padding: "20px",
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
            position: "relative",
          }}
        >
          <div
            style={{
              width: "100%",
              height: "80px",
              background: "linear-gradient(90deg, #0096c7 0%, #4bd1fd 100%)",
              borderRadius: "8px 8px 0 0",
              position: "absolute",
              top: 0,
              left: 0,
            }}
          ></div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginTop: "40px",
              marginBottom: "40px",
            }}
          >
            <div className="flex items-center">
              <Avatar
                className="w-1/6 cursor-pointer"
                src={avatarUrl}
                size={150}
                style={{
                  border: "3px solid #1E1E1E",
                  marginRight: "20px",
                }}
                onClick={handleAvatarClick}
              />
              <input
                type="file"
                accept="image/*"
                ref={fileInputRef}
                style={{ display: "none" }}
                onChange={handleImageUpload}
              />
            </div>
            <div
              className="w-1/4"
              style={{
                backgroundColor: "#faf9f9",
                color: "#000",
                textAlign: "left",
              }}
            >
              <h2 style={{ marginBottom: "0" }}>{adminEmail}</h2>
              <p style={{ color: "#A9A9A9", marginTop: "8px" }}>Administrator</p>
            </div>
          </div>
          <div
            className="h-10 bg-[#e3e4e4]"
            style={{
              padding: "7px 10px",
              borderRadius: "8px",
            }}
          >
            <p style={{ color: "#000", marginBottom: "0" }}>
              Update your account preferences using the options below.
            </p>
          </div>
          <div style={{ marginTop: "20px" }}>
            <label
              style={{
                color: "#000",
                fontSize: "14px",
                display: "block",
                marginBottom: "5px",
              }}
            >
              Email ID
            </label>
            <Input
              value={adminEmail}
              suffix={<MailOutlined style={{ color: "#000000" }} />}
              style={{
                marginBottom: "20px",
                backgroundColor: "#e3e4e4",
                color: "#000",
                borderRadius: "8px",
                padding: "10px",
              }}
              disabled
            />
            {/* <label
              style={{
                color: "#000",
                fontSize: "14px",
                display: "block",
                marginBottom: "5px",
              }}
            >
              Password
            </label>
            <Input.Password
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              type="password"
              suffix={<EyeOutlined style={{ color: "#2e2e2e", fontSize: "16px" }} />}
              style={{
                marginBottom: "20px",
                backgroundColor: "#e3e4e4",
                color: "#000",
                borderRadius: "8px",
                padding: "10px",
              }}
            /> */}
          </div>
          <div className="my-4">
            <Button
              type="primary"
              style={{
                background: "linear-gradient(90deg, #0096c7 0%, #4bd1fd 100%)",
                borderColor: "transparent",
                color: "#F1F1F1",
                fontWeight: "bold",
                width: "20%",
                height: "8%",
                padding: "10px 0",
                borderRadius: "8px",
              }}
              onClick={() => handleForgotPassword(adminEmail)} // Use arrow function
            >
              Forgot Password
            </Button>
          </div>
          {!showPasswordFields ? (
            <Button
              type="primary"
              style={{
                background: "linear-gradient(90deg, #0096c7 0%, #4bd1fd 100%)",
                borderColor: "transparent",
                color: "#F1F1F1",
                fontWeight: "bold",
                width: "20%",
                height: "8%",
                padding: "10px 0",
                borderRadius: "8px",
              }}
              onClick={handleChangePasswordClick}
            >
              Change Password
            </Button>
          ) : (
            <div>
              <label style={{ color: "#000", fontSize: "14px" }}>Old Password</label>
              <Input.Password
                value={oldPassword}
                onChange={(e) => setOldPassword(e.target.value)}
                style={{ marginBottom: "20px", backgroundColor: "#e3e4e4", color: '#000' }}
              />
              <label style={{ color: "#000", fontSize: "14px" }}>New Password</label>
              <Input.Password
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                style={{ marginBottom: "20px", backgroundColor: "#e3e4e4", color: '#000' }}
              />
              <Button
                type="primary"
                style={{
                  background: "linear-gradient(90deg, #0096c7 0%, #4bd1fd 100%)",
                  borderColor: "transparent",
                  color: "#F1F1F1",
                  fontWeight: "bold",
                  width: "20%",
                  borderRadius: "8px",
                }}
                onClick={handlePasswordChange}
              >
                Submit
              </Button>
            </div>
          )}
        </div>
      </Layout >
    </div >
  );
};

export default UserProfile;