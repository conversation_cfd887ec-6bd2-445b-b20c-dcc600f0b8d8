import React from "react";
import { Layout, Table, Button, Space, Input } from "antd";
import Sidebar from "../components/Sidebar";
import TopBar from "../components/TopBar";

const { Content } = Layout;

const columns = [
  {
    title: "No.",
    dataIndex: "number",
    key: "number",
  },
  {
    title: "Order Name",
    dataIndex: "orderName",
    key: "orderName",
  },
  {
    title: "Buyer Name",
    dataIndex: "buyerName",
    key: "buyerName",
  },
  {
    title: "Seller Name",
    dataIndex: "sellerName",
    key: "sellerName",
  },
  {
    title: "Order Status",
    dataIndex: "status",
    key: "status",
  },
];

const data = [
  // Add your data here
];

const OrderHistory = () => (
  <Layout style={{ minHeight: "100vh" }}>
    <Sidebar />
    <Layout>
      <TopBar />
      <Content style={{ margin: "24px 16px 0" }}>
        <Space style={{ marginBottom: 16 }}>
          <Input.Search placeholder="Search here" style={{ width: 400 }} />
          <Button type="primary">Progress Orders</Button>
          <Button type="primary">Completed Orders</Button>
        </Space>
        <Table columns={columns} dataSource={data} />
      </Content>
    </Layout>
  </Layout>
);

export default OrderHistory;
