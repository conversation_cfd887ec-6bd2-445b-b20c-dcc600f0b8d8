.previous-button {
  position: absolute;
  left: 0;
}

.page-details {
  display: inline-block;
  /* Ensures the page details remain centered */
}

.next-button {
  position: absolute;
  right: 0;
}

.filters {
  margin-top: 50px;
  margin-bottom: 20px;
}

.filter-button {
  margin-right: 10px;
  padding: 10px 20px;
  background-color: #333;
  color: #ffffff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.vendor-payout-content {
  margin-top: 1rem;
}

.vendors-payout-table {
  border-collapse: collapse;
  background-color: #faf9f9 !important;
  width: fill;

}

.vendors-payout-table-cell {
  width: 100% !important;
  margin: 0 auto !important;
}

.vendors-payout-table th,
.vendors-payout-table td {
  padding: 15px;
  text-align: left;
}

.vendors-payout-table th {
  font-weight: bold;
  background-color: #faf9f9;
  color: #000;
}

.vendors-payout-table tr {
  background-color: #faf9f9;
  color: #000;
}

.vendors-payout-table tr:nth-child(even) {
  background-color: #faf9f9;
  color: #000;
}

/* .vendors-payout-table tr td {
  border-bottom: 1px solid #444;
} */

.switch {
  position: relative;
  display: inline-block;
  width: 34px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 12px;
  width: 12px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked+.slider {
  background-color: #4caf50;
}

input:checked+.slider:before {
  transform: translateX(14px);
}

.pagination {
  margin: 20px 0;
  margin-right: 93rem;
  text-align: center;
  color: #cccccc;
}

.download-report-button {
  padding: 10px 20px;
  background-color: #f1c40f00;
  color: #d0d31a;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  display: block;
  margin-left: 88rem;
}

.download-report {
  text-align: right;
  margin-top: 1rem;
}

.download-link {
  color: #fff;
  background: linear-gradient(90deg, #0096c7 0%, #4bd1fd 100%);
  text-decoration: none;
  font-weight: bold;
  padding: 10px;
  border-radius: 10px;
}