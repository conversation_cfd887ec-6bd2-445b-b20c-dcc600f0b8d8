.search-create-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.vendors-container {
  /* margin-left: 1rem;
  margin-top: 0.5rem; */
  padding: 1rem;
}

.dropdown-buttons {
  display: flex;
  gap: 1rem;
}

.dropdown-button {
  background: #2c2e2d;
  color: #ffffff;
  font-size: 16px;
  font-weight: 300;
  border: none;
  border-radius: 15px;
  padding: 0.9rem 1.3rem;
}

.create-button {
  background: linear-gradient(90deg, #0096c7 0%, #6ccaf0 100%);
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  border: none;
  border-radius: 10px;
  padding: 1.4rem 1.1rem;
  box-shadow: 0px 4px 201px rgba(0, 0, 0, 0.05);
}

.metrics-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.metric-card {
  background-color: #2c2e2d;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.metric-title {
  color: rgba(255, 255, 255, 0.5);
}

.metric-value {
  color: #f6d724;
  font-size: 2rem;
  font-weight: bold;
}

.no-data-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #faf9f9;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  height: 50vh;
}

.no-data-image {
  margin-bottom: 1rem;
}

.no-data-title {
  color: black;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.no-data-description {
  color: rgba(43, 42, 42, 0.5);
  text-align: center;
}

/* Table Styles */
.vendor-table-container {
  width: 100%;
  overflow-x: auto;
  /* Allow horizontal scrolling if needed */
  margin-top: 20px;
}

.vendor-table {
  width: 100%;
  max-width: 100%;
  /* Prevents the table from exceeding its container's width */
  border-collapse: collapse;
  background: #faf9f9;
  box-shadow: 2px 2px 8.3px -5px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
  overflow: hidden;
}

.vendor-table th,
.vendor-table td {
  padding: 15px;
  text-align: left;
  color: #000;
}

.vendor-table th {
  background: #faf9f9;
  border-bottom: none;
  border-top: 1px solid #444;
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  font-size: 16px;
}

.vendor-table thead th {
  background: #faf9f9;
  border-bottom: none;
  border-top: none;
  border-radius: 10px 10px 0 0;
}

.vendor-table tbody tr:nth-child(odd) {
  /* background: rgba(36, 38, 37, 0.5); */
}

.vendor-table tbody tr:nth-child(even) {
  /* background: rgba(29, 31, 30, 0.5); */
}

.more-link {
  color: #0096c7;
  cursor: pointer;
  text-decoration: underline;
}

.delete-link {
  color: #f62424;
  cursor: pointer;
  text-decoration: underline;
}

.table-container {
  position: relative;
}

.table-header,
.table-row {
  max-width: 100%;
  height: 65px;
  background: #faf9f9;
  box-shadow: 2px 2px 8.3px -5px rgba(0, 0, 0, 0.25);
  border-radius: 10px 10px 0 0;
}

.table-cell {
  padding: 10px;
}

.header-cell {
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #000;
}

.body-cell {
  font-family: "Poppins", sans-serif;
  font-weight: 300;
  font-size: 14px;
  color: #000;
}

/* Pagination Container */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
  padding: 10px;
}

/* Button Styles */
.pagination-container button {
  background-color: #333;
  border: none;
  color: #fff;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.pagination-container button:disabled {
  background-color: #555;
  color: #fff;
  cursor: not-allowed;
}

.pagination-container button:hover:not(:disabled) {
  background-color: #555;
  color: #fff;
}

/* Current Page Display */
.pagination-container span {
  font-size: 14px;
}

/* Similar CSS styles to maintain consistency */
.dropdown-buttons {
  display: flex;
  gap: 1rem;
}

.users-dropdown-button {
  color: #2c2e2d;
  background: #faf9f9;
  font-size: 16px;
  font-weight: 300;
  border: none;
  border-radius: 10px;
  padding: 0.9rem 1.3rem;
  cursor: pointer;
  position: relative;
}

.users-dropdown-content {
  display: none;
  position: absolute;
  color: #2c2e2d;
  background: #faf9f9;
  min-width: 180px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  z-index: 1;
  border-radius: 5px;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
}

.users-dropdown-button:hover .users-dropdown-content {
  display: block;
}

.users-dropdown-content a {
  color: #000;
  padding: 0.8rem 1rem;
  text-decoration: none;
  display: block;
  transition: background-color 0.2s ease;
}

.users-dropdown-content a:hover {
  background: #faf9f9;
  color: #999898;
}

.users-dropdown-content a.active {
  background: #0096c7;
  /* color: #ffffff; */
}

.download-report {
  text-align: right;
  margin-top: 1rem;
}

.download-link {
  color: #0096c7;
  text-decoration: none;
  font-weight: bold;
}