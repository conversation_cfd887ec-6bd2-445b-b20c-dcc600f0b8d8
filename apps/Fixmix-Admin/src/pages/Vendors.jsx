import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { <PERSON><PERSON>, Switch } from "antd";
import noDataImage from "../assets/icons/nodataimage.svg";
import DashboardMetrics from "../components/VendorMetrics";
import CreateVendorModal from "../components/CreateVendorModal";
import "./Vendors.css";
import moment from "moment";
import TopBar from "./../components/TopBar";
import { ShimmerDiv, ShimmerTable } from "shimmer-effects-react";
import { fetchVendors, setSearchText, toggleVendorStatus } from "../slices/vendorSlice";

const Vendors = () => {
  const dispatch = useDispatch();
  const {
    data: vendors,
    loading,
    error,
    totalVendors,
    totalPages,
    currentPage: currentPageFromStore,
    limit: limitFromStore,
    searchText: searchTextFromStore,
  } = useSelector((state) => state.vendors);

  // Local state for filters, search, and pagination
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchText, setSearchTextLocal] = useState(searchTextFromStore || "");
  const [filter, setFilter] = useState("all");
  const [limit, setLimit] = useState(10); // Default limit is 10
  const [dateFilter, setDateFilter] = useState("all");
  const [sortConfig, setSortConfig] = useState({ key: "createdAt", direction: "asc" });
  const [currentPage, setCurrentPage] = useState(1);

  // Effect to fetch vendors when filters, search, or pagination changes
  useEffect(() => {
    const params = {
      isActive: filter === "all" ? undefined : filter === "active",
      page: currentPage,
      limit,
      search: searchText,
      sortBy: sortConfig.key,
      order: sortConfig.direction,
      ...getDateRangeParams(dateFilter), // Add date range params if applicable
    };
    dispatch(fetchVendors(params));
  }, [searchText, filter, limit, dateFilter, sortConfig, currentPage, dispatch]);

  // Helper function to calculate date range params
  const getDateRangeParams = (range) => {
    const now = moment();
    switch (range) {
      case "today":
        return {
          startDate: now.startOf("day").toISOString(),
          endDate: now.endOf("day").toISOString(),
        };
      case "week":
        return {
          startDate: now.startOf("week").toISOString(),
          endDate: now.endOf("week").toISOString(),
        };
      case "month":
        return {
          startDate: now.startOf("month").toISOString(),
          endDate: now.endOf("month").toISOString(),
        };
      case "year":
        return {
          startDate: now.startOf("year").toISOString(),
          endDate: now.endOf("year").toISOString(),
        };
      default:
        return {};
    }
  };

  // Handle CSV download
  const convertToCSV = (data) => {
    const headers = ["No.", "Full Name", "Email ID", "Mobile No.", "Category", "Date", "isSubscribed", "Status"];
    const csvRows = [headers.join(",")];

    data.forEach((vendor, index) => {
      const row = [
        index + 1,
        vendor.name || "N/A",
        vendor.email || "N/A",
        vendor.phone || "N/A",
        vendor.categoryIds?.join(", ") || "N/A",
        moment(vendor.createdAt).format("DD-MM-YY"),
        vendor.isSubscribed ? "Yes" : "No",
        vendor.status || "N/A",
      ];
      csvRows.push(row.join(","));
    });

    return csvRows.join("\n");
  };

  const handleDownloadCSV = () => {
    const csvData = convertToCSV(vendors);
    const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `vendors_report.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle sorting
  const sortVendors = (key) => {
    const direction = sortConfig.key === key && sortConfig.direction === "asc" ? "desc" : "asc";
    setSortConfig({ key, direction });
  };

  // Handle pagination
  const handlePreviousPage = () => setCurrentPage((prev) => Math.max(prev - 1, 1));
  const handleNextPage = () => setCurrentPage((prev) => Math.min(prev + 1, totalPages));

  // Handle search
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setCurrentPage(1); // Reset page to 1 whenever filter changes
    setSearchTextLocal(value);
    dispatch(setSearchText(value)); // Update Redux store
  };
  // Handle status toggle
  const handleSwitchChange = ({ id, isActive }) => {
    dispatch(toggleVendorStatus({ id, isActive }));
  };

  // Metrics calculations
  const totalRevenue = vendors.reduce((sum, vendor) => sum + (vendor.totalEarnings || 0), 0).toFixed(0);

  const renderShimmer = () => {
    return Array.from({ length: 10 }).map((_, index) => (
      <tr key={index} className="shimmer-row">
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer shimmer-circle"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
        <td className="body-cell"><div className="shimmer"></div></td>
      </tr>
    ));
  };
  const shimmerStyles = `
        .shimmer-row {
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
            background-size: 800px 100%;
        }
        
        .shimmer {
            height: 20px;
            background: #f0f0f0;
            border-radius: 4px;
            margin: 4px 0;
        }
        
        .shimmer-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }
        
        @keyframes shimmer {
            0% { background-position: -400px 0; }
            100% { background-position: 400px 0; }
        }
    `;

  return (
    <>
      <TopBar
        searchPlaceholder="Search Vendor Here"
        isSearchEnable={false}
        onSearch={handleSearchChange}
      />      <div className="p-6">
        <div className="vendors-container">
          {/* Metrics Section */}
          <div className="metrics-section">
            <DashboardMetrics title="Total Vendors" count={totalVendors} />
            <DashboardMetrics title="Total Revenue" count={`₹ ${totalRevenue}`} />
          </div>

          {/* Filters and Create Button */}
          <div className="search-create-section">
            <div className="dropdown-buttons">
              <div className="users-dropdown">
                <button className="users-dropdown-button">
                  {filter === "all" ? "Vendor Status" : filter === "active" ? "Activated Vendors" : "Deactivated Vendors"}
                  <div className="users-dropdown-content">
                    <a href="#" className={filter === "all" ? "active" : ""} onClick={() => setFilter("all")}>All Vendors</a>
                    <a href="#" className={filter === "active" ? "active" : ""} onClick={() => setFilter("active")}>Activated Vendors</a>
                    <a href="#" className={filter === "deactive" ? "active" : ""} onClick={() => setFilter("deactive")}>Deactivated Vendors</a>
                  </div>
                </button>
              </div>
              <div className="users-dropdown">
                <button className="users-dropdown-button">
                  {dateFilter === "all" ? "Date Filter" : dateFilter === "today" ? "Today" : dateFilter === "week" ? "This Week" : dateFilter === "month" ? "This Month" : "This Year"}
                  <div className="users-dropdown-content">
                    <a href="#" className={dateFilter === "all" ? "active" : ""} onClick={() => setDateFilter("all")}>All Dates</a>
                    <a href="#" className={dateFilter === "today" ? "active" : ""} onClick={() => setDateFilter("today")}>Today</a>
                    <a href="#" className={dateFilter === "week" ? "active" : ""} onClick={() => setDateFilter("week")}>This Week</a>
                    <a href="#" className={dateFilter === "month" ? "active" : ""} onClick={() => setDateFilter("month")}>This Month</a>
                    <a href="#" className={dateFilter === "year" ? "active" : ""} onClick={() => setDateFilter("year")}>This Year</a>
                  </div>
                </button>
              </div>
              <div className='flex text-black'>
                <label htmlFor="limit" className='text-black w-40 flex items-center justify-center'>Items per page: </label>
                <select
                  id="limit"
                  className='w-12'
                  value={limit}
                  onChange={(e) => setLimit(Number(e.target.value))}
                >
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                </select>

                {/* Your existing UI components here */}
              </div>
            </div>
            <Button type="primary" className="create-button" onClick={() => setIsModalOpen(true)}>
              Create New Vendor
            </Button>
          </div>

          {loading ? (
            <div className="user-table-container">
              <table className="user-table">

                <tbody>
                  {renderShimmer()}
                </tbody>
              </table>
            </div>
          ) : error ? (
            <div className="error-message">{error}</div>
          ) : vendors.length > 0 ? (
            <div className="vendor-table-container">
              <table className="vendor-table">
                <thead>
                  <tr>
                    <th className="header-cell">No.</th>
                    <th className="header-cell clickable" onClick={() => sortVendors("name")}>
                      Full Name {sortConfig.key === "name" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                    </th>
                    <th className="header-cell clickable" onClick={() => sortVendors("email")}>
                      Email ID {sortConfig.key === "email" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                    </th>
                    <th className="header-cell">Mobile No.</th>
                    <th className="header-cell">Company</th>
                    <th className="header-cell">Orders</th>
                    <th className="header-cell clickable" onClick={() => sortVendors("createdAt")}>
                      Date {sortConfig.key === "createdAt" && (sortConfig.direction === "asc" ? "↑" : "↓")}
                    </th>
                    <th className="header-cell">Earning</th>
                    <th className="header-cell">Activate/Deactivate</th>
                    <th className="header-cell">More</th>
                  </tr>
                </thead>
                <tbody>
                  {vendors.map((vendor, index) => (
                    <tr key={vendor._id}>
                      <td className="body-cell">{(currentPage - 1) * limit + index + 1}</td>
                      <td className="body-cell">{vendor.name}</td>
                      <td className="body-cell">{vendor.email}</td>
                      <td className="body-cell">{vendor.phone}</td>
                      <td className="body-cell">{vendor.companyName}</td>
                      <td className="body-cell">{vendor.totalCompletedOrders}</td>
                      <td className="body-cell">{moment(vendor.createdAt).format("DD-MM-YYYY")}</td>
                      <td className="body-cell">{vendor.totalEarnings}</td>
                      <td className="body-cell">
                        <Switch
                          checked={vendor.isActive === true}
                          onChange={() => handleSwitchChange({ id: vendor._id, isActive: vendor.isActive })}
                          style={{ backgroundColor: vendor.isActive ? "#0096c7" : "#798593" }}
                        />
                      </td>
                      <td className="body-cell">
                        <Link to={`/vendors/${vendor._id}`} className="more-link">More..</Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Pagination */}
              <div className="pagination-container mt-4 mx-auto justify-between px-1">
                <button onClick={handlePreviousPage} disabled={currentPage === 1}>&lt; Previous</button>
                <span className="text-black">Page {currentPage} of {totalPages}</span>
                <button onClick={handleNextPage} disabled={currentPage === totalPages}>Next &gt;</button>
              </div>
              {/* <div className="download-report">
                <button className="download-link" onClick={handleDownloadCSV}>Download Report</button>
              </div> */}
            </div>
          ) : (
            <div className="no-data-section">
              <img src={noDataImage} alt="No data" className="no-data-image" />
              <div className="no-data-title">No Vendors to show right now!</div>
              <div className="no-data-description">We don't have any vendor's info to show here. Please add vendors first and then check their details here.</div>
            </div>
          )}

          {/* Modal for creating new vendor */}
          {isModalOpen && (
            <div className="modal-overlay" onClick={() => setIsModalOpen(false)}>
              <div className="modal-container" onClick={(e) => e.stopPropagation()}>
                <CreateVendorModal closeModal={() => setIsModalOpen(false)} updateVendorData={() => dispatch(fetchVendors())} />
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Vendors;