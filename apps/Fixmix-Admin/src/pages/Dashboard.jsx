// Dashboard.js
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import DashboardMetrics from "../components/DashboardMetrics";
import BusinessCategoryTable from "../components/BusinessCategoryTable";
import TotalSubscribers from "../components/TotalSubscribers";
import DownloadReports from "../components/DownloadReports";
import { ShimmerDiv } from "shimmer-effects-react";
import TopBar from "../components/TopBar";
import { fetchDashboardData } from "../slices/dashboardSlice";

const Dashboard = () => {
  const dispatch = useDispatch();
  const { data, loading, error } = useSelector((state) => state.dashboard);

  useEffect(() => {
    dispatch(fetchDashboardData());
  }, [dispatch]);

  if (loading) {
    return (
      <div className="pt-5 pr-5 pl-5 grid gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 items-center justify-between">
        {[...Array(8)].map((_, i) => (
          <ShimmerDiv key={i} mode="light" height={150} width={300} border={0} rounded={1} />
        ))}
      </div>
    );
  }

  return (
    <>
      <TopBar searchPlaceholder="Search Users Here" isSearchEnable={true} />
      {/* {error && (
        <div className="p-5 text-red-500">
          Error: {error} - Showing dummy data
        </div>
      )} */}
      <div className="ml-0 mt-5">
        <div className="grid lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 px-5 pt-1">
          <DashboardMetrics title="Total Orders" count={data.ordersAvailable || ""} />
          <DashboardMetrics title="Total Offers" count={data.offersAvailable || ""} />
          <DashboardMetrics title="Total Categories" count={data.categoriesAvailable || ""} />
          <DashboardMetrics title="Total Subcategories" count={data.subcategoriesAvailable || ""} />
          <DashboardMetrics title="Total Queries" count={data.queriesCount || ""} />
          <DashboardMetrics title="Total Users" count={data.usersCount || ""} />
          <DashboardMetrics title="Total Vendors" count={data.vendorsCount || ""} />
        </div>
        <div className="grid lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 p-5">
          <div className="lg:col-span-4 md:col-span-3 sm:col-span-2 col-span-1">
            <BusinessCategoryTable />
          </div>
          {/* <div className="lg:col-span-1 md:col-span-1 col-span-1 space-y-6">
            <TotalSubscribers />
            <DownloadReports />
          </div> */}
        </div>
      </div>
    </>
  );
};

export default Dashboard;
