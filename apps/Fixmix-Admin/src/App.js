import React from "react";
import { Route, Routes } from "react-router-dom";
import Dashboard from "./pages/Dashboard";
import Users from "./pages/Users";
import Vendors from "./pages/Vendors";
import Notifications from "./pages/Notifications";
import Orders from "./pages/Orders";
import Category from "./pages/Category";
import SubCategoryMore from "./components/SubCategoryMore";
import UserProfile from "./pages/UserProfile";
import Login from "./components/Login";
import ForgotPassword from "./components/ForgotPassword";
import UserDetail from "./components/UserDetail";
import Layout from "./components/Layout";
import PrivateRoute from "./PrivateRoute";
import "./index.css";
import "./styles/global.css";
import VendorDetails from "./components/VendorDetails";
import VendorPayout from "./pages/VendorsPayout";
import Setting from "./pages/Setting";
import Query from "./pages/Query";
import Offers from "./pages/Offers";
import QueryDetails from "./components/QueryDetails";
import OfferDetails from "./components/OfferDetails";
import CategoryDetails from "./pages/CategoryDetails";
import OrderDetails from "./pages/OrderDetails";

function App() {
  return (
    <Routes>
      <Route path="/login" element={<Login />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route element={<Layout />}>
        <Route
          path="/"
          element={
            <PrivateRoute>
              <Dashboard />
            </PrivateRoute>
          }
        />
        <Route
          path="/users"
          element={
            <PrivateRoute>
              <Users />
            </PrivateRoute>
          }
        />
        <Route
          path="/users/:userId"
          element={
            <PrivateRoute>
              <UserDetail />
            </PrivateRoute>
          }
        />
        <Route
          path="/vendors"
          element={
            <PrivateRoute>
              <Vendors />
            </PrivateRoute>
          }
        />
        <Route
          path="/category"
          element={
            <PrivateRoute>
              <Category />
            </PrivateRoute>
          }
        />
        <Route
          path="/category/:categoryId"
          element={
            <PrivateRoute>
              <CategoryDetails />
            </PrivateRoute>
          }
        />
        {/* <Route
          path="SubCategoryMore/:categoryId"
          element={
            <PrivateRoute>
              <SubCategoryMore />
            </PrivateRoute>
          }
        /> */}
        <Route
          path="/vendors/:vendorId"
          element={
            <PrivateRoute>
              <VendorDetails />
            </PrivateRoute>
          }
        />
        <Route
          path="/transactions"
          element={
            <PrivateRoute>
              <VendorPayout />
            </PrivateRoute>
          }
        />
        <Route
          path="/notifications"
          element={
            <PrivateRoute>
              <Notifications />
            </PrivateRoute>
          }
        />
        <Route
          path="/orders"
          element={
            <PrivateRoute>
              <Orders />
            </PrivateRoute>
          }
        />
        <Route
          path="/orders/:orderId"
          element={
            <PrivateRoute>
              <OrderDetails />
            </PrivateRoute>
          }
        />
        <Route
          path="/profile"
          element={
            <PrivateRoute>
              <UserProfile />
            </PrivateRoute>
          }
        />
        <Route
          path="/offers"
          element={
            <PrivateRoute>
              <Offers />
            </PrivateRoute>
          }
        />
        <Route
          path="/offers/:Id"
          element={
            <PrivateRoute>
              <OfferDetails />
            </PrivateRoute>
          }
        />
        <Route
          path="/queries"
          element={
            <PrivateRoute>
              <Query />
            </PrivateRoute>
          }
        />
        <Route
          path="/queries/:Id"
          element={
            <PrivateRoute>
              <QueryDetails />
            </PrivateRoute>
          }
        />
      </Route>
    </Routes>
  );
}

export default App;
