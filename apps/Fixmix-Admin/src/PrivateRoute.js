// import React from "react";
// import { useSelector } from "react-redux";
// import { Navigate } from "react-router-dom";
// import {verifyToken} from "./services/api";

// const isTokenVerified =async()=>{
// try{
//   await dispatch(verifyToken()).unwrap();
// return true;
// }catch(err){
//   return false;
// }
// }
// const PrivateRoute = ({ children }) => {
//   const { isAuthenticated } = useSelector((state) => state.auth);

//   return isAuthenticated ? children : <Navigate to="/login" />;
// };

// export default PrivateRoute;

import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Navigate, useLocation } from "react-router-dom";
import { verifyToken } from "./services/api";

const PrivateRoute = ({ children }) => {
  const { isAuthenticated } = useSelector((state) => state.auth);
  const location = useLocation();
  const [isVerified, setIsVerified] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const verifyUserToken = async () => {
      try {
        await verifyToken(); // Call the API to verify the token
        setIsVerified(true);
      } catch (error) {
        setIsVerified(false);
      } finally {
        setLoading(false); // Set loading to false after token verification
      }
    };

    verifyUserToken();
  }, []);

  if (loading) {
    return <div>Loading...</div>; // Optionally display a loading state
  }

  // Navigate based on token verification and authentication state
  if (!isVerified || !isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} />;
  }

  return children;
};

export default PrivateRoute;
