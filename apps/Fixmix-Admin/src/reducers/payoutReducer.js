const initialState = {
    payout: [],
    loading: false,
    error: null,
};

const payoutReducer = (state = initialState, action) => {
    switch (action.type) {
        case "FETCH_PAYOUTS_REQUEST":
            return { ...state, loading: true };
        case "FETCH_PAYOUTS_SUCCESS":
            return { ...state, loading: false, payouts: action.payload };
        case "FETCH_PAYOUTS_FAILURE":
            return { ...state, loading: false, error: action.payload };
        default:
            return state;
    }
};

export default payoutReducer;
