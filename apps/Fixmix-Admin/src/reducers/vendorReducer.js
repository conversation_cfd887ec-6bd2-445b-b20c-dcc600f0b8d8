const initialState = {
  vendors: [],
  loading: false,
  error: null,
};

const vendorReducer = (state = initialState, action) => {
  switch (action.type) {
    case "FETCH_VENDORS_REQUEST":
      return { ...state, loading: true };
    case "FETCH_VENDORS_SUCCESS":
      return { ...state, loading: false, vendors: action.payload };
    case "FETCH_VENDORS_FAILURE":
      return { ...state, loading: false, error: action.payload };
    default:
      return state;
  }
};

export default vendorReducer;
