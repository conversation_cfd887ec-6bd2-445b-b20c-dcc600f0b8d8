import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./slices/authSlice";
import usersReducer from "./slices/userSlice";
import vendorsReducer from "./slices/vendorSlice";
import payoutsReducer from "./slices/payOut";
import orderReducer from "./slices/orderSlice";
import subscriberReducer from "./slices/subscriberSlice";
import notificationsReducer from "./slices/notificationsSlice";
import dashboardReducer from './slices/dashboardSlice';
import queryReducer from './slices/querySlice'
import offerReducer from './slices/offerSlice'
import categoryReducer from './slices/categorySlice'
import transactionsReducer from './slices/transactionsSlice'

const store = configureStore({
  reducer: {
    auth: authReducer,
    users: usersReducer,
    vendors: vendorsReducer,
    payouts: payoutsReducer,
    orders: orderReducer,
    subscribers: subscriberReducer,
    notifications: notificationsReducer,
    dashboard: dashboardReducer,
    query: queryReducer,
    categories: categoryReducer,
    offer: offerReducer,
    transactions: transactionsReducer
  },
});

export default store;
