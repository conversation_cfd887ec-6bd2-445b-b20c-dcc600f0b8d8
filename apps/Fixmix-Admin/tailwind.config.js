// tailwind.config.js
module.exports = {
  purge: ["./src/**/*.{js,jsx,ts,tsx}", "./public/index.html"],
  darkMode: false, // or 'media' or 'class'
  theme: {
    extend: {
      lineHeight: {
        "extra-loose": "2.5",
        12: "3rem",
      },
      colors: {
        primary: "#0096c7",
        secondary: "#faf9f9",
        accent: "#F6D724",
        "accent-light": "rgba(255, 255, 255, 0.5)",
        "secondary-light": "rgba(36, 38, 37, 0.5)",
        "accent-dark": "#373938",
        "light-gray": "#9D9D9D",
        "accent-green": "#34A853",
        yellow: "#F6D724",
        "text-light": "#FFFFFF",
        "text-muted": "#9D9D9D",
        "text-highlight": "#F6D724",
      },
      maxWidth: {
        "6xl": "1440px",
      },
      fontFamily: {
        poppins: ["Poppins", "sans-serif"],
        manrope: ["Manrope", "sans-serif"],
      },
      spacing: {
        72: "18rem",
        84: "21rem",
        96: "24rem",
      },
      borderRadius: {
        lg: "20px",
        md: "15px",
        sm: "5px",
        "2xl": "1.5rem",
        "3xl": "2rem",
      },
      boxShadow: {
        custom: "2px 2px 8.3px -5px rgba(0, 0, 0, 0.25)",
      },
      transitionProperty: {
        width: "width",
      },
    },
    screens: {
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1536px",
    },
  },
  variants: {
    extend: {},
  },
  plugins: [],
};
