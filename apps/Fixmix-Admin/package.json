{"name": "admin", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.4.0", "@craco/craco": "^7.1.0", "@reduxjs/toolkit": "^2.2.7", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "antd": "^5.19.4", "assert": "^2.1.0", "autoprefixer": "^10.4.19", "axios": "^1.7.3", "browserify-zlib": "^0.2.0", "https-browserify": "^1.0.0", "moment": "^2.30.1", "postcss": "^8.4.40", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.2.1", "react-redux": "^9.1.2", "react-router-dom": "^6.26.1", "react-scripts": "^5.0.1", "redux": "^5.0.1", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^3.1.0", "shimmer-effects-react": "^1.0.4", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "sweetalert2": "^11.14.1", "url": "^0.11.4", "util": "^0.12.5", "web-vitals": "^4.2.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.7"}}