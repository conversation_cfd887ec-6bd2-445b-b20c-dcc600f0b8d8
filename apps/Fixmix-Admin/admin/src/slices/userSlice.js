import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  data: [
    {
      id: 1,
      fullName: "<PERSON>",
      email: "jays<PERSON><PERSON>@gmail.com",
      isActive: true,
    },
    {
      id: 2,
      fullName: "<PERSON><PERSON>",
      email: "<EMAIL>",
      isActive: true,
    },
    {
      id: 3,
      fullName: "<PERSON><PERSON><PERSON>",
      email: "shaili<PERSON><EMAIL>",
      isActive: true,
    },
    {
      id: 4,
      fullName: "Aarushi Vaidya",
      email: "<EMAIL>",
      isActive: true,
    },
    {
      id: 5,
      fullName: "Mann<PERSON> Patel",
      email: "<EMAIL>",
      isActive: false,
    },
    {
      id: 6,
      fullName: "<PERSON><PERSON><PERSON> Sindhvani",
      email: "<EMAIL>",
      isActive: false,
    },
    {
      id: 7,
      fullName: "<PERSON><PERSON>",
      email: "<EMAIL>",
      isActive: true,
    },
    {
      id: 8,
      fullName: "<PERSON> <PERSON><PERSON>",
      email: "<EMAIL>",
      isActive: true,
    },
    {
      id: 9,
      fullName: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      isActive: false,
    },
  ],
};

const usersSlice = createSlice({
  name: "users",
  // initialState,
  reducers: {
    setUsers(state, action) {
      state.data = action.payload;
    },
  },
});

export const { setUsers } = usersSlice.actions;
export default usersSlice.reducer;
